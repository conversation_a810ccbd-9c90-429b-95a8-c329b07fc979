export const CUSTOMER_UPDATE_TRANSLATIONS = {
    title: "Update Customer",
    email: "Email",
    street_line_one: "Street Line 1",
    street_line_two: "Street Line 2",
    city: "City",
    region: "Region",
    country: "Country",
    post_code: "Post Code",
    reference: "Reference",
    company_name: "Company Name",
    external_reference: "External Reference",
    advance: "advance",
    submit_btn: "Updated",
    show_advanced: "Advanced",
    success_message: "Successfully update customer",
    address_title: "Address",
    tax_number: "Tax Number",
    standard_tax_rate: "Standard Tax Rate",
    locale: "Locale",
    marketing_opt_in: "Marketing Opt In",
    error: {
        not_found: "No such customer found",
        unknown: "An unknown error has occurred"
    },
    billing_type: "Billing Type",
    billing_type_card: "Card",
    billing_type_invoice: "Invoice",
    type: "Customer Type",
    type_business: "Business",
    type_individual: "Individual",
    invoice_format: "Invoice Format",
    metadata: {
        title: "<PERSON>ada<PERSON>",
        name: "Name",
        value: "Value",
        no_values: "No metadata values",
        add: "Add Metadata",
    },
    help_info: {
        email: "The email for the customer where the invoices are to go",
        locale: "The locale to be used for language",
        company_name: "The name of the company",
        street_line_one: "The first line of the street billing address",
        street_line_two: "The second line of the street billing address",
        city: "The city for the billing address",
        region: "The region/state for the billing address",
        country: "The customer's billing country - ISO 3166-1 alpha-2 country code.",
        post_code: "The post code for the billing address",
        reference: "Your internal reference for the customer",
        billing_type: "How the customer should be billed. Card means payments will be automatic via a card that is registered. Invoice means they receive an invoice and pay manually",
        external_reference: "The reference for the customer that is used by the payment provider. Leave empty unless you're extremely confident you have the correct reference.",
        tax_number: 'The tax number for the customer',
        standard_tax_rate: "The tax rate to be applied for the customer for everything but digital services",
        type: "If the customer is a business or individual",
        invoice_format: "The format that should be used when creating and delivering an invoice",
        marketing_opt_in: "If the customer has opted in to marketing emails. This affects newsletter integrations.",
    }
}
