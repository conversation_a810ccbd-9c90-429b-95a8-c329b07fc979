security:
    access_decision_manager:
        strategy: unanimous
        allow_if_all_abstain: false
    providers:
        parthenon:
            id: Parthenon\User\Security\UserProvider
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        api:
            pattern: ^/api/
            custom_authenticators:
                - <PERSON><PERSON><PERSON><PERSON>\Security\ApiKeyAuthenticator
        main:
            pattern: ^/app/
            stateless: false
            lazy: true
            provider: parthenon
            remember_me:
                secret: '%kernel.secret%'
                always_remember_me: true
                lifetime: 2592000
                name: REMEMBERME
            logout:
                path: parthenon_user_logout
            json_login:
                check_path: /app/authenticate
            user_checker: Parthenon\User\Security\UserChecker\UserCheckerObserver
    password_hashers:
        Parthenon\User\Entity\UserInterface:
            algorithm: bcrypt

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/app/(authenticate|login|logout), roles: PUBLIC_ACCESS }
        - { path: ^/site/user/(signup|password|reset|confirm), roles: PUBLIC_ACCESS }
        - { path: ^/app/user/(signup|password|reset|confirm), roles: PUBLIC_ACCESS }
        - { path: ^/site/, roles: ROLE_USER }
        - { path: ^/app/, roles: ROLE_USER }
    role_hierarchy:
        ROLE_ADMIN: ROLE_DEVELOPER
        ROLE_DEVELOPER: ROLE_ACCOUNT_MANAGER
        ROLE_ACCOUNT_MANAGER: ROLE_CUSTOMER_SUPPORT
        ROLE_CUSTOMER_SUPPORT: ROLE_USER

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
