export const PRODUCT_UPDATE_TRANSLATIONS = {
    title: "Update Product",
    name: "Name",
    external_reference: "External Reference",
    advance: "advance",
    submit_btn: "Updated",
    show_advanced: "Advanced",
    success_message: "Successfully update product",
    address_title: "Address",
    error: {
        not_found: "No such product found",
        unknown: "An unknown error has occurred"
    },
    tax_type: "Tax Type",
    tax_types: {
        digital_services: "Digital Services",
        digital_goods: "Digital Goods",
        physical: "Physical Goods/Services"
    },
    tax_rate: "Tax Rate",
    help_info: {
        name: "The name of the product",
        external_reference: "The reference for the product that is used by the payment provider. Leave empty unless you're extremely confident you have the correct reference.",
        tax_type: "This is to help with taxing correctly. Physical goods and services are taxed differently from digital goods. And in some countries they have a digital services tax.",
        tax_rate: "The tax rate that is to be used for this product. It will override other tax rates."
    }
}