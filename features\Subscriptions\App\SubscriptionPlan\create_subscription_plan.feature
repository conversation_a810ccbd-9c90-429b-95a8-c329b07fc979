Feature: Plan Creation
  In order to charge customers for plan
  As an APP user
  I need to create a plan for a product

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
      | Product Two | prod_jf9j542       |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |

  Scenario: Create a Subscription Plan
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a Subscription Plan for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    Then there should be a subscription plan called "Test Plan"
    Then the subscription plan "Test Plan" should have a feature "Feature One"
    Then the subscription plan "Test Plan" should have a limit "Feature Two" with a limit of 10

  Scenario: Create a Subscription Plan fails used code name
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan  |
      | Public     | True       |
      | Per Seat   | False      |
      | User Count | 10         |
      | Code Name  | test_world |
    When I create a Subscription Plan for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan 2 |
      | Public     | True        |
      | Per Seat   | False       |
      | User Count | 10          |
      | Code Name  | test_world  |
    Then there should not be a subscription plan called "Test Plan 2"
    And there should be an error for "codeName"

  Scenario: Create a Subscription Plan use code name
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan  |
      | Public     | True       |
      | Per Seat   | False      |
      | User Count | 10         |
      | Code Name  | test       |
    When I create a Subscription Plan for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan 2 |
      | Public     | True        |
      | Per Seat   | False       |
      | User Count | 10          |
      | Code Name  | test_world  |
    Then there should be a subscription plan called "Test Plan 2"
    Then the subscription plan "Test Plan 2" should have the code name "test_world"