Feature: Generate new invoices with subsequential

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
      | Product Two | prod_jf9j542       |
    And there are the following tax types:
      | Name             | Physical |
      | Digital Goods    | False    |
      | Digital Services | False    |
      | Physical         | True     |
    And that the following countries exist:
      | Name           | ISO Code | Threshold | Currency | In EU |
      | United Kingdom | GB       | 1770      | GBP      | False |
      | United States  | US       | 0         | USD      | False |
      | Germany        | DE       | 0         | EUR      | True  |
    And the following country tax rules exist:
      | Country        | Tax Type      | Tax Rate | Valid From |
      | United States  | Digital Goods | 17.5     | -10 days   |
      | United Kingdom | Digital Goods | 17.5     | -10 days   |
      | Germany        | Digital Goods | 17.5     | -10 days   |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Two |
      | Public     | True     |
      | Per Seat   | False    |
      | User Count | 10       |
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      | Billing Type | Payment Reference | Tax Number |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   | invoice      | null              | FJDKSLfjdf |
      | <EMAIL>   | UK      | cust_dfugfdu       | Customer Two   | card         | ref_valid         | ssdfds     |
      | <EMAIL> | UK      | cust_mlklfdu       | Customer Three | card         | ref_valid         | gfdgsfd    |
      | <EMAIL>  | UK      | cust_dkkoadu       | Customer Four  | card         | ref_fails         | 35435 43   |
      | <EMAIL>  | UK      | cust_ddsjfu        | Customer Five  | card         | ref_valid         | dfadf      |
      | <EMAIL>   | UK      | cust_jliujoi       | Customer Six   | card         | ref_fails         | fdsafd     |


  Scenario:
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
    And stripe billing is enabled
    And the following credit transactions exist:
      | Customer                 | Type   | Amount | Currency |
      | <EMAIL> | credit | 1100  | USD      |
    And the invoice number generation is subsequential with the count of 13
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And the latest invoice for "<EMAIL>" will have the invoice number 14
    Then the invoice subsequential number is 14
