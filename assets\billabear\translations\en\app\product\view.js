export const PRODUCT_VIEW_TRANSLATIONS = {
    title: 'View Product Details',
    update: 'Update',
    error: {
        not_found: "No such product found",
        unknown: "An unknown error has occurred"
    },
    main: {
        title: "Main Details",
        name: "Name",
        physical: "Physical",
        tax_rate: "Tax Rate",
        tax_type: "Tax Type",
    },
    price: {
        title: "Prices",
        create: "Create New Price",
        no_prices: "There are currently no prices",
        hide: "Make price private",
        show: "Make price public",
        list: {
            amount: "Amount",
            currency: "Currency",
            recurring: "Recurring Payment",
            schedule: "Payment Schedule",
            including_tax: "Price Includes Tax",
            public: "Public Price",
            usage: "Usage"
        }
    },
    subscription_plan: {
        title: "Subscription Plans",
        create: "Create new Plan",
        no_subscription_plans: "There are currently no subscription plans",
        view: "View",
        list: {
            name: "Name",
            external_reference: "External Reference",
            code_name: "Code Name",
        }
    }
}
