export const SYSTEM_INTEGRATIONS_SLACK_NOTIFICATIONS_CREATE_TRANSLATIONS = {
    title: "Create Slack Notification",
    fields: {
        webhook: "Webhook",
        event: "Event",
        template: "Template"
    },
    help_info: {
        event: "The event that should trigger the notification",
        webhook: "The slack webhook to be used for the notification",
        template: "The template that is to be used when sending notification. <a href=\"https://docs.billabear.com/user/integration/slack\" target=\"_blank\">Variables can be found here</a>"
    },
    save_btn: "Save",
}
