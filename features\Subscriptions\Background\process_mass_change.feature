Feature: Create Subscription Mass Change

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
      | Product Two | prod_jf9j542       |
    And the follow brands exist:
      | Name    | Code    | Email               |
      | Example | example | <EMAIL> |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 3400   | USD      | true      | month    | true   |
      | Product One | 3300   | USD      | true      | month    | true   |
      | Product One | 3400   | GBP      | true      | month    | true   |
      | Product One | 3500   | GBP      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Two  |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Third Plan |
      | Public     | True       |
      | Per Seat   | False      |
      | User Count | 10         |
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   |
      | <EMAIL>   | UK      | cust_dfugfdu       | Customer Two   |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Three |
      | <EMAIL>  | UK      | cust_dfudsdu       | Customer Four  |
      | <EMAIL>  | UK      | cust_dfugjfdu      | Customer Five  |
      | <EMAIL>   | UK      | cust_dfugmnenf     | Customer Six   |
      | <EMAIL> | UK      | cust_dfurjg        | Customer Seven |
      | <EMAIL> | UK      | cust_drngu         | Customer Eight |
      | <EMAIL>  | UK      | cust_drmrdu        | Customer Nine  |
      | <EMAIL>   | UK      | cust_dloluesu      | Customer Ten   |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
      | Test Two          | 3000         | USD            | month          | <EMAIL> |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
      | Test Two          | 3000         | USD            | month          | <EMAIL> |
      | Test Plan         | 3300         | USD            | month          | <EMAIL> |
      | Test Plan         | 3300         | USD            | month          | <EMAIL> |
      | Test Two          | 3300         | USD            | month          | <EMAIL> |
      | Test Plan         | 3300         | USD            | month          | <EMAIL> |
      | Test Plan         | 3400         | USD            | month          | <EMAIL> |
      | Test Two          | 3400         | GBP            | month          | <EMAIL> |
# 5 at 3000 USD # 7 on Test Plan  # 1 Test Two at 3400 GBP  # 3 Test Plan at 3000 USD
# 4 at 3300 USD # 4 on Test Two   # 1 Test Two at 3300 USD  # 2 Test Two at 3000 USD
# 1 at 3400 USD #                 # 1 Test Plan at 3400 USD # 3 Test Plan at 3300 USD
# 1 at 3400 GBP #                 # 1 Test Plan at 3300 USD #

  Scenario: Process Mass Change change
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And there are the following mass subscription changes exists:
      | Target Subscription Plan | Change Date | New Subscription Plan |
      | Test Plan                | +1 second   | Test Two              |
    When I process mass change subscriptions
    Then there will be 11 subscriptions on "Test Two"

  Scenario: Process Mass Change price
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And there are the following mass subscription changes exists:
      | Target Subscription Plan | Target Price Amount | Target Price Currency | Target Price Schedule | Change Date | New Subscription Plan | New Price Amount | New Price Currency | New Price Schedule |
      | Test Plan                | 3000                | USD                   | month                 | +1 second   | Test Two              | 3400             | USD                | month              |
    When I process mass change subscriptions
    Then there will be 7 subscriptions on "Test Two"
    Then there will be 4 subscriptions with the price 3400 "USD" per "month"