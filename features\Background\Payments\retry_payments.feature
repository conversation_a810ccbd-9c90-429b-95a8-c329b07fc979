Feature: Retry failed payments

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
      | Product Two | prod_jf9j542       |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Two |
      | Public     | True     |
      | Per Seat   | False    |
      | User Count | 10       |
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      | Billing Type | Payment Reference |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   | invoice      | null              |
      | <EMAIL>   | UK      | cust_dfugfdu       | Customer Two   | card         | ref_valid         |
      | <EMAIL> | UK      | cust_mlklfdu       | Customer Three | card         | ref_valid         |
      | <EMAIL>  | UK      | cust_dkkoadu       | Customer Four  | card         | ref_fails         |
      | <EMAIL>  | UK      | cust_ddsjfu        | Customer Five  | card         | ref_valid         |
      | <EMAIL>   | UK      | cust_jliujoi       | Customer Six   | card         | ref_fails         |
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |

  Scenario:
    Given the following invoices with a payment attempt exist:
      | Customer                 | Paid  | Next Attempt |
      | <EMAIL> | false | +30 seconds  |
    When I retry failed payments
    Then then the invoice for "<EMAIL>" will be marked as paid

  Scenario:
    Given the following invoices with a payment attempt exist:
      | Customer                  | Paid  | Next Attempt | Retry Count |
      | <EMAIL> | false | +30 seconds  | 1           |
    When I retry failed payments
    Then then the invoice for "<EMAIL>" will not be marked as paid
    And the retry count for payment failure process for "<EMAIL>" will be 2
    Then the subscription "Test Plan" for "<EMAIL>" will not be cancelled

  Scenario:
    Given the following invoices with a payment attempt exist:
      | Customer                  | Paid  | Next Attempt | Retry Count |
      | <EMAIL> | false | +30 seconds  | 4           |
    When I retry failed payments
    Then then the invoice for "<EMAIL>" will not be marked as paid
    And the retry count for payment failure process for "<EMAIL>" will be 5
    Then the subscription "Test Plan" for "<EMAIL>" will be cancelled