{"global": {"loading": "Carregamento", "country": {"AU": "Austrália", "BE": "Bélgica", "CA": "Canadá", "HR": "<PERSON><PERSON><PERSON><PERSON>", "CZ": "República Checa", "DK": "Dinamarca", "EE": "Estónia", "FI": "Finlândia", "FR": "França", "DE": "<PERSON><PERSON><PERSON>", "GR": "Gré<PERSON>", "HU": "Hungria", "IS": "Islândia", "LV": "Letónia", "LI": "Liechtenstein", "LT": "Lituânia", "LU": "Luxemburgo", "GB": "Reino Unido", "US": "Estados Unidos", "NL": "<PERSON><PERSON><PERSON>", "RO": "Roménia", "SK": "Eslováquia", "SI": "Eslovénia", "ES": "Espanha", "SE": "Suécia", "AF": "Afeganistão", "AL": "Albânia", "DZ": "<PERSON><PERSON><PERSON><PERSON>", "AD": "Andorra", "AO": "Angola", "AI": "<PERSON><PERSON><PERSON>", "AQ": "Antárctica", "AG": "Antígua e Barbuda", "AR": "Argentina", "AM": "Arménia", "AW": "Aruba", "AT": "Áustria", "AZ": "Azerbaijão", "BS": "Bahamas", "BH": "<PERSON><PERSON><PERSON>", "BD": "Bangladesh", "BB": "Barbados", "BY": "Bielorrússia", "BZ": "Belize", "BJ": "<PERSON><PERSON>", "BM": "Bermudas", "BT": "<PERSON><PERSON>", "BO": "Bolívia", "BA": "Bósnia e Herzegovina", "BW": "<PERSON><PERSON><PERSON>", "BR": "Brasil", "IO": "Território Britânico do Oceano Índico", "BN": "Brunei Darussalam", "BG": "Bulgária", "BF": "Burquina Faso", "BI": "Burundi", "CV": "Cabo Verde", "KH": "Camboja", "CM": "Camarões", "KY": "<PERSON><PERSON>", "CF": "República Centro-Africana", "TD": "<PERSON><PERSON>", "CL": "Chile", "CN": "China", "CX": "Ilha do Natal", "CC": "<PERSON><PERSON> (Keeling)", "CO": "Colômbia", "KM": "Comores", "CG": "Congo", "CD": "Congo, República Democrática do", "CK": "<PERSON><PERSON>", "CR": "Costa Rica", "CI": "Costa do Marfim", "CU": "Cuba", "CY": "<PERSON><PERSON>", "DJ": "<PERSON><PERSON><PERSON>", "DM": "<PERSON><PERSON><PERSON>", "DO": "República Dominicana", "EC": "Equador", "EG": "<PERSON><PERSON><PERSON>", "SV": "El Salvador", "GQ": "G<PERSON>é <PERSON>", "ER": "Eritreia", "SZ": "<PERSON><PERSON><PERSON><PERSON>", "ET": "Etiópia", "FK": "<PERSON><PERSON> (Malvinas)", "FO": "<PERSON><PERSON>", "FJ": "Fiji", "GF": "Guiana Francesa", "PF": "Polinésia <PERSON>", "GA": "Gabão", "GM": "Gâmbia", "GE": "Geórgia", "GH": "Gana", "GI": "Gibraltar", "GL": "Gronelândia", "GD": "Granada", "GP": "Guadalupe", "GT": "Guatemala", "GG": "Guernsey", "GN": "G<PERSON>é", "GW": "Guiné-Bissau", "GY": "Guiana", "HT": "Haiti", "HN": "Honduras", "HK": "Hong Kong", "IN": "Índia", "ID": "Indonésia", "IR": "Irão, República Islâmica do", "IQ": "<PERSON><PERSON>", "IE": "Irlanda", "IM": "<PERSON><PERSON>", "IL": "Israel", "IT": "Itália", "JM": "Jamaica", "JP": "Japão", "JE": "Jersey", "JO": "Jordânia", "KZ": "Cazaquistão", "KE": "Quénia", "KI": "Kiribati", "KP": "Coreia, República Popular Democrática da", "KR": "Coreia, República da", "KW": "Kuwait", "KG": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LA": "República Democrática Popular do Laos", "LB": "Líbano", "LS": "<PERSON><PERSON>", "LR": "Libéria", "LY": "Líbia", "MO": "Macau", "MG": "Madagáscar", "MW": "Malawi", "MY": "Malásia", "MV": "<PERSON><PERSON><PERSON>", "ML": "Mali", "MT": "Malta", "MH": "<PERSON><PERSON>", "MQ": "Martinica", "MR": "Mauritânia", "MU": "<PERSON><PERSON><PERSON><PERSON>", "YT": "Mayotte", "MX": "México", "FM": "Micronésia, Estados Federados da", "MD": "Moldá<PERSON>, República da", "MC": "Mónaco", "MN": "Mongólia", "ME": "Montenegro", "MS": "Montserrat", "MA": "Marrocos", "MZ": "Moçambique", "MM": "Myanmar", "NA": "Namíbia", "NR": "Nauru", "NP": "Nepal", "NC": "Nova Caledónia", "NZ": "Nova Zelândia", "NI": "Nicarágua", "NE": "<PERSON><PERSON><PERSON>", "NG": "Nigéria", "NU": "Niue", "NF": "Ilha Norfolk", "MK": "Macedónia do Norte", "NO": "<PERSON><PERSON><PERSON>", "OM": "Omã", "PK": "Pa<PERSON><PERSON>", "PW": "<PERSON><PERSON>", "PS": "Palestina, Estado de", "PA": "Panamá", "PG": "Papua Nova Guiné", "PY": "Paraguai", "PE": "Peru", "PH": "Filipinas", "PN": "Pitcairn", "PL": "Polónia", "PT": "Portugal", "QA": "Qatar", "RE": "Reunião", "RU": "Federação Russa", "RW": "<PERSON><PERSON><PERSON>", "BL": "<PERSON>", "SH": "Santa Helena, Ascensão e Tristão da Cunha", "KN": "São Cristóvão e Nevis", "LC": "Santa Lúcia", "MF": "<PERSON> (parte francesa)", "PM": "São Pedro e Miquelon", "VC": "São Vicente e Granadinas", "WS": "Samoa", "SM": "São <PERSON>", "ST": "São Tomé e Príncipe", "SA": "A<PERSON>á<PERSON>", "SN": "Senegal", "RS": "Sérvia", "SC": "Seychelles", "SL": "<PERSON>", "SG": "Singapura", "SX": "<PERSON> (parte neerlandesa)", "SB": "<PERSON><PERSON>", "SO": "Somália", "ZA": "África do Sul", "GS": "Geórgia do Sul e Ilhas Sandwich do Sul", "SS": "Sudão do Sul", "LK": "Sri Lanka", "SD": "Sudão", "SR": "Suriname", "SJ": "Svalbard <PERSON>", "CH": "Suíça", "SY": "República Árabe da Síria", "TW": "Taiwan, Província da China", "TJ": "<PERSON><PERSON><PERSON><PERSON>", "TZ": "Tanzânia, República Unida da", "TH": "Tailândia", "TL": "Timor-Leste", "TG": "Togo", "TK": "Tokelau", "TO": "Tonga", "TT": "Trinidad e Tobago", "TN": "Tunísia", "TR": "Turquia", "TM": "Turquemenistão", "TC": "Ilhas Turcas e Caicos", "TV": "Tuvalu", "UG": "Uganda", "UA": "Ucrânia", "AE": "Emirados Árabes Unidos", "UM": "Ilhas Menores Distantes dos Estados Unidos", "UY": "Uruguai", "UZ": "Uzbequistão", "VU": "Vanuatu", "VE": "Venezuela", "VN": "Vietname", "VG": "Il<PERSON>, Reino Unido", "VI": "<PERSON><PERSON>, E.U.A.", "WF": "Wallis e Futuna", "EH": "Saara Ocidental", "YE": "Iémen", "ZM": "Zâmbia", "ZW": "Zimbabué"}, "select_country": "Selecionar país"}, "public": {"login": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "password": "Palavra-passe", "login_button": "<PERSON><PERSON><PERSON>", "remember_me_label": "Lembrar-se de mim", "forgot_password_link": "Esque<PERSON>u-se da sua palavra-passe?", "signup_link": "Inscrever-se numa conta", "logging_in": "<PERSON><PERSON><PERSON>"}, "signup": {"title": "Inscrever-se", "email": "Correio eletrónico", "email_error": "O correio eletrónico deve ser fornecido", "email_invalid_error": "É necessário fornecer um correio eletrónico válido", "password": "Palavra-passe", "password_error": "A palavra-passe deve ser fornecida", "password_confirm": "Confirmar a palavra-passe", "password_confirm_error": "A palavra-passe deve corresponder", "signup_button": "Inscrever-se", "signing_up": "Em curso", "remember_me_label": "Lembrar-se de mim", "forgot_password_link": "Esque<PERSON>u-se da sua palavra-passe?", "login_link": "Já tem uma conta? Iniciar sessão agora.", "success_message": "A sua inscrição foi efectuada com sucesso. Verifique o seu correio eletrónico."}, "forgot_password": {"title": "Repor a palavra-passe", "email": "Correio eletrónico", "email_error": "É necessário fornecer um endereço eletrónico.", "in_progress": "Em curso", "login_link": "Lembra-se da sua palavra-passe? Iniciar sessão", "success_message": "Verifique o seu correio eletrónico", "request_button": "Repor a palavra-passe"}, "forgot_password_confirm": {"title": "Repor a palavra-passe", "password": "Palavra-passe", "password_error": "Deve ser fornecida uma palavra-passe.", "password_length_error": "A palavra-passe deve ter pelo menos 7 caracteres", "password_confirm": "Confirmar", "password_confirm_error": "As palavras-passe devem corresponder", "reset_button": "Redefinir a palavra-passe", "in_progress": "Em curso", "login_link": "Clique aqui para iniciar sessão.", "success_message": "A sua palavra-passe foi redefinida. Já pode iniciar sessão.", "request_button": "Redefinir a palavra-passe"}, "confirm_email": {"error_message": "Esta ligação não é válida", "success_message": "O seu e-mail está agora confirmado e pode iniciar sessão.", "login_link": "Clique aqui para iniciar sessão."}}, "app": {"menu": {"main": {"home": "Início", "reports": "Relatórios", "subscriptions": "Assinaturas", "finance": "Finanças", "settings": "Definições", "customers": "Clientes", "customer_list": "Lista de clientes", "products": "<PERSON><PERSON><PERSON>", "invoices": "Facturas", "system": "Sistema", "docs": "Documentação", "workflows": "Fluxos de trabalho", "developers": "Programadores", "tax": "Imposto", "mobile": {"show": "<PERSON><PERSON>", "hide": "Ocultar menu"}, "customer_support_integrations": "Integrações de suporte"}}, "team": {"main": {"title": "Definições da equipa", "add_team_member": "Adicionar membro da equipa"}, "invite": {"title": "Adicionar membro da equipa", "close": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "invite_successfully_sent": "O convite foi enviado com sucesso.", "send": "Enviar convite", "sending": "<PERSON><PERSON>", "send_another": "Enviar outro"}, "pending_invites": {"title": "Convites pendentes", "none": "Não existem convites pendentes", "email": "Correio eletrónico", "invited_at": "Convidado em", "cancel": "<PERSON><PERSON><PERSON>", "cancelling": "Cancelamento"}, "members": {"email": "Correio eletrónico", "created_at": "Inscrever-se em", "disable": "Desativar", "disabling": "Em curso", "active": "Ativo", "disabled": "Desativar"}}, "plan": {"main": {"title": "Plano", "payment_schedule_yearly": "<PERSON><PERSON>", "payment_schedule_monthly": "Mensal", "payment_schedule_label": "Calendário de pagamentos", "select_plan": "Selecionar plano", "selected_plan": "Atualmente ativo", "change": "Modificação deste plano", "payment_settings": "Definições de pagamento", "cancel_button": "<PERSON><PERSON><PERSON>", "in_progress": "Processamento", "features": "Caraterísticas", "your_current_plan": "O seu plano atual", "plan_options": "Opções do plano"}}, "user": {"settings": {"title": "Definições do utilizador", "name": "Nome", "email": "E-Mail", "password": "Palavra-passe", "locale": "Localidade", "save": "Guardar", "error_message": "Houve um problema ao guardar as definições do utilizador. Verifique os erros.", "success_message": "<PERSON><PERSON> as suas definições com sucesso.", "danger_zone": "Zona de perigo", "current_password": "Palavra-passe atual", "new_password": "Nova palavra-passe", "new_password_again": "Confirmar a palavra-passe", "change_password": "Alterar a palavra-passe", "need_current_password": "Necessidade de fornecer a sua palavra-passe atual", "need_new_password": "Necessidade de fornecer uma nova palavra-passe", "need_valid_password": "A palavra-passe tem de ter mais de 8 caracteres", "need_password_to_match": "As palavras-passe têm de corresponder", "in_progress": "Em curso"}, "invite": {"title": "Convidar utilizador", "email": "Correio eletrónico", "send": "Enviar", "in_progress": "Em curso", "success_message": "Convite enviado com sucesso!", "need_email": "Necessidade de fornecer um endereço eletrónico", "error_message": "Não é possível enviar o convite.", "role": "Papel"}}, "billing": {"details": {"title": "Dados de faturação", "street_line_one": "<PERSON><PERSON> <PERSON>ua um", "street_line_two": "<PERSON><PERSON> de <PERSON>ua dois", "city": "Cidade", "region": "Estado", "country": "<PERSON><PERSON>", "postal_code": "Código Postal", "submit": "Guardar"}, "main": {"title": "Faturação", "details": "Dados de faturação", "methods": "Métodos de pagamento", "invoices": "Facturas"}, "card_form": {"name": "Nome", "number": "Número do cartão", "exp_month": "Mês de expiração", "exp_year": "Ano de expiração", "cvc": "Código de segurança", "add_card": "<PERSON><PERSON><PERSON><PERSON>"}, "payment_methods": {"title": "Métodos de pagamento", "card_number": "Número", "card_expiry": "Data de validade do cartão", "is_default": "Método de pagamento por defeito", "make_default_btn": "Predefinição", "delete_btn": "Eliminar", "add_card_btn": "Adicionar novo cartão", "no_saved_payment_methods": "Não guardar métodos de pagamento"}}, "customer": {"list": {"title": "Clientes", "email": "Correio eletrónico", "country": "<PERSON><PERSON>", "reference": "Referência", "no_customers": "Atualmente, não existem clientes", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view_btn": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "no_filters": "Sem filtros", "country": "<PERSON><PERSON>", "company_name": "Nome da empresa"}, "loading": "Resultados do carregamento", "error_message": "Ocorreu um erro", "company_name": "Nome da empresa"}, "create": {"title": "Criar novo cliente", "email": "Correio eletrónico", "street_line_one": "Linha de rua 1", "street_line_two": "Linha de rua 2", "city": "Cidade", "region": "Região", "country": "<PERSON><PERSON>", "post_code": "Código postal", "reference": "Referência", "external_reference": "Referência externa", "advance": "avanço", "submit_btn": "Criar cliente", "show_advanced": "Avançado", "success_message": "Cliente criado com sucesso", "address_title": "Endereço", "locale": "Localidade", "billing_type": "Tipo de faturação", "billing_type_card": "Cartão", "billing_type_invoice": "<PERSON><PERSON>", "company_name": "Nome da empresa", "brand": "<PERSON><PERSON>", "tax_number": "Número de identificação fiscal", "standard_tax_rate": "Taxa de imposto normal", "type": "Tipo de cliente", "type_business": "<PERSON>eg<PERSON><PERSON><PERSON>", "type_individual": "Individual", "invoice_format": "Formato da fatura", "help_info": {"email": "O e-mail do cliente para onde devem ser enviadas as facturas", "locale": "A localidade a ser utilizada para a língua", "company": "O nome da empresa", "street_line_one": "A primeira linha do endereço de faturação", "street_line_two": "A segunda linha do endereço de faturação", "city": "A cidade do endereço de faturação", "region": "A região/estado do endereço de faturação", "country": "O país de faturação do cliente - código de país ISO 3166-1 alfa-2.", "post_code": "O código postal do endereço de faturação", "reference": "A sua referência interna para o cliente", "billing_type": "Como o cliente deve ser facturado. Cartão significa que os pagamentos serão automáticos através de um cartão registado. Fatura significa que o cliente recebe uma fatura e paga manualmente", "external_reference": "A referência para o cliente que é utilizada pelo fornecedor de pagamentos. Deixe em branco, a menos que tenha a certeza absoluta de que tem a referência correta.", "brand": "A marca a que o cliente pertencerá.", "tax_number": "O número de identificação fiscal do cliente", "standard_tax_rate": "A taxa de imposto a aplicar ao cliente para tudo o que não seja serviços digitais", "type": "Se o cliente for uma empresa ou um particular", "invoice_format": "O formato que deve ser utilizado para criar e entregar uma fatura"}, "failed_message": "Não foi possível criar o cliente com êxito", "metadata": {"title": "Metadados", "name": "Nome", "value": "Valor", "no_values": "Nenhum valor de metadados", "add": "Adiciona<PERSON>"}}, "view": {"title": "Ver detalhes do cliente", "update": "Atualização", "disable": "Desativar", "enable": "Ativar", "error": {"not_found": "Não foi encontrado nenhum cliente", "unknown": "Ocorreu um erro desconhecido"}, "main": {"title": "Principa<PERSON> pormeno<PERSON>", "email": "Correio eletrónico", "reference": "Referência interna", "external_reference": "Referência externa", "status": "Estado", "locale": "Localidade", "brand": "<PERSON><PERSON>", "billing_type": "Tipo de faturação", "tax_number": "Número de identificação fiscal", "standard_tax_rate": "Taxa de imposto normal", "type": "Tipo", "marketing_opt_in": "Marketing Opt In"}, "address": {"company_name": "Nome da empresa", "title": "Endereço", "street_line_one": "Linha de rua 1", "street_line_two": "Linha de rua 2", "city": "Cidade", "region": "Região", "post_code": "Código postal", "country": "<PERSON><PERSON>"}, "credit_notes": {"title": "Notas de crédito", "list": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON> por", "created_at": "C<PERSON><PERSON> em"}, "no_credit_notes": "Não há notas de crédito para este cliente"}, "credit": {"title": "Ajustes de crédito", "list": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON> por", "created_at": "C<PERSON><PERSON> em"}, "no_credit": "Nenhum crédito para este cliente", "add_button": "<PERSON><PERSON><PERSON><PERSON>"}, "subscriptions": {"title": "Assinaturas", "list": {"plan_name": "Plano", "status": "Estado", "schedule": "<PERSON><PERSON><PERSON><PERSON>", "created_at": "C<PERSON><PERSON> em", "valid_until": "Próxima faturação", "view": "<PERSON>er"}, "add_new": "Adicionar nova subscrição", "no_subscriptions": "Sem assinaturas"}, "subscription_events": {"title": "Eventos de assinatura", "list": {"event": "Evento", "subscription": "Assinatura", "created_at": "C<PERSON><PERSON> em"}, "no_subscription_events": "Nenhum evento de assinatura"}, "payments": {"title": "Pagamentos", "list": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "status": "Estado", "created_at": "C<PERSON><PERSON> em"}, "no_payments": "Ainda não há pagamentos para este cliente"}, "refunds": {"title": "Reembolsos", "list": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON> por", "created_at": "C<PERSON><PERSON> em"}, "no_refunds": "Não há reembolsos para este cliente"}, "payment_details": {"title": "Detalhes do pagamento", "list": {"brand": "<PERSON><PERSON>", "last_four": "Os quatro últimos", "default": "Pagamento por defeito", "expiry_month": "Mês de expiração", "expiry_year": "Ano de expiração", "name": "Nome"}, "add_token": "Com <PERSON>", "add_new": "Adicionar novo", "no_payment_details": "Sem pormenores de pagamento", "delete": "Eliminar", "make_default": "Predefinição"}, "limits": {"title": "Limites", "list": {"feature": "Caraterística", "limit": "Limite"}, "no_limits": "Sem limites"}, "features": {"title": "Caraterísticas", "list": {"feature": "Caraterística"}, "no_features": "Sem caraterísticas"}, "invoices": {"title": "Facturas", "list": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "status": "Estado", "outstanding": "Extraordinário", "overdue": "<PERSON><PERSON><PERSON>", "paid": "Pago", "created_at": "C<PERSON><PERSON> em", "view_btn": "<PERSON>er"}, "no_invoices": "Sem facturas", "next": "Se<PERSON><PERSON>", "prev": "Anterior"}, "invoice_delivery": {"title": "Entrega de facturas", "add_new": "Adicionar novo", "list": {"method": "<PERSON><PERSON><PERSON><PERSON>", "format": "Formato", "detail": "<PERSON><PERSON><PERSON>", "view": "<PERSON>er"}, "no_delivery_methods": "<PERSON>enhum mé<PERSON> de entrega"}, "metric_counters": {"title": "Contadores métricos", "list": {"name": "Nome", "usage": "Utilização", "cost": "<PERSON>usto estimado"}, "no_counters": "Não existem contadores métricos"}, "usage_limits": {"title": "Limites de utilização", "add_new": "Adicionar novo", "list": {"amount": "Mont<PERSON>", "warn_level": "Ação"}, "warn_levels": {"warn": "Avisar", "disable": "Desativar"}, "no_limits": "Não existem limites de utilização para este cliente"}, "metadata": {"title": "Metadados", "no_metadata": "Sem metadados"}, "audit_log": "Registo de auditoria"}, "update": {"title": "Atualizar cliente", "email": "Correio eletrónico", "street_line_one": "Linha de rua 1", "street_line_two": "Linha de rua 2", "city": "Cidade", "region": "Região", "country": "<PERSON><PERSON>", "post_code": "Código postal", "reference": "Referência", "company_name": "Nome da empresa", "external_reference": "Referência externa", "advance": "avanço", "submit_btn": "Atualizado", "show_advanced": "Avançado", "success_message": "Atualizar com êxito o cliente", "address_title": "Endereço", "tax_number": "Número de identificação fiscal", "standard_tax_rate": "Taxa de imposto normal", "locale": "Localidade", "error": {"not_found": "Não foi encontrado nenhum cliente", "unknown": "Ocorreu um erro desconhecido"}, "billing_type": "Tipo de faturação", "billing_type_card": "Cartão", "billing_type_invoice": "<PERSON><PERSON>", "type": "Tipo de cliente", "type_business": "<PERSON>eg<PERSON><PERSON><PERSON>", "type_individual": "Individual", "invoice_format": "Formato da fatura", "help_info": {"email": "O e-mail do cliente para onde devem ser enviadas as facturas", "locale": "A localidade a ser utilizada para a língua", "company_name": "O nome da empresa", "street_line_one": "A primeira linha do endereço de faturação", "street_line_two": "A segunda linha do endereço de faturação", "city": "A cidade do endereço de faturação", "region": "A região/estado do endereço de faturação", "country": "O país de faturação do cliente - código de país ISO 3166-1 alfa-2.", "post_code": "O código postal do endereço de faturação", "reference": "A sua referência interna para o cliente", "billing_type": "Como o cliente deve ser facturado. Cartão significa que os pagamentos serão automáticos através de um cartão registado. Fatura significa que o cliente recebe uma fatura e paga manualmente", "external_reference": "A referência para o cliente que é utilizada pelo fornecedor de pagamentos. Deixe em branco, a menos que esteja extremamente seguro de que tem a referência correta.", "tax_number": "O número de identificação fiscal do cliente", "standard_tax_rate": "A taxa de imposto a aplicar ao cliente para tudo o que não seja serviços digitais", "type": "Se o cliente for uma empresa ou um particular", "invoice_format": "O formato que deve ser utilizado para criar e entregar uma fatura", "marketing_opt_in": "Se o cliente tiver optado por receber mensagens de correio eletrónico de marketing. Isto afecta as integrações de boletins informativos."}, "marketing_opt_in": "Marketing Opt In", "metadata": {"title": "Metadados", "name": "Nome", "value": "Valor", "no_values": "Nenhum valor de metadados", "add": "Adiciona<PERSON>"}}, "menu": {"title": "Clientes", "customers": "Clientes"}}, "product": {"list": {"title": "<PERSON><PERSON><PERSON>", "name": "Nome", "physical": "Físico", "no_products": "Atualmente não existem produtos", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "filter": {"title": "<PERSON><PERSON><PERSON>", "name": "Nome", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Ocorreu um erro"}, "create": {"title": "Criar um novo produto", "name": "Nome", "external_reference": "Referência externa", "advance": "avanço", "submit_btn": "<PERSON><PERSON><PERSON> produto", "show_advanced": "Avançado", "success_message": "Produto criado com sucesso", "failed_message": "Falha ao criar o produto", "tax_rate": "Taxa de imposto", "tax_type": "Tipo de imposto", "physical": "Físico", "tax_types": {"digital_services": "Serviços digitais", "digital_goods": "<PERSON><PERSON> digitais", "physical": "Bens/Serviços físicos"}, "help_info": {"name": "O nome do produto", "external_reference": "A referência do produto que é utilizada pelo fornecedor de serviços de pagamento. Deixe em branco, a menos que tenha a certeza absoluta de que tem a referência correta.", "tax_type": "Isto serve para ajudar a tributar corretamente. Os bens e serviços físicos são tributados de forma diferente dos bens digitais. E, em alguns países, existe um imposto sobre os serviços digitais.", "tax_rate": "A taxa de imposto que deve ser utilizada para este produto. Esta taxa sobrepõe-se a outras taxas de imposto.", "physical": "Este produto é físico?"}}, "view": {"title": "Ver detalhes do produto", "update": "Atualização", "error": {"not_found": "Nenhum produto encontrado", "unknown": "Ocorreu um erro desconhecido"}, "main": {"title": "Principa<PERSON> pormeno<PERSON>", "name": "Nome", "physical": "Físico", "external_reference": "Referência externa", "tax_rate": "Taxa de imposto", "tax_type": "Tipo de imposto", "tax_types": {"digital_services": "Serviços digitais", "digital_goods": "<PERSON><PERSON> digitais", "physical": "Bens/Serviços físicos"}}, "price": {"title": "Preços", "create": "Criar novo preço", "no_prices": "Não existem atualmente preços", "hide": "Tornar o preço privado", "show": "Tornar o preço público", "list": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "recurring": "Pagamento recorrente", "schedule": "Calendário de pagamentos", "including_tax": "O preço inclui impostos", "public": "Preço público", "external_reference": "Referência externa", "usage": "Utilização"}}, "subscription_plan": {"title": "Planos de assinatura", "create": "Criar novo plano", "no_subscription_plans": "Atualmente, não existem planos de subscrição", "view": "<PERSON>er", "list": {"name": "Nome", "external_reference": "Referência externa", "code_name": "Nome do código"}}}, "update": {"title": "Atualizar o produto", "name": "Nome", "external_reference": "Referência externa", "advance": "avanço", "submit_btn": "Atualizado", "show_advanced": "Avançado", "success_message": "Atualizar o produto com êxito", "address_title": "Endereço", "error": {"not_found": "Nenhum produto encontrado", "unknown": "Ocorreu um erro desconhecido"}, "tax_type": "Tipo de imposto", "tax_types": {"digital_services": "Serviços digitais", "digital_goods": "<PERSON><PERSON> digitais", "physical": "Bens/Serviços físicos"}, "tax_rate": "Taxa de imposto", "help_info": {"name": "O nome do produto", "external_reference": "A referência do produto que é utilizada pelo fornecedor de serviços de pagamento. Deixe em branco, a menos que tenha a certeza absoluta de que tem a referência correta.", "tax_type": "Isto serve para ajudar a tributar corretamente. Os bens e serviços físicos são tributados de forma diferente dos bens digitais. E, em alguns países, existe um imposto sobre os serviços digitais.", "tax_rate": "A taxa de imposto que deve ser utilizada para este produto. Esta taxa sobrepõe-se a outras taxas de imposto."}}, "menu": {"title": "Produ<PERSON>", "products": "<PERSON><PERSON><PERSON>", "products_list": "Lista de produtos", "features": "Caraterísticas", "vouchers": "Cupões", "metrics": "Métricas"}}, "price": {"create": {"title": "Criar novo preço", "amount": "Mont<PERSON>", "external_reference": "Referência externa", "advance": "avanço", "submit_btn": "<PERSON><PERSON><PERSON>", "show_advanced": "Avançado", "success_message": "Preço criado com sucesso", "schedule_label": "Calendário de pagamentos", "currency": "<PERSON><PERSON>", "recurring": "É recorrente?", "including_tax": "O preço inclui impostos?", "public": "Público", "metric": "M<PERSON><PERSON><PERSON>", "metric_type": "<PERSON><PERSON><PERSON> m<PERSON>", "create_metric": "É necessário criar uma Métrica", "help_info": {"amount": "O preço é a moeda de nível inferior. Assim, 1,00 USD seria 100 e 9,99 seria 999.", "display_amount": "Este preço seria de {montante}.", "external_reference": "A referência do produto que é utilizada pelo fornecedor de serviços de pagamento. Deixe em branco, a menos que tenha a certeza absoluta de que tem a referência correta.", "recurring": "Se se trata de um pagamento recorrente ou de um pagamento único.", "currency": "A moeda em que o cliente deve ser cobrado", "schedule": "Com que frequência o cliente deve ser facturado", "including_tax": "Se quiser ocultar o imposto no preço ou se quiser que o cliente pague o imposto ele próprio", "public": "Se se tratar de um preço que pode ser afixado publicamente", "usage": "Se o cliente for facturado com base na sua utilização de uma métrica ou por lugar.", "metric_type": "Se a métrica de utilização for reiniciada no final do plano de pagamentos e for utilizada na totalidade para a faturação ou se for contínua e for utilizada a diferença entre a última fatura e a fatura seguinte."}, "metric_types": {"resettable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "continuous": "<PERSON><PERSON><PERSON><PERSON>"}, "schedule": {"week": "<PERSON><PERSON><PERSON>", "month": "Mensal", "year": "<PERSON><PERSON>"}, "type": "Tipo", "types": {"fixed_price": "Preço fixo", "package": "Embalagem", "per_unit": "Por unidade/por lugar", "tiered_volume": "Volume escalonado", "tiered_graduated": "Escalonado Graduado"}, "usage": "Utilização", "units": "Unidades", "tiers": "Níveis", "tiers_fields": {"first_unit": "Primeira unidade", "last_unit": "Última unidade", "unit_price": "Preço unitário", "flat_fee": "Taxa fixa"}}}, "feature": {"list": {"title": "Caraterísticas", "name": "Nome", "code": "Código", "reference": "Referência", "no_features": "Atualmente não existem caraterísticas", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Ocorreu um erro", "loading": "Caraterísticas de carregamento"}, "create": {"title": "Criar nova funcionalidade", "advance": "avanço", "submit_btn": "Criar funcionalidade", "show_advanced": "Avançado", "success_message": "Caraterística criada com sucesso", "address_title": "Endereço", "fields": {"name": "Nome", "code": "Nome de código", "description": "Descrição"}, "help_info": {"name": "O nome da caraterística", "code": "O nome de código da caraterística. É utilizado ao registar uma utilização ou ao verificar os limites.", "description": "A descrição da caraterística"}}}, "subscription_plan": {"create": {"title": "Criar novo plano de subscrição", "main_section": {"title": "Principa<PERSON> pormeno<PERSON>", "fields": {"name": "Nome", "code_name": "Nome do código", "user_count": "Contagem de utilizadores", "public": "Plano disponível ao público", "per_seat": "Por lugar", "free": "<PERSON><PERSON><PERSON><PERSON>"}, "help_info": {"name": "O nome do plano de subscrição", "code_name": "O nome de código do plano a ser utilizado com a API.", "user_count": "O número de utilizadores permitido para este plano", "public": "O plano está disponível para o público ou é um plano personalizado", "free": "Este plano é gratuito?", "per_seat": "O plano é cobrado por lugar?"}}, "trial_section": {"title": "Detalhes do ensaio", "fields": {"has_trial": "<PERSON><PERSON> julgame<PERSON>", "is_trial_standalone": "O ensaio é autónomo", "trial_length_days": "Duração do ensaio em dias"}, "help_info": {"has_trial": "Se o plano tiver um período experimental por defeito", "trial_length_days": "Qual deve ser a duração do ensaio, em dias", "is_trial_standalone": "Se uma avaliação for autónoma, não precisa de um preço e a subscrição é interrompida no final da avaliação"}}, "features_section": {"title": "Caraterísticas", "columns": {"feature": "Caraterística", "description": "Descrição"}, "create": {"name": "Nome", "code_name": "Nome do código", "description": "Descrição", "button": "<PERSON><PERSON><PERSON>"}, "add_feature": "<PERSON><PERSON><PERSON><PERSON>", "existing": "Caraterísticas existentes", "new": "Criar novo", "no_features": "Sem caraterísticas"}, "limits_section": {"title": "Limites", "columns": {"limit": "Limite", "feature": "Caraterística", "description": "Descrição"}, "fields": {"limit": "Limite", "feature": "Caraterística"}, "add_limit": "<PERSON><PERSON><PERSON><PERSON>", "no_limits": "Sem limites"}, "prices_section": {"title": "Preços", "columns": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "schedule": "<PERSON><PERSON><PERSON><PERSON>"}, "create": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "recurring": "Recorrente", "schedule": "<PERSON><PERSON><PERSON><PERSON>", "including_tax": "Incluindo impostos", "public": "Público", "button": "<PERSON><PERSON><PERSON>"}, "add_price": "<PERSON><PERSON><PERSON><PERSON>", "existing": "Preços actuais", "new": "Criar novo", "no_prices": "Se<PERSON> preç<PERSON>"}, "submit_btn": "Criar plano"}, "view": {"title": "Ver detalhes do plano de subscrição", "update": "Atualização", "error": {"not_found": "Não foi encontrado nenhum plano de subscrição", "unknown": "Ocorreu um erro desconhecido"}, "main": {"title": "Principa<PERSON> pormeno<PERSON>", "name": "Nome", "code_name": "Nome do código", "per_seat": "Por lugar", "free": "<PERSON><PERSON><PERSON><PERSON>", "user_count": "Contagem de utilizadores", "public": "Disponível publicamente", "has_trial": "<PERSON><PERSON> julgame<PERSON>", "trial_length_days": "Duração do ensaio", "is_trial_standalone": "O ensaio é autónomo?"}, "limits": {"title": "Limites", "list": {"feature": "Caraterística", "limit": "Limite", "no_limits": "Sem limites"}}, "features": {"title": "Caraterísticas", "list": {"feature": "Caraterística", "no_features": "Sem caraterísticas"}}, "price": {"title": "Preços", "list": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "recurring": "Pagamento recorrente", "schedule": "Calendário de pagamentos", "including_tax": "O preço inclui impostos", "public": "Preço público", "external_reference": "Referência externa", "usage": "Utilização"}}}, "update": {"title": "Atualizar o plano de subscrição", "advance": "avanço", "submit_btn": "Atualizar o plano de subscrição", "show_advanced": "Avançado", "success_message": "Plano de subscrição atualizado com êxito", "address_title": "Endereço", "fields": {"name": "Nome", "code_name": "Nome do código", "user_count": "Contagem de utilizadores", "public": "Plano disponível ao público", "per_seat": "Por lugar", "free": "<PERSON><PERSON><PERSON><PERSON>", "prices": "Preços", "features": "Caraterísticas", "limits": "Limites", "has_trial": "<PERSON><PERSON> julgame<PERSON>", "trial_length_days": "Duração do ensaio", "is_trial_standalone": "O ensaio é autónomo?"}, "help_info": {"name": "O nome do plano", "code_name": "O nome de código do plano a ser utilizado com a API.", "user_count": "O número de utilizadores permitido para este plano", "public": "O plano está disponível para o público ou é um plano personalizado", "free": "Este plano é gratuito?", "per_seat": "O plano é cobrado por lugar?", "has_trial": "Se o plano tiver um período experimental por defeito", "trial_length_days": "Qual deve ser a duração do ensaio, em dias", "is_trial_standalone": "Se uma avaliação for autónoma, não necessita de um preço e a subscrição é interrompida no final da avaliação"}, "features": {"title": "Caraterísticas", "add_feature": "Adicionar funcionalidade"}, "limits": {"title": "Limites", "add_limit": "Adicionar limites"}, "prices": {"title": "Preços", "add_price": "Adiciona<PERSON>"}}, "menu": {"subscription_plans": "Planos de assinatura", "products": "<PERSON><PERSON><PERSON>", "features": "Caraterísticas"}}, "payment_details": {"add": {"title": "Adicionar <PERSON><PERSON> de pagamento"}, "add_with_token": {"title": "Adicionar de<PERSON>he de pagamento com token", "field": {"token": "<PERSON><PERSON>"}, "help_info": {"token": "O token fornecido pelo <PERSON>."}, "submit": "Enviar"}}, "subscription": {"create": {"title": "Criar nova subscrição", "subscription_plans": "Planos de assinatura", "payment_details": "Detalhes do pagamento", "no_eligible_prices": "Não existem preços elegíveis", "prices": "Preços", "success_message": "Assinatura criada com sucesso", "submit_btn": "<PERSON><PERSON><PERSON>", "trial": "Teste gratuito", "trial_length_days": "Número de <PERSON>as", "unknown_error": "Ocorreu um erro desconhecido durante a criação", "seats": "Número de lugares", "help_info": {"eligible_prices": "Quando um cliente já tem uma subscrição ativa, todas as novas subscrições devem corresponder ao mesmo período de faturação e à mesma moeda.", "trial": "Quando um cliente já tem uma subscrição ativa, não é elegível para outra avaliação gratuita.", "no_trial": "Este plano não tem um teste gratuito", "seats": "O número de lugares para os quais a subscrição deve ser efectuada"}}, "view": {"title": "Ver subscrição", "main": {"title": "Dados de subscrição", "status": "Estado", "plan": "Plano", "plan_change": "Alterar plano", "customer": "Cliente", "main_external_reference": "Referência externa principal", "created_at": "C<PERSON><PERSON> em", "ended_at": "Terminou em", "valid_until": "<PERSON><PERSON><PERSON><PERSON>", "seat_number": "Número do assento", "change_seat": "<PERSON><PERSON> de lugar"}, "pricing": {"title": "Preços", "price": "Preço", "recurring": "Recorrente", "schedule": "<PERSON><PERSON><PERSON><PERSON>", "change": "Alterar", "no_price": "Não há preço definido para a subscrição"}, "payments": {"title": "Pagamentos", "amount": "Mont<PERSON>", "created_at": "C<PERSON><PERSON> em", "view": "<PERSON>er", "no_payments": "Ainda não há pagamentos"}, "payment_method": {"title": "Método de pagamento", "last_four": "Os quatro últimos", "expiry_month": "Mês de expiração", "expiry_year": "Ano de expiração", "brand": "Tipo de <PERSON>ão", "invoiced": "Facturado"}, "subscription_events": {"title": "Eventos de assinatura", "list": {"event": "Evento", "subscription": "Assinatura", "created_at": "C<PERSON><PERSON> em"}, "no_subscription_events": "Nenhum evento de assinatura"}, "usage_estimate": {"title": "Estimativa de utilização Custo", "usage": "Utilização", "estimate_cost": "Estimativa de custos", "metric": "M<PERSON><PERSON><PERSON>"}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "payment_method": "Atualizar dados de pagamento", "audit_log": "Registo de auditoria"}, "modal": {"seats": {"seats": "<PERSON>sent<PERSON>", "seats_help": "O número de lugares do plano", "submit": "Guardar"}, "price": {"price": "Novo preço", "price_help": "O novo preço a cobrar na próxima fatura", "submit": "Atualização"}, "plan": {"plan": "Novo plano", "plan_help": "O plano para o qual pretende alterar esta subscrição", "price": "Novo preço", "price_help": "O novo preço a cobrar na próxima fatura", "submit": "Atualização", "when": {"title": "Quando", "next_cycle": "Utilizar para o próximo ciclo de faturação", "instantly": "Instantaneamente", "specific_date": "Data específica"}}, "payment_method": {"payment_method": "Utilizar dad<PERSON> de pagamento", "payment_method_help": "Estes dados serão utilizados para a próxima vez que cobrarmos ao cliente.", "update_button": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> de pagamento", "submit": "Atualização"}, "cancel": {"title": "Cancelar subscrição", "cancel_btn": "Confirmar", "close_btn": "<PERSON><PERSON><PERSON>", "when": {"title": "Quando", "end_of_run": "Fim do período de faturação atual", "instantly": "Instantaneamente", "specific_date": "Data específica"}, "refund_type": {"title": "Tipo de reembolso", "none": "<PERSON><PERSON><PERSON>", "prorate": "Reembolso proporcional à utilização", "full": "Reembolso total"}, "cancelled_message": "Cancelado com sucesso"}}, "metadata": {"title": "Metadados", "no_metadata": "Sem metadados"}}, "list": {"title": "Assinaturas", "email": "Cliente", "status": "Estado", "plan": "Plano", "no_subscriptions": "Atualmente não existem subscrições", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "filters": {"status": "Estado", "status_choices": {"cancelled": "Cancelado", "active": "Ativo", "blocked": "Bloqueado", "overdue_payment_open": "Pagamento em atraso em aberto", "trial_active": "Ensaio ativo", "trial_ended": "Ensaio Terminou"}}, "error_message": "Ocorreu um erro", "loading": "Carregando assinaturas..."}, "menu": {"title": "Assinaturas", "subscriptions": "Assinaturas", "subscriptions_list": "Lista de assinaturas", "mass_change": "Mudança de massa"}, "mass_change": {"list": {"title": "Subscrições - Alteração em massa", "change_date": "Alterar data", "status": "Estado", "created_at": "C<PERSON><PERSON> em", "no_mass_change": "Não existem atualmente alterações em massa nas subscrições", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Ocorreu um erro"}, "create": {"title": "C<PERSON>r uma mudança em massa", "criteria": {"title": "Crit<PERSON><PERSON><PERSON>", "plan": "Plano", "price": "Preço", "brand": "<PERSON><PERSON>", "country": "<PERSON><PERSON>"}, "new": {"title": "Novos valores", "plan": "Novo plano", "price": "Novo preço"}, "change_date": {"title": "Alterar data", "help_info": "Após a data de alteração, todas as renovações serão efectuadas ao novo preço. O plano de subscrição será alterado de imediato."}, "estimate": {"amount": "Isto produzirá uma alteração estimada {montante} {moeda} no {calendário}"}, "submit_button": "Botão Enviar"}, "view": {"title": "Alteração da subscrição em massa", "criteria": {"title": "Crit<PERSON><PERSON><PERSON>", "plan": "Plano", "price": "Preço", "brand": "<PERSON><PERSON>", "country": "<PERSON><PERSON>"}, "new_values": {"title": "Novos valores", "plan": "Plano", "price": "Preço"}, "change_date": {"title": "Alterar data"}, "estimate": {"amount": "Isto produzirá uma alteração estimada {montante} {moeda} no {calendário}"}, "export_button": "Exportar lista de clientes", "cancel": "<PERSON><PERSON><PERSON>", "uncancel": "<PERSON><PERSON><PERSON>"}}}, "payment": {"list": {"title": "Pagamentos", "no_payments": "Não existem atualmente pagamentos", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "list": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "customer": "Cliente", "status": "Estado", "created_at": "C<PERSON><PERSON> em"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Ocorreu um erro"}, "view": {"title": "Detalhes do pagamento", "main": {"title": "Principa<PERSON> pormeno<PERSON>", "amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "external_reference": "Referência externa", "status": "Estado", "created_at": "C<PERSON><PERSON> em"}, "customer": {"title": "Cliente", "email": "Correio eletrónico", "more_info": "Mais informaç<PERSON>", "country": "<PERSON><PERSON>", "attach": "Anexar ao cliente"}, "refunds": {"title": "Reembolsos", "amount": "Mont<PERSON>", "reason": "Motivo", "created_by": "<PERSON><PERSON><PERSON> por", "created_at": "C<PERSON><PERSON> em", "none": "Não foram encontrados reembolsos"}, "subscriptions": {"title": "Assinaturas", "plan_name": "Nome do plano", "more_info": "Mais informaç<PERSON>", "none": "Pagamento não associado a subscrições"}, "receipts": {"title": "Receitas", "created_at": "C<PERSON><PERSON> em", "download": "<PERSON><PERSON><PERSON><PERSON>", "none": "O pagamento não tem recibos"}, "buttons": {"refund": "Emissão Reembolso", "generate_receipt": "<PERSON><PERSON><PERSON> recibo"}, "modal": {"attach": {"title": "Anexar ao cliente", "button": "Anexar"}, "refund": {"title": "Reembolso", "amount": {"title": "Mont<PERSON>", "help_info": "Este é o montante da moeda menor. Assim, 100 USD é 1,00 USD."}, "reason": {"title": "Motivo"}, "submit": "Emissão Reembolso", "success_message": "Reembolso criado com sucesso", "error_message": "Algo correu mal"}}}}, "refund": {"list": {"title": "Reembolsos", "no_refunds": "Atualmente não há reembolsos", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "list": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "customer": "Cliente", "status": "Estado", "created_by": "<PERSON><PERSON><PERSON> por", "created_at": "C<PERSON><PERSON> em"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Ocorreu um erro"}, "view": {"title": "Detalhes do reembolso", "main": {"title": "Principa<PERSON> pormeno<PERSON>", "amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "external_reference": "Referência externa", "status": "Estado", "created_at": "C<PERSON><PERSON> em"}, "buttons": {"refund": "Emissão Reembolso"}, "modal": {"refund": {"title": "Reembolso", "amount": {"title": "Mont<PERSON>", "help_info": "Este é o montante da moeda menor. Assim, 100 USD é 1,00 USD."}, "reason": {"title": "Motivo"}, "submit": "Emissão Reembolso"}}}}, "transactions": {"menu": {"title": "Transacções", "payments": "Pagamentos", "refunds": "Reembolsos", "charge_backs": "<PERSON><PERSON> traseira", "invoices": "Facturas", "unpaid_invoices": "Facturas não pagas", "checkout": "Finalizar a compra", "countries": "Países", "tax_types": "Tipos de impostos"}}, "settings": {"menu": {"title": "Definições", "user_settings": "Definições do utilizador", "invite": "<PERSON><PERSON><PERSON>", "pdf_templates": "Modelos PDF", "email_templates": "Modelos de correio eletrónico", "tax_settings": "Definições de impostos", "brand_settings": "Definições da marca", "notification_settings": "Definições de notificação", "system_settings": "Definições do sistema", "users": "Utilizadores", "stripe": "Riscas", "api_keys": "<PERSON><PERSON> da <PERSON>", "exchange_rates": "Taxas de câmbio", "integrations": "Integrações", "vat_sense": "VatSense", "audit_log": "Registo de auditoria"}, "pdf_template": {"list": {"title": "<PERSON><PERSON>", "name": "Nome", "locale": "Localidade", "brand": "<PERSON><PERSON>", "create_btn": "<PERSON><PERSON><PERSON>", "edit_btn": "<PERSON><PERSON>", "no_templates": "Sem modelos", "error_message": "Ocorreu um erro", "generator": "Atualizar as definições do gerador"}, "update": {"title": "Atualizar modelo - {name}", "content": "<PERSON><PERSON><PERSON><PERSON>", "save": "Guardar", "download": "<PERSON><PERSON><PERSON><PERSON> o teste em PDF", "template": "<PERSON><PERSON>", "help_info": {"template": "Utilizar a linguagem de modelos Twig", "variable_docs": "Consulte a documentação para ver quais as variáveis disponíveis"}}, "generator_settings": {"title": "Definições do gerador de PDF", "generator": "G<PERSON>dor", "tmp_dir": "O diretório temporário", "api_key": "<PERSON><PERSON>", "bin": "Localização do contentor", "submit": "Guardar", "help_info": {"generator": "O gerador a ser utilizado. Se não tiver a certeza, utilize mpdf", "tmp_dir": "O diretório temporário a ser utilizado. Se não tiver a certeza, utilize /tmp", "api_key": "A chave API a ser utilizada", "bin": "A localização do wkhtmltopdf"}}, "create": {"title": "C<PERSON>r modelo", "content": "<PERSON><PERSON><PERSON><PERSON>", "save": "Guardar", "download": "<PERSON><PERSON><PERSON><PERSON> o teste em PDF", "template": "<PERSON><PERSON>", "locale": "Localidade", "type": "Tipo", "brand": "<PERSON><PERSON>", "help_info": {"locale": "A localidade a que se destina o modelo PDF", "brand": "A marca a que se destina o modelo PDF", "type": "O tipo de PDF a que se destina o modelo", "template": "Utilizar a linguagem de modelos Twig", "variable_docs": "Consulte a documentação para ver quais as variáveis disponíveis"}}}, "brand_settings": {"list": {"title": "Definições da marca", "name": "Nome", "edit_btn": "<PERSON><PERSON>", "no_brands": "Não existem marcas", "create_new": "<PERSON><PERSON><PERSON>", "error_message": "Ocorreu um erro"}, "update": {"title": "<PERSON><PERSON><PERSON>r definiç<PERSON> da marca - {name}", "fields": {"name": "Nome", "email": "Endereço de correio eletrónico", "company_name": "Nome da empresa", "street_line_one": "Linha de rua 1", "street_line_two": "Linha de rua 2", "city": "Cidade", "region": "Região", "country": "<PERSON><PERSON>", "postcode": "Código postal", "code": "Código", "tax_number": "Número de identificação fiscal", "tax_rate": "Taxa de imposto", "digital_services_tax_rate": "Taxa do imposto sobre serviços digitais", "support_email": "E-mail de suporte", "support_phone_number": "Número de telefone do suporte"}, "help_info": {"name": "O nome da marca", "code": "O código a ser utilizado para identificar a marca nas chamadas à API. Não pode ser atualizado.", "email": "O correio eletrónico a ser utilizado para enviar mensagens de correio eletrónico ao cliente da marca", "company_name": "O nome da empresa para efeitos de faturação", "street_line_one": "A primeira linha do endereço de faturação", "street_line_two": "A segunda linha do endereço de faturação", "city": "A cidade do endereço de faturação", "region": "A região/estado do endereço de faturação", "country": "O país de faturação do cliente - código de país ISO 3166-1 alfa-2.", "postcode": "O código postal do endereço de faturação", "tax_number": "O número de identificação fiscal da empresa/marca", "tax_rate": "A taxa de imposto a utilizar no seu país de origem ou quando não for possível encontrar outra taxa de imposto", "digital_services_tax_rate": "A taxa de imposto a utilizar no seu país de origem ou quando não for possível encontrar outra taxa de imposto para os serviços digitais", "support_email": "O endereço de correio eletrónico do contacto de apoio", "support_phone_number": "O número de telefone do contacto de apoio"}, "general": "Definições gerais", "notifications": "Notificações", "address_title": "Endereço de faturação", "support": "Dados de contacto do apoio", "success_message": "Atualizado", "submit_btn": "Atualização", "notification": {"subscription_creation": "Criação de assinaturas", "subscription_cancellation": "Cancelamento da subscrição", "expiring_card_warning": "Aviso de cartão expirado", "expiring_card_warning_day_before": "Aviso de expiração do cartão - Véspera", "invoice_created": "<PERSON><PERSON> criada", "invoice_overdue": "<PERSON>ura vencida", "quote_created": "Citação criada", "trial_ending_warning": "Aviso de fim de ensaio", "before_charge_warning": "Aviso antes do carregamento", "payment_failure": "Falha de pagamento", "before_charge_warning_options": {"none": "<PERSON><PERSON><PERSON>", "all": "Todos", "yearly": "<PERSON><PERSON>"}}}, "create": {"title": "Criar definições de marca", "fields": {"name": "Nome", "email": "Endereço de correio eletrónico", "company_name": "Nome da empresa", "street_line_one": "Linha de rua 1", "street_line_two": "Linha de rua 2", "city": "Cidade", "region": "Região", "country": "<PERSON><PERSON>", "post_code": "Código postal", "code": "Código", "tax_number": "Número de identificação fiscal", "tax_rate": "Taxa de imposto", "digital_services_tax_rate": "Taxa do imposto sobre serviços digitais", "support_email": "E-mail de suporte", "support_phone_number": "Número de telefone do suporte"}, "help_info": {"name": "O nome da marca", "code": "O código a ser utilizado para identificar a marca nas chamadas à API. Este código não pode ser atualizado. Deve ser alfanumérico em minúsculas, apenas com sublinhados.", "tax_number": "O número de identificação fiscal da marca/empresa", "email": "O correio eletrónico a ser utilizado para enviar mensagens de correio eletrónico ao cliente da marca", "company_name": "O nome da empresa para efeitos de faturação", "street_line_one": "A primeira linha do endereço de faturação", "street_line_two": "A segunda linha do endereço de faturação", "city": "A cidade do endereço de faturação", "region": "A região/estado do endereço de faturação", "country": "O país de faturação do cliente - código de país ISO 3166-1 alfa-2.", "postcode": "O código postal do endereço de faturação", "tax_rate": "A taxa de imposto a utilizar no seu país de origem ou quando não for possível encontrar outra taxa de imposto", "digital_services_tax_rate": "A taxa de imposto a utilizar no seu país de origem ou quando não for possível encontrar outra taxa de imposto para os serviços digitais", "support_email": "O endereço de correio eletrónico do contacto de apoio", "support_phone_number": "O número de telefone do contacto de apoio"}, "address_title": "Endereço de faturação", "success_message": "Atualizado", "support": "Dados de contacto do apoio", "submit_btn": "<PERSON><PERSON><PERSON>"}}, "email_template": {"list": {"title": "Modelos de correio eletrónico", "email": "Correio eletrónico", "country": "<PERSON><PERSON>", "reference": "Referência", "brand": "<PERSON><PERSON>", "no_customers": "Não existem atualmente modelos de correio eletrónico", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "locale": "Localidade", "view_btn": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Ocorreu um erro"}, "create": {"title": "Criar modelo de correio eletrónico", "fields": {"name": "Nome", "locale": "Localidade", "use_emsp_template": "Utilizar o modelo EMSP", "subject": "<PERSON><PERSON><PERSON>", "template_body": "Corpo do modelo", "template_id": "ID do modelo", "brand": "<PERSON><PERSON>"}, "help_info": {"name": "A que correio eletrónico se destina este modelo", "locale": "A que localidade se destina este modelo.", "use_emsp_template": "Se deve ser utilizado o sistema de modelos do fornecedor de serviços de correio eletrónico que está a utilizar. Se não tiver a certeza, não selecione", "subject": "A mensagem a colocar no assunto", "template_body": "O modelo TWIG que deve ser utilizado para gerar o html da mensagem de correio eletrónico.", "template_id": "A ID do modelo fornecida pelo seu fornecedor de serviços de correio eletrónico onde criou o modelo. Se não tiver a certeza, desmarque a opção utilizar modelo emsp.", "brand": "A marca a que se destina o modelo de correio eletrónico.", "variable_docs": "Consulte a documentação para ver quais as variáveis disponíveis"}, "submit_btn": "<PERSON><PERSON><PERSON>", "success_message": "Modelo de correio eletrónico criado com sucesso"}, "update": {"title": "Atualizar o modelo de correio eletrónico", "fields": {"name": "Nome", "locale": "Localidade", "use_emsp_template": "Utilizar o modelo EMSP", "subject": "<PERSON><PERSON><PERSON>", "template_body": "Corpo do modelo", "template_id": "ID do modelo"}, "help_info": {"name": "A que correio eletrónico se destina este modelo", "locale": "A que localidade se destina este modelo.", "use_emsp_template": "Se deve ser utilizado o sistema de modelos do fornecedor de serviços de correio eletrónico que está a utilizar. Se não tiver a certeza, não selecione", "subject": "A mensagem a colocar no assunto", "template_body": "O modelo TWIG que deve ser utilizado para gerar o html da mensagem de correio eletrónico.", "template_id": "A ID do modelo fornecida pelo seu fornecedor de serviços de correio eletrónico onde criou o modelo. Se não tiver a certeza, desmarque a opção utilizar modelo emsp.", "variable_docs": "Consulte a documentação para ver quais as variáveis disponíveis"}, "test_email": "Enviar e-mail de teste", "submit_btn": "Atualização", "success_message": "Modelo de correio eletrónico atualizado com êxito"}}, "notification_settings": {"update": {"title": "Definições de notificação", "submit_btn": "Atualização", "success_message": "Definições de notificação actualizadas", "fields": {"send_customer_notifications": "Enviar notificações de clientes", "emsp": "Fornecedor de serviços de correio eletrónico", "emsp_api_key": "Fornecedor de serviços de correio eletrónico - Chave API", "emsp_api_url": "Fornecedor de serviços de correio eletrónico - URL da API", "emsp_domain": "Fornecedor de serviços de correio eletrónico - Domínio", "default_outgoing_email": "E-mail de saída predefinido"}, "help_info": {"emsp": "Qual o fornecedor de correio eletrónico que pretende utilizar. Se não tiver a certeza, utilize o sistema.", "emsp_api_key": "A chave API fornecida pelo fornecedor do serviço de correio eletrónico.", "emsp_api_url": "O URL da API fornecido pelo fornecedor do serviço de correio eletrónico.", "emsp_domain": "O domínio do fornecedor de serviços de correio eletrónico.", "send_customer_notifications": "Se pretender que o BillaBear envie notificações aos clientes, tais como a criação de uma subscrição, a sua interrupção, o recebimento de um pagamento, etc.", "default_outgoing_email": "O endereço de correio eletrónico predefinido a ser utilizado para enviar notificações quando não existem definições de marca"}}}, "system_settings": {"update": {"title": "Definições do sistema", "submit_btn": "Atualização", "success_message": "Definições do sistema actualizadas", "fields": {"system_url": "URL do sistema", "timezone": "<PERSON><PERSON>", "invoice_number_generation": "Geração do número da fatura", "subsequential_number": "Número <PERSON>", "default_invoice_due_time": "Prazo de vencimento da fatura por defeito", "format": "Formato", "invoice_generation": "Geração de facturas"}, "help_info": {"system_url": "O url de base em que o BillaBear pode ser encontrado.", "timezone": "O fuso horário predefinido para o sistema", "invoice_number_generation": "Como é gerado o número da fatura. Aleatório é uma cadeia aleatória e subsequente significa que é um número que aumenta", "subsequential_number": "O último número de fatura utilizado. O número de fatura seguinte terá um dígito a mais", "default_invoice_due_time": "Quanto tempo decorre entre a criação da fatura e a data de vencimento", "format": "O formato que deve ser utilizado para a geração do número da fatura. %S é o número subsequente e %R é o número de 8 caracteres aleatórios.", "invoice_generation": "Quando devem ser geradas novas facturas de subscrições"}, "invoice_number_generation": {"random": "Número aleatório", "subsequential": "<PERSON><PERSON>", "format": "Formato"}, "default_invoice_due_time": {"30_days": "30 dias", "60_days": "60 dias", "90_days": "90 dias", "120_days": "120 dias"}, "invoice_generation_types": {"periodically": "Periodicamente", "end_of_month": "Fim do mês"}}}, "user": {"list": {"title": "Utilizador", "email": "Correio eletrónico", "roles": "Funções", "reference": "Referência", "no_customers": "Atualmente, não existem clientes", "create_new": "<PERSON><PERSON><PERSON>", "invite": "Convidar novo utilizador", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view_btn": "<PERSON>er", "list": {"email": "Correio eletrónico", "role": "Funções"}, "invite_title": "<PERSON><PERSON><PERSON>", "invite_list": {"email": "Correio eletrónico", "sent_at": "Enviado em", "role": "Funções", "copy_link": "Copiar ligação", "copied_link": "Copiado"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Ocorreu um erro", "audit_log": "Registo de auditoria"}, "update": {"title": "Atualizar utilizador", "fields": {"email": "Correio eletrónico", "roles": "Funções"}, "help_info": {"email": "O e-mail que o utilizador deve utilizar para iniciar sessão e receber notificações.", "roles": "A que é que o utilizador deve ter acesso."}, "submit_btn": "Atualização", "success_message": "Atualização bem sucedida do utilizador"}}, "stripe": {"main": {"title": "Importação de riscas", "edit_config": "Editar configura<PERSON>", "hide_config": "Ocultar configuração", "start_button": "Botão Iniciar importação", "already_in_progress": "Importação já em curso", "list": {"state": "Estado", "last_id": "Último ID processado", "created_at": "C<PERSON><PERSON> em", "updated_at": "Atualização em", "no_results": "Até à data, não se registaram importações de bandas.", "view": "<PERSON>er"}, "danger_zone": {"title": "Zona de perigo", "use_stripe_billing": "Utilize a faturação Stripe para cobrar aos clientes.", "disable_billing": "Desativar a faturação com Stripe", "enable_billing": "Ativar a faturação com Stripe"}, "disable_billing_modal": {"title": "Desativar a faturação com Stripe", "disable_all_subscriptions": "Ao desativar a faturação Stripe, está a dizer que já não quer que o Stripe faça a gestão dos clientes cobrados, mas sim que seja o BillaBear a fazer essa gestão. Isto permite-lhe poupar dinheiro.", "warning": "Uma vez desactivada, se pretender voltar a utilizar a Faturação Stripe, terá de voltar a subscrever manualmente todos os utilizadores.", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar"}, "webhook": {"title": "Webhook", "url": "URL do webhook", "register_webhook": "Registar Webhook", "deregister_webhook": "Anular o registo do Webhook", "help_info": {"url": "Um URL https que está publicamente disponível para chamadas de webhook."}}, "stripe_config": {"title": "<PERSON><PERSON> da <PERSON>", "description": "Para utilizar o <PERSON>e, é necessário configurar as chaves da <PERSON>.", "stripe_private_key": "Chave privada", "help_info": {"stripe_private_key": "A chave API a ser utilizada para autenticar os pedidos de backend", "stripe_public_key": "A chave da API a ser utilizada para autenticar os pedidos de frontend."}, "stripe_public_key": "Chave pública", "submit_button": "Enviar", "error": "<PERSON>ão é possível confirmar as chaves da <PERSON> do Stripe."}}, "view_import": {"title": "Importação de riscas", "progress": "Progresso", "error": "Erro", "last_updated_at": "Última atualização em", "last_id_processed": "Último ID processado", "process": {"started": "Iniciado", "customers": "Clientes", "products": "<PERSON><PERSON><PERSON>", "prices": "Preços", "subscriptions": "Assinaturas", "payments": "Pagamentos", "refunds": "Reembolsos", "charge_backs": "<PERSON><PERSON> traseira", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "api_keys": {"main": {"title": "<PERSON><PERSON> da <PERSON>", "add_new_button": "Criar nova chave de API", "info": {"api_base_url": "URL de base da API"}, "list": {"name": "Nome", "key": "Chave", "expires_at": "Expira em", "created_at": "C<PERSON><PERSON> em", "no_api_keys": "Atualmente não existem chaves API", "disable_button": "Desativar"}, "create": {"title": "Criar nova chave", "name": "Nome", "expires": "Expirações", "close": "<PERSON><PERSON><PERSON>", "create_button": "<PERSON><PERSON><PERSON>"}}}, "exchange_rates": {"title": "Taxas de câmbio", "list": {"currency_code": "<PERSON><PERSON>", "rate": "Taxa", "no_rates": "Sem taxas"}}, "tax_settings": {"update": {"title": "Definições de impostos", "submit_btn": "Enviar", "success_message": "Definições de impostos actualizadas", "fields": {"tax_customers_with_tax_number": "Clientes fiscais com número de identificação fiscal", "eu_business_tax_rules": "<PERSON><PERSON><PERSON> das regras da UE em matéria de fiscalidade das empresas", "eu_one_stop_shop_rule": "Regra do balcão único da UE"}, "help_info": {"tax_customers_with_tax_number": "Se não for verificado, não é cobrado imposto aos clientes que forneceram um número de identificação fiscal", "eu_business_tax_rules": "Se esta opção for activada, os clientes empresariais que tenham fornecido um número de IVA serão tratados de forma diferente dos clientes normais", "eu_one_stop_shop_rule": "Aplicar a regra do balcão único da UE. Em que os países da UE são tributados independentemente do limiar."}}, "vatsense": {"title": "VatSense", "fields": {"vat_sense_enabled": "Sentido de IVA ativado", "vat_sense_api_key": "Chave da <PERSON> do VAT Sense", "validate_vat_ids": "Validar IDs de IVA"}, "help_info": {"vat_sense_enabled": "Se quiser sincronizar diariamente as suas regras fiscais com a base de dados do VAT Sense", "vat_sense_api_key": "A sua chave de API do VAT Sense. <a href=\"https://vatsense.com/signup?referral=BILLABEAR\" target='_blank'>Obtenha uma gr<PERSON>tis aqui</a>", "validate_vat_ids": "Se pretender validar IDs de impostos em relação à API do VAT Sense."}, "description": "Com a nossa integração com o VAT Sense, pode ter as suas regras fiscais actualizadas automaticamente quando houver alterações à legislação fiscal em todo o mundo. Também é possível ter IDs de IVA validados pelo VAT Sense para que possa garantir que os clientes europeus têm IDs de IVA válidos.", "create_account": "Pode criar uma conta gratuita.", "create_account_link": "C<PERSON><PERSON> conta"}}}, "charge_backs": {"list": {"title": "<PERSON><PERSON> traseira", "no_charge_backs": "Atualmente, não existem retrocessos", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view_payment": "<PERSON>er pagamento", "list": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "customer": "Cliente", "status": "Estado", "created_at": "C<PERSON><PERSON> em"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}}}, "reports": {"dashboard": {"title": "Painel de controlo", "subscription_count": {"title": "Subscrições activas"}, "subscription_creation": {"title": "Novas assinaturas"}, "subscription_cancellation": {"title": "Assinaturas canceladas"}, "payment_amount": {"title": "Receitas obtidas"}, "refund_amount": {"title": "Montante reembol<PERSON>o"}, "charge_back_amount": {"title": "Montante contestado"}, "estimated_mrr": "Estimativa da taxa de rentabilidade", "estimated_arr": "Estimativa de ARR", "header": {"active_subscriptions": "Subscrições activas", "active_customers": "Clientes activos", "unpaid_invoices": "Facturas não pagas"}, "buttons": {"daily": "<PERSON><PERSON><PERSON>", "monthly": "Mensal", "yearly": "<PERSON><PERSON>", "subscriptions": "Assinaturas", "payments": "Pagamentos"}, "links": {"customers": "Clientes", "subscriptions": "Assinaturas", "invoices": "Facturas"}, "latest_customers": {"title": "Clientes mais recentes", "list": {"email": "Correio eletrónico", "creation_date": "Data de criação"}}, "latest_events": {"title": "Últimos eventos", "list": {"event_type": "Tipo de evento", "customer": "Cliente", "creation_date": "Data de criação"}}, "latest_payments": {"title": "Últimos pagamentos", "list": {"amount": "Mont<PERSON>", "customer": "Cliente", "creation_date": "Data de criação"}}, "payments": {"title": "Totais de pagamento"}, "loading_chart": "Carregando dados do gráfico..."}, "expiring_cards": {"main": {"title": "Cartõ<PERSON> expirados", "list": {"customer_email": "E-mail do cliente", "card_number": "Número do cartão", "no_expiring_cards": "Nenhum cartão prestes a expirar", "loading": "carre<PERSON><PERSON>", "view": "<PERSON>er"}}}, "menu": {"title": "Relatórios", "dashboard": "Painel de controlo", "expiring_cards": "Cartõ<PERSON> expirados", "subscriptions": "Assinaturas", "tax": "Imposto", "churn": "Cancelamento de subscrições", "lifetime": "Vida útil"}, "subscriptions": {"overview": {"title": "Assinaturas", "plans": {"title": "Repartição dos planos"}, "schedules": {"title": "Repartição do calendário"}}, "churn": {"title": "Cancelamento de subscrições", "buttons": {"daily": "<PERSON><PERSON><PERSON>", "monthly": "Mensal", "yearly": "<PERSON><PERSON>"}}}, "vat": {"overview": {"title": "IVA", "list": {"amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "country": "<PERSON><PERSON>"}}}, "financial": {"lifetime": {"title": "Valor do tempo de vida", "lifespan": "Tempo de vida", "lifespan_value": "{vida} anos", "lifetime": "Valor do tempo de vida", "customer_count": "Contagem de clientes", "filters": {"country": "<PERSON><PERSON>", "payment_schedule": "Calendário de pagamentos", "subscription_plan": "Plano de assinatura", "brand": "<PERSON><PERSON>"}, "help_info": {"country": "Para ver o valor do tempo de vida dos utilizadores deste país", "payment_schedule": "Para ver o valor do tempo de vida dos utilizadores que pagam segundo um calendário de pagamentos", "subscription_plan": "Para ver o valor do tempo de vida dos utilizadores para um plano de subscrição", "brand": "Para ver o valor do tempo de vida dos utilizadores de uma marca"}, "schedules": {"week": "<PERSON><PERSON><PERSON>", "month": "Mensal", "year": "<PERSON><PERSON>"}, "chart": {"lifetime_values": "Valor do tempo de vida", "customer_counts": "Contagem de clientes"}, "submit": "Filtro"}}, "tax": {"title": "Relatório fiscal", "map": {"title": "Imposto cobrado para"}, "countries": {"title": "Limiares por país", "transacted_amount": "<strong>Transacted:</strong> {currency}{transacted_amount}", "collected_amount": "<strong>Cobrado:</strong> {currency}{collected_amount}", "threshold_status": "<strong>Limiar:</strong> {status}", "threshold_reached": "Alcançado", "threshold_not_reached": "Não atingido"}, "transactions": {"title": "Exemplo de exportação", "download": "Descarregar a exportação"}}}, "credit": {"create": {"title": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Mont<PERSON>", "currency": "<PERSON><PERSON>", "reason": "Motivo", "type": "Tipo", "credit": "<PERSON><PERSON><PERSON><PERSON>", "debit": "Débito", "help_info": {"type": "Tipo de ajustamento do crédito, crédito ou débito", "amount": "O preço é a moeda de nível inferior. Assim, 1,00 USD seria 100 e 9,99 seria 999.", "display_amount": "Este preço seria de {montante}.", "currency": "A moeda em que o cliente deve ser cobrado", "reason": "Um motivo opcional que pode ser útil mais tarde."}, "success_message": "Crédito criado com sucesso", "submit_btn": "<PERSON><PERSON><PERSON>"}}, "invoices": {"list": {"title": "Facturas", "unpaid_title": "Facturas não pagas", "email": "E-mail do cliente", "total": "Total", "currency": "<PERSON><PERSON>", "created_at": "C<PERSON><PERSON> em", "download": "<PERSON><PERSON><PERSON><PERSON>", "charge": "Tentativa de pagamento", "no_invoices": "Não existem facturas aqui", "next": "Se<PERSON><PERSON>", "prev": "Anterior", "view_btn": "Ver fatura", "status": "Estado", "paid": "Pago", "outstanding": "Extraordinário", "filter": {"title": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON>", "email": "E-mail do cliente", "number": "Número da fatura"}, "mark_as_paid": "Marcar como pago"}, "menu": {"title": "Facturas", "invoices_list": "Lista de facturas", "unpaid_invoices": "Lista não paga", "create": "<PERSON><PERSON><PERSON> fatura", "quotes": "Citações", "settings": "Definições"}, "create": {"title": "<PERSON><PERSON><PERSON> fatura", "create_invoice": "<PERSON><PERSON><PERSON> fatura", "success_message": "<PERSON><PERSON> criada", "errors": {"no_customer": "É necessário um cliente", "nothing_to_invoice": "É necessário adicionar uma subscrição ou um item único.", "same_currency_and_schedule": "A mesma moeda e o mesmo calendário devem ser utilizados para as subscrições", "currency": "É necessária uma moeda", "need_description": "Necessita de uma descrição", "need_amount": "Quantidade necessária", "need_tax_type": "Necessita de um tipo de imposto"}, "customer": {"create_customer": "Criar cliente", "fields": {"customer": "Cliente", "currency": "<PERSON><PERSON>", "due_date": "Data de vencimento"}, "help_info": {"customer": "O cliente a quem se destina o orçamento", "currency": "A moeda a ser utilizada na fatura", "due_date": "A data de vencimento da fatura; se não for indicada nenhuma data, é utilizada a predefinição do sistema."}}, "subscriptions": {"title": "Assinaturas", "add_new": "Adicionar subscrição", "list": {"subscription_plan": "Plano de assinatura", "price": "Preço", "seat_number": "Número do assento"}, "no_subscriptions": "Sem assinaturas", "add_subscription": "Adicionar subscrição"}, "items": {"title": "Rubricas não recorrentes", "add_item": "Adicionar item único", "no_items": "Não há artigos únicos", "list": {"description": "Descrição", "amount": "Mont<PERSON>", "tax_included": "Imposto incluído", "digital_product": "Produto digital", "tax_type": "Tipo de imposto"}, "tax_types": {"digital_services": "Serviços digitais", "digital_goods": "<PERSON><PERSON> digitais", "physical": "Bens/Serviços físicos"}}}, "view": {"title": "Ver fatura", "main": {"title": "Informações sobre a fatura", "created_at": "C<PERSON><PERSON> em", "pay_link": "Ligação de pagamento", "due_date": "Data de vencimento"}, "customer": {"title": "Cliente", "email": "Correio eletrónico", "more_info": "Mais informaç<PERSON>", "address": {"company_name": "Nome da empresa", "street_line_one": "Linha de rua 1", "street_line_two": "Linha de rua 2", "city": "Cidade", "region": "Região", "post_code": "Código postal", "country": "<PERSON><PERSON>"}}, "biller": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "more_info": "Mais informaç<PERSON>", "address": {"company_name": "Nome da empresa", "street_line_one": "Linha de rua 1", "street_line_two": "Linha de rua 2", "city": "Cidade", "region": "Região", "post_code": "Código postal", "country": "<PERSON><PERSON>"}}, "lines": {"title": "Artigos", "description": "Descrição", "tax_rate": "Taxa de imposto", "amount": "Mont<PERSON>", "tax_exempt": "Isento de impostos"}, "total": {"title": "Totais", "total": "Total", "sub_total": "Subtotal", "tax_total": "Imposto Total"}, "status": {"paid": "Fatura paga com sucesso em {data}", "outstanding": "A fatura ainda tem de ser paga."}, "actions": {"charge_card": "Cartão de débito", "mark_as_paid": "Marcar como pago"}, "payment_failed": {"message": "Não foi possível efetuar o pagamento com êxito"}, "payment_succeeded": {"message": "Pagamento efectuado com sucesso."}, "download": "Transferir fatura", "invoice_delivery": {"title": "Fornecimentos de facturas", "method": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON>", "status": "Estado", "created_at": "C<PERSON><PERSON> em", "no_invoice_deliveries": "Não há entregas de facturas"}}, "settings": {"title": "Definições da fatura", "update": "Atualização"}, "delivery": {"create": {"title": "Criar nova entrega de facturas", "fields": {"method": "<PERSON><PERSON><PERSON><PERSON>", "format": "Formato", "sftp": {"port": "Porto", "hostname": "Nome do anfitrião", "directory": "<PERSON><PERSON><PERSON><PERSON>", "username": "Nome de utilizador", "password": "Palavra-passe"}, "webhook": {"method": "<PERSON><PERSON><PERSON><PERSON>", "url": "URL"}, "email": {"email": "Correio eletrónico", "help_info": "Se não for fornecido um e-mail, o e-mail do cliente será utilizado por defeito"}}, "save": "Guardar"}, "update": {"title": "Atualizar a entrega de facturas", "fields": {"method": "<PERSON><PERSON><PERSON><PERSON>", "format": "Formato", "sftp": {"port": "Porto", "hostname": "Nome do anfitrião", "directory": "<PERSON><PERSON><PERSON><PERSON>", "username": "Nome de utilizador", "password": "Palavra-passe"}, "webhook": {"method": "<PERSON><PERSON><PERSON><PERSON>", "url": "URL"}, "email": {"email": "Correio eletrónico", "help_info": "Se não for fornecido um e-mail, o e-mail do cliente será utilizado por defeito"}}, "save": "Guardar"}, "format": {"pdf": "PDF", "zugferd_v1": "ZUGFeRD V1", "zugferd_v2": "ZUGFeRD V2 - XRechnung"}}, "download": {"loading_message": "Carregando...", "format": "Escolher o formato para descarregar", "download": "<PERSON><PERSON><PERSON><PERSON>"}}, "home": {"stripe_import": {"text": "Não importou os seus dados de faixa.", "link": "Clique aqui para importar agora", "dismiss": "<PERSON><PERSON><PERSON><PERSON>"}, "update_available": {"text": "Existe uma atualização disponível", "link": "Detalhes da publicação", "dismiss": "Recusar"}, "default_tax": {"text": "O seu país não é suportado para taxas de imposto por defeito. Tem de definir uma taxa de imposto na sua marca predefinida!", "link": "Novas marcas"}}, "vouchers": {"create": {"title": "Criar voucher", "submit": "Enviar", "success_message": "Criado com sucesso o voucher", "fields": {"name": "Nome", "type": "Tipo", "type_percentage": "Percentagem", "type_fixed_credit": "Crédito fixo", "percentage": "Percentagem", "entry_type": "Tipo de entrada", "entry_type_manual": "Manual", "entry_type_automatic": "Automático", "amount": "Montante - {moeda}", "code": "Código", "entry_event": "Evento", "event_expired_card_added": "Adicionar novo cartão de pagamento durante o aviso de cartão expirado"}, "help_info": {"name": "O nome do vale", "type": "A percentagem é uma percentagem de uma fatura e o crédito fixo dá um crédito fixo", "entry_type": "Manual significa que o utilizador introduz um código, automático significa que é acionado por um evento", "percentage": "A percentagem de desconto", "amount": "O montante em {moeda} que o vale fornece", "code": "O código que o cliente terá de fornecer para que o vale seja ativado", "entry_event": "O evento que tem de ocorrer para que o vale seja ativado"}}, "list": {"title": "Cupões", "no_vouchers": "Não existem atualmente vales", "create_new": "Criar novo voucher", "list": {"name": "Nome", "type": "Tipo", "entry_type": "Tipo de entrada"}, "view_btn": "<PERSON>er", "loading": "Vales de carregamento"}, "view": {"title": "Vale", "main": {"name": "Nome", "type": "Tipo", "disabled": "Desativado", "entry_type": "Tipo de entrada", "percentage": "Percentagem", "amount": "<PERSON><PERSON> para {moeda}", "code": "Código", "automatic_event": "Evento automático"}, "disable": "Desativar", "enable": "Ativar"}}, "quotes": {"create": {"title": "<PERSON><PERSON><PERSON>", "create_quote": "<PERSON><PERSON><PERSON>", "success_message": "Citação criada", "errors": {"no_customer": "É necessário um cliente", "nothing_to_invoice": "É necessário adicionar uma subscrição ou um item único.", "same_currency_and_schedule": "A mesma moeda e o mesmo calendário devem ser utilizados para as subscrições", "currency": "É necessária uma moeda", "need_description": "Necessita de uma descrição", "need_amount": "Quantidade necessária", "need_tax_type": "Necessita de um tipo de imposto"}, "customer": {"create_customer": "Criar cliente", "fields": {"customer": "Cliente", "currency": "<PERSON><PERSON>", "expires_at": "Expira em"}, "help_info": {"customer": "O cliente a quem se destina o orçamento", "currency": "A moeda a ser utilizada para a cotação", "expires_at": "Quando o orçamento expira e não pode ser pago"}}, "subscriptions": {"title": "Assinaturas", "add_new": "Adicionar subscrição", "list": {"subscription_plan": "Plano de assinatura", "price": "Preço", "per_seat": "Por lugar"}, "no_subscriptions": "Sem assinaturas", "add_subscription": "Adicionar subscrição"}, "items": {"title": "Rubricas não recorrentes", "add_item": "Adicionar item único", "no_items": "Não há artigos únicos", "list": {"description": "Descrição", "amount": "Mont<PERSON>", "tax_included": "Imposto incluído", "digital_product": "Produto digital", "tax_type": "Tipo de imposto"}, "tax_types": {"digital_services": "Serviços digitais", "digital_goods": "<PERSON><PERSON> digitais", "physical": "Bens/Serviços físicos"}}}, "list": {"title": "Citações", "email": "E-mail do cliente", "total": "Total", "currency": "<PERSON><PERSON>", "created_at": "C<PERSON><PERSON> em", "no_quotes": "Não há citações aqui", "next": "Se<PERSON><PERSON>", "prev": "Anterior", "view_btn": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON>", "email": "E-mail do cliente", "number": "Número da fatura"}}, "view": {"title": "Ver orçamento", "quote": {"title": "Citar informações", "created_by": "<PERSON><PERSON><PERSON> por", "created_at": "C<PERSON><PERSON> em", "expires_at": "Expira em", "pay_link": "Ligação de pagamento"}, "status": {"paid": "Cotação paga com sucesso em {data}"}, "customer": {"title": "Cliente", "email": "Correio eletrónico", "more_info": "Mais informaç<PERSON>", "address": {"company_name": "Nome da empresa", "street_line_one": "Linha de rua 1", "street_line_two": "Linha de rua 2", "city": "Cidade", "region": "Região", "post_code": "Código postal", "country": "<PERSON><PERSON>"}}, "lines": {"title": "Artigos", "description": "Descrição", "schedule": "Calendário de pagamentos", "tax_rate": "Taxa de imposto", "amount": "Mont<PERSON>", "one_off": "Único", "tax_exempt": "Isento de impostos"}, "total": {"title": "Totais", "total": "Total", "sub_total": "Subtotal", "tax_total": "Imposto Total"}}}, "system": {"webhooks": {"webhook_endpoint": {"list": {"title": "Pontos de extremidade de webhook", "add": "Adicionar ponto final", "view": "<PERSON>er", "list": {"name": "Nome", "url": "URL", "status": "Estado"}, "no_endpoints": "Não existem atualmente pontos de extremidade de webhook"}, "create": {"title": "Criar ponto de extremidade do Webhook", "fields": {"name": "Nome", "url": "URL"}, "help_info": {"name": "O nome do ponto de extremidade do webhook para ajudar a identificá-lo mais tarde", "url": "O URL para onde as cargas úteis devem ser enviadas"}, "create_button": "<PERSON><PERSON><PERSON>"}, "view": {"title": "Ver ponto final", "main": {"title": "Informações", "name": "Nome", "url": "URL"}}}, "main": {"title": "Webhooks", "manage_endpoints": "Gerir pontos de extremidade", "list": {"type": "Tipo", "created_at": "C<PERSON><PERSON> em", "view_btn": "Ver dados do evento", "loading": "Carregamento de eventos Webhook", "no_events": "Não ocorreram eventos de webhook"}}, "event": {"view": {"title": "Informações sobre o evento", "main": {"title": "Dados do evento", "type": "Tipo de evento", "payload": "Carga útil", "created_at": "C<PERSON><PERSON> em"}, "responses": {"title": "Pedidos de ponto final", "list": {"url": "URL", "status_code": "Código de estado", "body": "Corpo", "error": "Erro", "view": "<PERSON>er", "created_at": "C<PERSON><PERSON> em"}}, "info": {"title": "Ver Pedir informações", "error_message": "Mensagem de erro", "status_code": "Código de estado", "body": "Corpo da resposta", "processing_time": "Tempo de processamento"}}}}, "integrations": {"list": {"title": "Integrações", "list": {"name": "Integração"}, "slack": {"name": "<PERSON><PERSON>ck", "button": "Configurar"}}, "slack": {"webhooks": {"list": {"title": "Webhooks do Slack", "name": "Nome", "webhook": "Webhook", "disable_btn": "Desativar", "enable_btn": "Ativar", "no_webhooks": "Ainda não existem webhooks do Slack", "next": "Se<PERSON><PERSON>", "prev": "Anterior", "error_message": "Não é possível obter webhooks do slack", "create_new": "<PERSON><PERSON><PERSON>"}, "create": {"title": "C<PERSON>r Webhook do Slack", "fields": {"name": "Nome", "webhook": "URL do Webhook"}, "help_info": {"name": "O nome utilizado para identificar este webhook no BillaBear", "webhook": "O URL fornecido pelo Slack para ser usado como um webhook"}, "save_btn": "Guardar"}}, "notifications": {"list": {"title": "Notificação do Slack", "event": "Evento", "webhook": "Webhook", "disable_btn": "Desativar", "template": "<PERSON><PERSON>", "enable_btn": "Ativar", "no_notifications": "Ainda não existem notificações do Slack", "next": "Se<PERSON><PERSON>", "prev": "Anterior", "error_message": "Não é possível obter notificações do Slack", "create_new": "<PERSON><PERSON><PERSON>"}, "create": {"title": "Criar notificação do Slack", "fields": {"webhook": "Webhook", "event": "Evento", "template": "<PERSON><PERSON>"}, "help_info": {"event": "O evento que deve acionar a notificação", "webhook": "O webhook do slack a ser utilizado para a notificação", "template": "O modelo que deve ser utilizado para enviar a notificação. <a href=\"https://docs.billabear.com/user/integration/slack\" target=\"_blank\">As variáveis podem ser encontradas aqui</a>"}, "save_btn": "Guardar"}}, "menu": {"title": "<PERSON><PERSON>ck", "webhooks": "Webhooks", "notification": "Notificações"}}}, "menu": {"title": "Ferramentas do sistema", "webhooks": "Webhooks", "integrations": "Integrações"}}, "checkout": {"create": {"title": "Criar checkout", "create_quote": "Criar checkout", "success_message": "Checkout criado", "errors": {"no_customer": "É necessário um cliente", "nothing_to_invoice": "É necessário adicionar uma subscrição ou um item único.", "same_currency_and_schedule": "A mesma moeda e o mesmo calendário devem ser utilizados para as subscrições", "currency": "É necessária uma moeda", "need_description": "Necessita de uma descrição", "need_amount": "Quantidade necessária", "need_tax_type": "Necessita de um tipo de imposto"}, "customer": {"create_customer": "Criar cliente", "fields": {"name": "Nome", "permanent": "Permanente", "customer": "Cliente", "currency": "<PERSON><PERSON>", "slug": "<PERSON><PERSON>", "expires_at": "Expira em", "brand": "<PERSON><PERSON>"}, "help_info": {"permanent": "Se a saída é permanente ou única", "name": "O nome de identificação da caixa", "customer": "O cliente a quem se destina a caixa", "currency": "A moeda a ser utilizada para o checkout", "expires_at": "Quando o orçamento expira e não pode ser pago", "slug": "O slug para o URL. Se quiser que o checkout tenha um URL bonito, utilize isto.", "brand": "A marca a que pertence a caixa de saída"}}, "subscriptions": {"title": "Assinaturas", "add_new": "Adicionar subscrição", "list": {"subscription_plan": "Plano de assinatura", "price": "Preço", "per_seat": "Por lugar"}, "no_subscriptions": "Sem assinaturas", "add_subscription": "Adicionar subscrição"}, "items": {"title": "Rubricas não recorrentes", "add_item": "Adicionar item único", "no_items": "Não há artigos únicos", "list": {"description": "Descrição", "amount": "Mont<PERSON>", "tax_included": "Imposto incluído", "digital_product": "Produto digital", "tax_type": "Tipo de imposto"}, "tax_types": {"digital_services": "Serviços digitais", "digital_goods": "<PERSON><PERSON> digitais", "physical": "Bens/Serviços físicos"}}}, "view": {"title": "Ver caixa", "checkout": {"title": "Informações de check-out", "created_by": "<PERSON><PERSON><PERSON> por", "created_at": "C<PERSON><PERSON> em", "expires_at": "Expira em", "pay_link": "Ligação de pagamento", "name": "Nome"}, "status": {"paid": "Cotação paga com sucesso em {data}"}, "customer": {"title": "Cliente", "email": "Correio eletrónico", "more_info": "Mais informaç<PERSON>", "address": {"company_name": "Nome da empresa", "street_line_one": "Linha de rua 1", "street_line_two": "Linha de rua 2", "city": "Cidade", "region": "Região", "post_code": "Código postal", "country": "<PERSON><PERSON>"}}, "lines": {"title": "Artigos", "description": "Descrição", "schedule": "Calendário de pagamentos", "tax_rate": "Taxa de imposto", "amount": "Mont<PERSON>", "one_off": "Único", "tax_exempt": "Isento de impostos"}, "total": {"title": "Totais", "total": "Total", "sub_total": "Subtotal", "tax_total": "Imposto Total"}}, "list": {"title": "Checkouts", "email": "Correio eletrónico", "country": "<PERSON><PERSON>", "reference": "Referência", "no_checkouts": "Atualmente não existem checkouts", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view_btn": "<PERSON>er", "list": {"name": "Nome", "created_at": "C<PERSON><PERSON> em", "view": "<PERSON>er"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "name": "Nome", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "loading": "Resultados do carregamento", "error_message": "Ocorreu um erro"}}, "layout": {"topbar": {"menu": {"settings": "Definições", "signout": "<PERSON><PERSON>"}}}, "workflows": {"cancellation_request": {"list": {"title": "Pedidos de cancelamento", "email": "Cliente", "status": "Estado", "plan": "Plano", "no_cancellation_requests": "Não existem atualmente pedidos de cancelamento", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view": "<PERSON>er", "edit_button": "<PERSON><PERSON>", "bulk_button": "Reprocessamento a granel", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Tem erro"}, "error_message": "Ocorreu um erro"}, "view": {"title": "Detalhes do pedido de cancelamento", "subscription": {"title": "Detalhes da subscrição", "name": "Nome do plano", "customer": "Cliente", "original_cancellation_date": "Data de cancelamento original"}, "details": {"title": "Detalhes de cancelamento", "state": "Estado", "when": "Quando", "refund_type": "Tipo de reembolso", "specific_date": "Data de cancelamento"}, "error": {"title": "Erro"}, "buttons": {"process": "Processo de nova tentativa"}}, "edit": {"title": "Editar pedidos de cancelamento", "add_place": "Adicionar local", "add_place_modal": {"title": "Adicionar local", "from_place": "Do local", "to_place": "Para colocar", "name": "Nome", "event_handler": "Manipulador de eventos", "handler_options": "Opções do manipulador", "add": "<PERSON><PERSON><PERSON><PERSON>", "required": "É necessário"}, "edit_place_modal": {"title": "Editar local", "delete_button": "Eliminar local", "enable_button": "Ativar", "disable_button": "Desativar"}}}, "menu": {"title": "Ferramentas de fluxo de trabalho", "cancellation_requests": "Pedidos de cancelamento", "subscription_creation": "Criação de assinaturas", "payment_creation": "Criação de pagamentos", "refund_created_process": "Processo de criação de reembolso", "payment_failure_process": "Processo de falha de pagamento", "charge_back_creation": "Criação de charge back"}, "subscription_creation": {"list": {"title": "Criação de assinaturas", "email": "Cliente", "status": "Estado", "plan": "Plano", "no_cancellation_requests": "Atualmente, não há criação de assinaturas", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Tem erro"}, "error_message": "Ocorreu um erro", "edit_button": "<PERSON><PERSON>", "bulk_button": "Reprocessamento a granel"}, "view": {"title": "Detalhes da criação da assinatura", "subscription": {"title": "Detalhes da subscrição", "name": "Nome do plano", "customer": "Cliente", "view": "Ver subscrição"}, "details": {"title": "Detalhes da criação", "state": "Estado"}, "error": {"title": "Erro"}, "buttons": {"process": "Processo de nova tentativa"}}, "edit": {"title": "Editar criação de subscrição", "add_place": "Adicionar local", "add_place_modal": {"title": "Adicionar local", "from_place": "Do local", "to_place": "Para colocar", "name": "Nome", "event_handler": "Manipulador de eventos", "handler_options": "Opções do manipulador", "add": "<PERSON><PERSON><PERSON><PERSON>", "required": "É necessário"}}}, "payment_creation": {"list": {"title": "Criação de pagamentos", "email": "Cliente", "status": "Estado", "plan": "Plano", "no_results": "Não existem atualmente resultados", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Tem erro"}, "bulk_button": "Reprocessamento a granel", "error_message": "Ocorreu um erro", "edit_button": "<PERSON><PERSON>"}, "view": {"title": "Detalhes da criação do pagamento", "payment": {"title": "Detalhes do pagamento", "name": "Nome do plano", "customer": "Cliente", "view": "<PERSON>er pagamento"}, "details": {"title": "Detalhes da criação", "state": "Estado"}, "error": {"title": "Erro"}, "buttons": {"process": "Processo de nova tentativa"}}, "edit": {"title": "Editar criação de pagamento", "add_place": "Adicionar local", "add_place_modal": {"title": "Adicionar local", "from_place": "Do local", "to_place": "Para colocar", "name": "Nome", "event_handler": "Manipulador de eventos", "handler_options": "Opções do manipulador", "add": "<PERSON><PERSON><PERSON><PERSON>", "required": "É necessário"}}}, "refund_created_process": {"list": {"title": "<PERSON><PERSON><PERSON><PERSON> criado", "email": "Cliente", "status": "Estado", "plan": "Plano", "no_results": "Não existem atualmente resultados", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Tem erro"}, "error_message": "Ocorreu um erro", "edit_button": "<PERSON><PERSON>", "bulk_button": "Reprocessamento a granel"}, "view": {"title": "Detalhes do processo de criação de reembolso", "refund": {"title": "Detalhes do reembolso", "name": "Nome do plano", "customer": "Cliente", "view": "<PERSON>er Reembolso"}, "details": {"title": "Detalhes da criação", "state": "Estado"}, "error": {"title": "Erro"}, "buttons": {"process": "Processo de nova tentativa"}}, "edit": {"title": "Editar processo de reembolso criado", "add_place": "Adicionar local", "add_place_modal": {"title": "Adicionar local", "from_place": "Do local", "to_place": "Para colocar", "name": "Nome", "event_handler": "Manipulador de eventos", "handler_options": "Opções do manipulador", "add": "<PERSON><PERSON><PERSON><PERSON>", "required": "É necessário"}, "edit_place_modal": {"title": "Editar local", "disable_button": "Desativar", "enable_button": "Ativar"}}}, "payment_failure_process": {"list": {"title": "Falha de pagamento", "email": "Cliente", "status": "Estado", "plan": "Plano", "no_results": "Não existem atualmente resultados", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Tem erro"}, "error_message": "Ocorreu um erro"}, "view": {"title": "Detalhes do processo de falha de pagamento", "payment": {"title": "Detalhes da tentativa de pagamento", "amount": "Mont<PERSON>", "customer": "Cliente", "view": "Ver fatura"}, "details": {"title": "Detalhes da criação", "state": "Estado"}, "error": {"title": "Erro"}, "buttons": {"process": "Processo de nova tentativa"}}}, "charge_back_creation": {"list": {"title": "Criação de charge back", "email": "Cliente", "status": "Estado", "plan": "Plano", "no_results": "Não existem atualmente resultados", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correio eletrónico", "reference": "Referência", "external_reference": "Referência externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Tem erro"}, "error_message": "Ocorreu um erro", "edit_button": "<PERSON><PERSON>", "bulk_button": "Reprocessamento a granel"}, "view": {"title": "Detalhes da criação de charge back", "payment": {"title": "Detalhes do pagamento", "name": "Nome do plano", "customer": "Cliente", "view": "<PERSON>er pagamento"}, "details": {"title": "Detalhes da criação", "state": "Estado"}, "error": {"title": "Erro"}, "buttons": {"process": "Processo de nova tentativa"}}, "edit": {"title": "Editar criação de estorno", "add_place": "Adicionar local", "add_place_modal": {"title": "Adicionar local", "from_place": "Do local", "to_place": "Para colocar", "name": "Nome", "event_handler": "Manipulador de eventos", "handler_options": "Opções do manipulador", "add": "<PERSON><PERSON><PERSON><PERSON>", "required": "É necessário"}}}}, "country": {"list": {"title": "Países", "no_countries": "Atualmente, não existem países", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "prev": "Página anterior", "list": {"name": "Nome", "iso_code": "Código", "tax_threshold": "Limiar de tributação", "collecting": "Cobrança de impostos"}, "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "name": "Nome", "code": "Código", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "collecting": "Cobrança de impostos"}, "error_message": "Ocorreu um erro"}, "create": {"title": "Criar novo país", "country": {"fields": {"name": "Nome", "iso_code": "Código do país", "currency": "<PERSON><PERSON>", "threshold": "<PERSON><PERSON>", "in_eu": "Na UE?", "tax_year": "Início do ano fiscal", "collecting": "Cobrar impostos", "tax_number": "Número de identificação fiscal"}, "help_info": {"name": "O nome do país", "iso_code": "O código ISO do país", "currency": "A moeda de reporte do país", "threshold": "O limiar fiscal do país", "in_eu": "O país pertence à UE?", "tax_year": "A data de início do ano fiscal do país", "collecting": "Se os impostos devem ser sempre cobrados para este país", "tax_number": "O seu número de contribuinte para este país."}}, "create_button": "<PERSON><PERSON><PERSON>"}, "view": {"title": "Ver país", "fields": {"name": "Nome", "iso_code": "Código do país", "threshold": "<PERSON><PERSON>", "currency": "<PERSON><PERSON>", "in_eu": "Na UE", "start_of_tax_year": "Início do ano fiscal", "enabled": "<PERSON><PERSON>do", "collecting": "Cobrança de impostos", "tax_number": "Número de identificação fiscal", "transaction_threshold": "Limiar de transação", "threshold_type": "Tipo de limiar"}, "edit_button": "<PERSON><PERSON>", "tax_rule": {"title": "Regras fiscais", "rate": "Taxa de imposto", "type": "Tipo de imposto", "default": "Está predefinido", "start_date": "Data de início", "end_date": "Data final", "no_tax_rules": "Sem regras fiscais", "add": "Adicionar regra de imposto", "edit": "<PERSON><PERSON>"}, "add_tax_rule": {"tax_rate": "Taxa de imposto", "tax_type": "Tipo de imposto", "valid_from": "Válido a partir de", "valid_until": "<PERSON><PERSON><PERSON><PERSON>", "title": "Adicionar regra de imposto", "default": "Regra fiscal por defeito", "save": "Guardar", "select_tax_type": "Selecionar o tipo de imposto"}, "edit_tax_rule": {"tax_rate": "Taxa de imposto", "tax_type": "Tipo de imposto", "valid_from": "Válido a partir de", "valid_until": "<PERSON><PERSON><PERSON><PERSON>", "title": "Editar regra de imposto", "default": "Regra fiscal por defeito", "save": "Atualização", "select_tax_type": "Selecionar o tipo de imposto"}, "states": {"title": "Estados", "add": "Adicionar novo Estado", "name": "Nome", "code": "Código", "collecting": "Cobrar impostos?", "threshold": "<PERSON><PERSON>", "view": "<PERSON>er", "no_states": "Não existem Estados"}}, "edit": {"title": "<PERSON>ar p<PERSON>", "country": {"fields": {"name": "Nome", "iso_code": "Código do país", "currency": "<PERSON><PERSON>", "threshold": "<PERSON><PERSON>", "in_eu": "Na UE?", "tax_year": "Início do ano fiscal", "enabled": "<PERSON><PERSON>do", "collecting": "Cobrar impostos", "tax_number": "Número de identificação fiscal", "transaction_threshold": "Limiar de transação", "threshold_type": "Tipo de limiar", "threshold_types": {"rolling": "Rolagem anual", "calendar": "Ano civil", "rolling_quarterly": "Rolagem por trimestres", "rolling_accounting": "Rolagem por exercício contabilístico"}}, "help_info": {"name": "O nome do país", "iso_code": "O código ISO do país", "currency": "A moeda de reporte do país", "threshold": "O limiar fiscal do país", "in_eu": "O país pertence à UE?", "tax_year": "A data de início do ano fiscal do país", "enabled": "Se o país estiver ativado para inscrições de clientes", "collecting": "Se os impostos devem ser sempre cobrados para este país", "tax_number": "O seu número de contribuinte para este país.", "transaction_threshold": "Qual o limiar de transação para o Estado", "threshold_type": "Como é determinado o período de tempo para o cálculo do limiar"}}, "update_button": "Atualização"}}, "tax_type": {"list": {"title": "Tipos de impostos", "create_new": "Criar novo", "error_message": "Ocorreu um erro", "list": {"name": "Nome", "make_default": "Predefinição", "is_default": "Está predefinido", "default": "Predefinição", "update": "Atualização"}, "no_tax_types": "Não existem atualmente tipos de impostos"}, "create": {"title": "Criar tipo de imposto", "tax_type": {"fields": {"name": "Nome", "vat_sense_type": "IVA Tipo de sentido"}, "help_info": {"name": "O nome do imposto", "vat_sense_type": "O tipo de imposto no sistema do VAT Sense"}}, "create_button": "<PERSON><PERSON><PERSON>"}, "update": {"title": "Atualizar tipo de imposto", "tax_type": {"fields": {"name": "Nome", "vat_sense_type": "IVA Tipo de sentido"}, "help_info": {"name": "O nome do imposto", "vat_sense_type": "O tipo de imposto no sistema do VAT Sense"}}, "update_button": "Atualização"}}, "finance": {"integration": {"title": "Integrações", "fields": {"integration": "Integração", "api_key": "Chave API", "enabled": "<PERSON><PERSON>do"}, "buttons": {"connect": "Ligar através de OAuth", "disconnect": "<PERSON><PERSON><PERSON>", "save": "Guardar"}, "settings": {"title": "Definições"}, "xero": {"account_id": "Código de conta para pagamentos"}, "errors": {"required": "Este campo é obrigatório", "invalid": "Este campo é inválido", "complete_error": "Ocorreu um erro ao tentar guardar estas definições. Por favor, tente novamente."}}, "menu": {"integration": "Integração"}}, "tax": [], "state": {"view": {"title": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "fields": {"name": "Nome", "code": "Código", "threshold": "<PERSON><PERSON>", "collecting": "Coleção", "transaction_threshold": "Limiar de transação", "threshold_type": "Tipo de limiar"}, "tax_rule": {"title": "Regras fiscais", "rate": "Taxa de imposto", "type": "Tipo de imposto", "default": "Está predefinido", "start_date": "Data de início", "end_date": "Data final", "no_tax_rules": "Sem regras fiscais", "add": "Adicionar regra de imposto", "edit": "<PERSON><PERSON>"}, "add_tax_rule": {"tax_rate": "Taxa de imposto", "tax_type": "Tipo de imposto", "valid_from": "Válido a partir de", "valid_until": "<PERSON><PERSON><PERSON><PERSON>", "title": "Adicionar regra de imposto", "default": "Regra fiscal por defeito", "save": "Guardar", "select_tax_type": "Selecionar o tipo de imposto"}, "edit_tax_rule": {"tax_rate": "Taxa de imposto", "tax_type": "Tipo de imposto", "valid_from": "Válido a partir de", "valid_until": "<PERSON><PERSON><PERSON><PERSON>", "title": "Editar regra de imposto", "default": "Regra fiscal por defeito", "save": "Atualização", "select_tax_type": "Selecionar o tipo de imposto"}}, "create": {"title": "Criar novo Estado", "state": {"fields": {"name": "Nome", "code": "Código", "collecting": "Coleção", "threshold": "<PERSON><PERSON>"}, "help_info": {"name": "O nome do Estado", "code": "O código que é frequentemente utilizado como abreviatura para o estado", "collecting": "Se estivermos sempre a cobrar impostos ao Estado", "threshold": "Qual o limiar económico para o Estado"}}, "create_button": "<PERSON><PERSON><PERSON>"}, "edit": {"title": "<PERSON><PERSON>", "state": {"fields": {"name": "Nome", "code": "Código", "collecting": "Coleção", "threshold": "<PERSON><PERSON>", "transaction_threshold": "Limiar de transação", "threshold_type": "Tipo de limiar", "threshold_types": {"rolling": "Rolagem anual", "calendar": "Ano civil", "rolling_quarterly": "Rolagem por trimestres", "rolling_accounting": "Rolagem por exercício contabilístico"}}, "help_info": {"name": "O nome do Estado", "code": "O código que é frequentemente utilizado como abreviatura para o estado", "collecting": "Se estivermos sempre a cobrar impostos ao Estado", "threshold": "Qual o limiar económico para o Estado", "transaction_threshold": "Qual o limiar de transação para o Estado", "threshold_type": "Como é determinado o período de tempo para o cálculo do limiar"}}, "update_button": "Atualização"}}, "onboarding": {"main": {"bar": {"message": "O Stripe deve ser configurado antes de poder utilizar o BillaBear"}, "dialog": {"title": "Integração", "has_stripe_key": {"text": "Introduzir chaves API Stripe válidas", "button": "Introduzir aqui"}, "has_stripe_imports": {"text": "Importar dados do Stripe", "button": "Importação", "dismiss": "Recusar"}, "has_product": {"text": "C<PERSON>r o primeiro produto", "button": "<PERSON><PERSON><PERSON> produto"}, "has_subscription_plan": {"text": "Criar o primeiro plano de assinatura", "button": "<PERSON><PERSON><PERSON>"}, "has_customer": {"text": "C<PERSON>r o primeiro cliente", "button": "<PERSON><PERSON><PERSON>"}, "has_subscription": {"text": "Criar a primeira subscrição", "button": "<PERSON><PERSON><PERSON>"}}, "error": "Alguma coisa correu mal!"}}, "default_error_message": "Alguma coisa correu mal!", "metric": {"list": {"title": "M<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "name": "Nome", "no_metrics": "Ainda não existem métricas!", "filter": {"name": "Nome"}, "view_btn": "<PERSON>er"}, "create": {"title": "<PERSON><PERSON><PERSON> m<PERSON><PERSON>", "fields": {"name": "Nome", "code": "Código", "type": "Tipo", "aggregation_method": "Método de agregação", "aggregation_property": "Propriedade de agregação", "ingestion": "Ingestão", "filters": "<PERSON><PERSON><PERSON>"}, "help_info": {"name": "O nome da métrica", "code": "O código que deve ser utilizado nas chamadas à API. Apenas letras minúsculas, números e sublinhados.", "type": "Se o contador do cliente tiver de ser anulado no final de um período de subscrição", "aggregation_method": "Como devem ser agregados os eventos enviados para o BillaBear.", "aggregation_property": "Que propriedade nos dados do evento deve ser utilizada para agregação.", "ingestion": "Com que frequência os eventos devem ser processados", "filters": "Os filtros que devem ser aplicados à carga útil do evento para serem excluídos na agregação"}, "aggregation_methods": {"count": "Contagem", "sum": "<PERSON><PERSON>", "latest": "<PERSON><PERSON> recentes", "unique_count": "Contagem única", "max": "Máximo"}, "ingestion": {"real_time": "Tempo real", "hourly": "Por hora", "daily": "<PERSON><PERSON><PERSON>"}, "filter": {"name": "Nome", "value": "Valor", "type": "Tipo", "no_filters": "Sem filtros"}, "filter_type": {"inclusive": "Inclusivo", "exclusive": "Exclusivo"}, "create_button": "<PERSON><PERSON><PERSON>"}, "view": {"title": "<PERSON>er métrica", "main": {"name": "Nome", "code": "Código", "type": "Tipo", "aggregation_method": "Método de agregação", "aggregation_property": "Propriedade de agregação", "event_ingestion": "Ingestão"}, "filters": {"title": "<PERSON><PERSON><PERSON>", "name": "Nome", "value": "Valor", "type": "Tipo", "inclusive": "Inclusivo", "exclusive": "Exclusivo"}, "update": "Atualização"}, "update": {"title": "<PERSON><PERSON><PERSON><PERSON>", "update_button": "Guardar"}}, "usage_limit": {"create": {"title": "Criar limite de utilização", "fields": {"amount": "Mont<PERSON>", "action": "Ação"}, "help_info": {"amount": "O montante a que pretende limitar o cliente antes de serem tomadas medidas", "action": "A ação que deve ocorrer quando o limite é excedido."}, "actions": {"warn": "Avisar", "disable": "Desativar"}, "submit": "<PERSON><PERSON><PERSON>"}}, "customer_support": {"integration": {"title": "Integrações de suporte ao cliente", "fields": {"integration": "Integração", "api_key": "Chave API", "enabled": "<PERSON><PERSON>do"}, "buttons": {"connect": "Ligar através de OAuth", "disconnect": "<PERSON><PERSON><PERSON>", "save": "Guardar"}, "settings": {"title": "Definições"}, "errors": {"required": "Este campo é obrigatório", "invalid": "Este campo é inválido", "complete_error": "Ocorreu um erro ao tentar guardar estas definições. Por favor, tente novamente."}, "zendesk": {"token": "<PERSON><PERSON>", "subdomain": "Subdomínio", "username": "Nome de utilizador"}, "freshdesk": {"subdomain": "Subdomínio", "api_key": "Chave API"}}}, "integrations": {"newsletter": {"title": "Integração de boletins informativos", "fields": {"marketing_list": "Lista de marketing", "announcement_list": "Lista de anúncios"}, "no_lists": "Não existem listas disponíveis. Introduzir primeiro os dados de ligação.", "errors": {"list_required": "Não é possível ativar até ter selecionado uma lista. Introduza os detalhes da ligação, guarde e, em seguida, selecione uma lista."}, "mailchimp": {"fields": {"server_prefix": "Prefixo do servidor"}}}, "menu": {"main": "Integrações", "accounting": "Contabilidade", "customer_support": "Apoio ao cliente", "newsletter": "Boletim informativo", "notifications": "Notificações", "crm": "CRM"}, "general": {"fields": {"integration": "Integração", "api_key": "Chave API", "enabled": "<PERSON><PERSON>do"}, "buttons": {"connect": "Ligar através de OAuth", "disconnect": "<PERSON><PERSON><PERSON>", "save": "Guardar"}, "settings": {"title": "Definições"}, "errors": {"required": "Este campo é obrigatório", "invalid": "Este campo é inválido", "complete_error": "Ocorreu um erro ao tentar guardar estas definições. Por favor, tente novamente."}}, "crm": {"title": "Integrações CRM", "fields": {"integration": "Integração"}, "buttons": {"connect": "Ligar através de Oauth", "disconnect": "<PERSON><PERSON><PERSON>", "save": "Guardar"}}}, "compliance": {"audit": {"all": {"title": "Registo de auditoria", "log": "Registo", "date": "Data", "billing_admin": "Administrador de faturação com sessão iniciada", "no_billing_admin": "Isto não foi feito por um administrador de faturação", "display_name": "Nome de exibição", "context": "Contexto do registo", "no_logs": "Não foram encontrados registos"}, "customer": {"title": "Registo de auditoria do cliente - {nome}"}, "billing_admin": {"title": "Registo de auditoria do administrador de faturação - {nome}"}}}}, "install": {"title": "Instalar", "submit_button": "Instalar", "user": {"title": "Primeiro utilizador administrador", "email": "Correio eletrónico", "password": "Palavra-passe"}, "settings": {"title": "Definições do sistema", "default_brand": "Nome da marca por defeito", "from_email": "Endereço de correio eletrónico de origem predefinido", "timezone": "<PERSON><PERSON>", "webhook_url": "URL de base", "currency": "<PERSON><PERSON>", "country": "<PERSON><PERSON>"}, "complete_text": "O BillaBear foi instalado! Pode agora iniciar sessão com os dados que forneceu.", "login_link": "Clique aqui para iniciar sessão", "unknown_error": "<PERSON><PERSON> desconhecido.", "stripe": {"no_api_key": "É necessário fornecer uma chave da API Stripe na variável ENV STRIPE_PRIVATE_API_KEY.", "doc_link": "Mais informações sobre como configurar o BillaBear.", "invalid_api_key": "A chave da API do Stripe é inválida", "support_link": "Pode pedir ajuda aqui."}}}