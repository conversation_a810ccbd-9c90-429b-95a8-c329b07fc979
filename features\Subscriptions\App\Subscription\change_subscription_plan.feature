Feature: Customer Subscription Update Plan

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name          | External Reference |
      | Product One   | prod_jf9j545       |
      | Product Two   | prod_jf9j542       |
      | Product Three | prod_jf9jk42       |
    And there are the following tax types:
      | Name             | Physical |
      | Digital Goods    | False    |
      | Digital Services | False    |
      | Physical         | True     |
    And that the following countries exist:
      | Name           | ISO Code | Threshold | Currency | In EU |
      | United Kingdom | GB       | 1770      | GBP      | False |
      | United States  | US       | 0         | USD      | False |
      | Germany        | DE       | 0         | EUR      | True  |
    And the following country tax rules exist:
      | Country        | Tax Type      | Tax Rate | Valid From |
      | United States  | Digital Goods | 17.5     | -10 days   |
      | United Kingdom | Digital Goods | 17.5     | -10 days   |
      | Germany        | Digital Goods | 17.5     | -10 days   |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 3500   | USD      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
      | Product One | 4500   | USD      | true      | month    | true   |
      | Product Two | 4500   | USD      | true      | month    | true   |
      | Product Two | 4000   | EUR      | true      | month    | true   |
      | Product Three | 5500   | USD      | true      | month    | true   |
      | Product Three | 55000   | USD      | true      | year    | true   |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price 3500 in "USD" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    Given a Subscription Plan exists for product "Product Two" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price 4500 in "USD" with:
      | Name       | Better Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    Given a Subscription Plan exists for product "Product Three" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price 5500 in "USD" monthly and 55000 yearly with:
      | Name       | Even Better Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |


  Scenario: Update Plan
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year |
      | <EMAIL> | 0444      | 03           | 25          |
    When I update the subscription "Test Plan" for "<EMAIL>" to plan:
      | Product | Product Two      |
      | Plan    | Better Test Plan |
      | Price   | 4500             |
      | Currency| USD              |
    Then the subscription "Test Plan" for "<EMAIL>" will not exist
    And the subscription "Better Test Plan" for "<EMAIL>" will exist

  Scenario: Update Plan - Read
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year |
      | <EMAIL> | 0444      | 03           | 25          |
    When I go to update the subscription plan for "Test Plan" for "<EMAIL>"
    Then I will see the plan "Even Better Test Plan" with the price 5500 in "USD"
    Then I will see the plan "Better Test Plan" with the price 4500 in "USD"
    Then I will see the plan "Even Better Test Plan" with the price 55000 in "USD"
    Then I will not see the plan "Better Test Plan" with the price 4000 in "EUR"



  Scenario: Update Plan - Stripe Billing Disable
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And stripe billing is disabled
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year |
      | <EMAIL> | 0444      | 03           | 25          |
    When I update the subscription "Test Plan" for "<EMAIL>" to plan to be changed instantly:
      | Product | Product Two      |
      | Plan    | Better Test Plan |
      | Price   | 4500             |
      | Currency| USD              |
    Then the subscription "Test Plan" for "<EMAIL>" will not exist
    And the subscription "Better Test Plan" for "<EMAIL>" will exist
    And the latest invoice for "<EMAIL>" will have amount due as 1500

  Scenario: Downgrade event
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And stripe billing is disabled
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 |
      | Better Test Plan  | 4500         | USD            | month          | <EMAIL> |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year |
      | <EMAIL> | 0444      | 03           | 25          |
    When I update the subscription "Better Test Plan" for "<EMAIL>" to plan to be changed instantly:
      | Product | Product One |
      | Plan    | Test Plan   |
      | Price   | 3000        |
      | Currency| USD         |
    Then there should be an downgrade event for "<EMAIL>"

  Scenario: Upgrade event
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And stripe billing is disabled
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 |
      | Better Test Plan  | 3000         | USD            | month          | <EMAIL> |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year |
      | <EMAIL> | 0444      | 03           | 25          |
    When I update the subscription "Better Test Plan" for "<EMAIL>" to plan to be changed instantly:
      | Product | Product One |
      | Plan    | Test Plan   |
      | Price   | 4500        |
      | Currency| USD         |
    Then there should be an upgrade event for "<EMAIL>"
