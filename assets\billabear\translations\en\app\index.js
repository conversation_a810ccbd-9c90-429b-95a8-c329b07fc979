import {MENU_TRANSLATIONS} from "./menu";
import {TEAM_TRANSLATIONS} from "./team";
import {PLAN_TRANSLATIONS} from "./plan";
import {USER_TRANSLATIONS} from "./user";
import {BILLING_TRANSLATIONS} from "./billing";
import {CUSTOMER_TRANSLATIONS} from "./customer";
import {PRODUCT_TRANSLATIONS} from "./product";
import {PRICE_TRANSLATIONS} from "./price";
import {FEATURE_TRANSLATIONS} from "./feature";
import {SUBSCRIPTION_PLAN_TRANSLATIONS} from "./subscription_plan";
import {PAYMENT_DETAILS_TRANSLATIONS} from "./payment_details";
import {SUBSCRIPTION_TRANSLATIONS} from "./subscription";
import {PAYMENT_TRANSLATIONS} from "./payment";
import {REFUND_TRANSLATIONS} from "./refund";
import {TRANSACTIONS_TRANSLATIONS} from "./transactions";
import {SETTINGS_TRANSLATIONS} from "./settings";
import {CHARGE_BACKS_TRANSLATIONS} from "./charge_backs";
import {REPORTS_INDEX_TRANSLATION} from "./reports";
import {CREDIT_TRANSLATIONS} from "./credit";
import {INVOICES_TRANSLATIONS} from "./invoices";
import {HOME_TRANSLATIONS} from "./home";
import {VOUCHER_TRANSLATIONS} from "./vouchers";
import {QUOTE_TRANSLATIONS} from "./quote";
import {SYSTEM_TRANSLATIONS} from "./system";
import {CHECKOUT_TRANSLATIONS} from "./checkout";
import {LAYOUT_TRANSLATIONS} from "./layout";
import {WORKFLOWS_TRANSLATIONS} from "./workflows";
import {COUNTRY_TRANSLATIONS} from "./country";
import {TAX_TYPE_TRANSLATIONS} from "./tax_type";
import {FINANCE_TRANSLATIONS} from "./finance";
import {TAX_TRANSLATIONS} from "./tax";
import {STATE_TRANSLATIONS} from "./state";
import {ONBOARDING_TRANSLATIONS} from "./onboarding";
import {METRIC_TRANSLATIONS} from "./metric";
import {USAGE_LIMIT_TRANSLATION} from "./usage_limit";
import {CUSTOMER_SUPPORT_TRANSLATIONS} from "./customer_support";
import {INTEGRATION_TRANSLATIONS} from "./integrations";
import {COMPLIANCE_TRANSLATIONS} from "./compliance";

export const APP_TRANSLATIONS = {
    menu: MENU_TRANSLATIONS,
    team: TEAM_TRANSLATIONS,
    plan: PLAN_TRANSLATIONS,
    user: USER_TRANSLATIONS,
    billing: BILLING_TRANSLATIONS,
    customer: CUSTOMER_TRANSLATIONS,
    product: PRODUCT_TRANSLATIONS,
    price: PRICE_TRANSLATIONS,
    feature: FEATURE_TRANSLATIONS,
    subscription_plan: SUBSCRIPTION_PLAN_TRANSLATIONS,
    payment_details: PAYMENT_DETAILS_TRANSLATIONS,
    subscription: SUBSCRIPTION_TRANSLATIONS,
    payment: PAYMENT_TRANSLATIONS,
    refund: REFUND_TRANSLATIONS,
    transactions: TRANSACTIONS_TRANSLATIONS,
    settings: SETTINGS_TRANSLATIONS,
    charge_backs: CHARGE_BACKS_TRANSLATIONS,
    reports: REPORTS_INDEX_TRANSLATION,
    credit: CREDIT_TRANSLATIONS,
    invoices: INVOICES_TRANSLATIONS,
    home: HOME_TRANSLATIONS,
    vouchers: VOUCHER_TRANSLATIONS,
    quotes: QUOTE_TRANSLATIONS,
    system: SYSTEM_TRANSLATIONS,
    checkout: CHECKOUT_TRANSLATIONS,
    layout: LAYOUT_TRANSLATIONS,
    workflows: WORKFLOWS_TRANSLATIONS,
    country: COUNTRY_TRANSLATIONS,
    tax_type: TAX_TYPE_TRANSLATIONS,
    finance: FINANCE_TRANSLATIONS,
    tax: TAX_TRANSLATIONS,
    state: STATE_TRANSLATIONS,
    onboarding: ONBOARDING_TRANSLATIONS,
    default_error_message: "Something went wrong!",
    metric: METRIC_TRANSLATIONS,
    usage_limit: USAGE_LIMIT_TRANSLATION,
    customer_support: CUSTOMER_SUPPORT_TRANSLATIONS,
    integrations: INTEGRATION_TRANSLATIONS,
    compliance: COMPLIANCE_TRANSLATIONS,
};
