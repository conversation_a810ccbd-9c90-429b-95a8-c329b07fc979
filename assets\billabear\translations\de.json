{"global": {"loading": "Laden", "country": {"AU": "Australien", "BE": "Belgien", "CA": "Ka<PERSON><PERSON>", "HR": "<PERSON><PERSON><PERSON>", "CZ": "Tschechische Republik", "DK": "Dänemark", "EE": "Estland", "FI": "Finnland", "FR": "<PERSON><PERSON><PERSON>", "DE": "Deutschland", "GR": "Griechenland", "HU": "<PERSON><PERSON><PERSON>", "IS": "Island", "LV": "Lettland", "LI": "Liechtenstein", "LT": "Li<PERSON>uen", "LU": "Luxemburg", "GB": "Vereinigtes Königreich", "US": "Vereinigte Staaten", "NL": "Niederlande", "RO": "Rumänien", "SK": "Slowakei", "SI": "Slowenien", "ES": "Spanien", "SE": "Schweden", "AF": "Afghanistan", "AL": "Albanien", "DZ": "Algerien", "AD": "Andorra", "AO": "Angola", "AI": "<PERSON><PERSON><PERSON>", "AQ": "An<PERSON><PERSON><PERSON>", "AG": "Antigua und Barbuda", "AR": "Argentinien", "AM": "Armenien", "AW": "Aruba", "AT": "Österreich", "AZ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BS": "Bahamas", "BH": "Bahrain", "BD": "Bangladesch", "BB": "Barbados", "BY": "Weißrussland", "BZ": "Belize", "BJ": "Benin", "BM": "Bermuda", "BT": "Bhutan", "BO": "Bolivien", "BA": "Bosnien und Herzegowina", "BW": "Botswana", "BR": "Brasilien", "IO": "Britisches Territorium im Indischen Ozean", "BN": "Brunei Darussalam", "BG": "Bulgarien", "BF": "Burkina Faso", "BI": "Burundi", "CV": "Cabo Verde", "KH": "Kambodscha", "CM": "Kamerun", "KY": "Kaimaninseln", "CF": "Zentralafrikanische Republik", "TD": "Chad", "CL": "Chile", "CN": "China", "CX": "Weihnachtsinsel", "CC": "Cocos (Keeling) Inseln", "CO": "Kolumbien", "KM": "Komore<PERSON>", "CG": "Kong<PERSON>", "CD": "Kongo, Demokratische Republik", "CK": "Cookinseln", "CR": "Costa Rica", "CI": "Côte d'Ivoire", "CU": "Ku<PERSON>", "CY": "<PERSON><PERSON><PERSON>", "DJ": "Dschibuti", "DM": "Dominica", "DO": "Dominikanische Republik", "EC": "Ecuador", "EG": "Ägypten", "SV": "El Salvador", "GQ": "Äquatorialguinea", "ER": "Eritrea", "SZ": "<PERSON><PERSON><PERSON><PERSON>", "ET": "Äthiopien", "FK": "Falklandinseln (Malwinen)", "FO": "Fä<PERSON><PERSON><PERSON>", "FJ": "<PERSON><PERSON><PERSON>", "GF": "Französisch-Guayana", "PF": "Französisch-Polynesien", "GA": "<PERSON><PERSON><PERSON>", "GM": "Gambia", "GE": "<PERSON><PERSON>", "GH": "Ghana", "GI": "Gibraltar", "GL": "Grönland", "GD": "Grenada", "GP": "Guadeloupe", "GT": "Guatemala", "GG": "Guernsey", "GN": "Guinea", "GW": "Guinea-Bissau", "GY": "Guyana", "HT": "Haiti", "HN": "Honduras", "HK": "Hongkong", "IN": "Indien", "ID": "Indonesien", "IR": "Iran, Islamische Republik", "IQ": "<PERSON><PERSON>", "IE": "Irland", "IM": "Isle of Man", "IL": "Israel", "IT": "Italien", "JM": "<PERSON><PERSON><PERSON>", "JP": "Japan", "JE": "Jersey", "JO": "<PERSON><PERSON>", "KZ": "Kasachstan", "KE": "Ken<PERSON>", "KI": "Kiribati", "KP": "Korea, Demokratische Volksrepublik", "KR": "Korea, Republik", "KW": "Kuwait", "KG": "Kirgisistan", "LA": "Demokratische Volksrepublik Laos", "LB": "Libanon", "LS": "Lesotho", "LR": "Liberia", "LY": "Libyen", "MO": "Macao", "MG": "<PERSON><PERSON><PERSON>", "MW": "Malawi", "MY": "Malaysia", "MV": "Malediven", "ML": "Mali", "MT": "Malta", "MH": "Marshallinseln", "MQ": "Martinique", "MR": "<PERSON><PERSON><PERSON><PERSON>", "MU": "Mauritius", "YT": "Mayotte", "MX": "Mexiko", "FM": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Föderierte Staaten von", "MD": "Moldawien, Republik", "MC": "Monaco", "MN": "Mongolei", "ME": "Montenegro", "MS": "Montserrat", "MA": "<PERSON><PERSON><PERSON>", "MZ": "Mosambik", "MM": "Myanmar", "NA": "Namibia", "NR": "Nauru", "NP": "Nepal", "NC": "Neukaledonien", "NZ": "Neuseeland", "NI": "Nicaragua", "NE": "Niger", "NG": "Nigeria", "NU": "Niue", "NF": "Norfolkinsel", "MK": "Nordmazedonien", "NO": "Norwegen", "OM": "Oman", "PK": "Pakistan", "PW": "<PERSON><PERSON>", "PS": "Palästina, Staat", "PA": "Panama", "PG": "Papua-Neuguinea", "PY": "Paraguay", "PE": "Peru", "PH": "Philippinen", "PN": "Pitcairn", "PL": "<PERSON><PERSON>", "PT": "Portugal", "QA": "<PERSON><PERSON>", "RE": "Réunion", "RU": "Russische Föderation", "RW": "<PERSON><PERSON><PERSON>", "BL": "St. <PERSON>", "SH": "St. <PERSON>, Himmelfahrt und Tristan <PERSON>ha", "KN": "St<PERSON> Kitts und Nevis", "LC": "St. Lucia", "MF": "<PERSON><PERSON> (französischer Teil)", "PM": "<PERSON><PERSON> und <PERSON>", "VC": "St<PERSON> und die Grenadinen", "WS": "Samoa", "SM": "San Marino", "ST": "Sao Tome und Principe", "SA": "Saudi-Arabien", "SN": "Senegal", "RS": "<PERSON><PERSON>", "SC": "<PERSON><PERSON><PERSON><PERSON>", "SL": "Sierra Leone", "SG": "Singapur", "SX": "Sint Maarten (Niederländischer Teil)", "SB": "Salomoninseln", "SO": "Somalia", "ZA": "Südafrika", "GS": "Südgeorgien und die Südlichen Sandwichinseln", "SS": "Südsudan", "LK": "Sri Lanka", "SD": "Sudan", "SR": "Surinam", "SJ": "Svalbard und Jan Mayen", "CH": "Schweiz", "SY": "Arabische Republik Syrien", "TW": "Taiwan, Provinz China", "TJ": "Tadschikistan", "TZ": "Tansania, Vereinigte Republik", "TH": "Thailand", "TL": "Timor-Leste", "TG": "Togo", "TK": "Tokelau", "TO": "Tonga", "TT": "Trinidad und Tobago", "TN": "Tunesien", "TR": "Türkei", "TM": "Turkmenistan", "TC": "Turks- und Caicosinseln", "TV": "Tuvalu", "UG": "Uganda", "UA": "Ukraine", "AE": "Vereinigte Arabische Emirate", "UM": "Vereinigte Staaten Minor Outlying Islands", "UY": "Uruguay", "UZ": "Usbekistan", "VU": "Vanuatu", "VE": "Venezuela", "VN": "Vietnam", "VG": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Britisch", "VI": "<PERSON><PERSON><PERSON>sel<PERSON>, U.S.A.", "WF": "Wallis und Futuna", "EH": "Westsahara", "YE": "<PERSON><PERSON>", "ZM": "Sambia", "ZW": "Simbabwe"}, "select_country": "Land auswählen"}, "public": {"login": {"title": "<PERSON><PERSON><PERSON><PERSON>", "email": "E-Mail", "password": "Passwort", "login_button": "<PERSON><PERSON><PERSON><PERSON>", "remember_me_label": "<PERSON><PERSON><PERSON>e sich an mich", "forgot_password_link": "Haben Sie Ihr Passwort vergessen?", "signup_link": "Registrierung für ein Konto", "logging_in": "Einloggen"}, "signup": {"title": "<PERSON><PERSON><PERSON><PERSON>", "email": "E-Mail", "email_error": "E-Mail muss angegeben werden", "email_invalid_error": "Eine gültige E-Mail muss angegeben werden", "password": "Passwort", "password_error": "Passwort muss angegeben werden", "password_confirm": "Bestätigen Sie Ihr Passwort", "password_confirm_error": "Passwort muss übereinstimmen", "signup_button": "<PERSON><PERSON><PERSON><PERSON>", "signing_up": "In Arbeit", "remember_me_label": "<PERSON><PERSON><PERSON>e sich an mich", "forgot_password_link": "Haben Sie Ihr Passwort vergessen?", "login_link": "Sie haben bereits ein Konto? Jetzt anmelden.", "success_message": "Sie haben sich erfolgreich angemeldet. Bitte prüfen Sie Ihre E-Mail."}, "forgot_password": {"title": "Passwort zurücksetzen", "email": "E-Mail", "email_error": "Es muss eine E-Mail angegeben werden.", "in_progress": "In Arbeit", "login_link": "Erinnern Sie sich an Ihr Passwort? Anmeldung", "success_message": "Bitte überprüfen Sie Ihre E-Mail", "request_button": "Passwort zurücksetzen"}, "forgot_password_confirm": {"title": "Passwort zurücksetzen", "password": "Passwort", "password_error": "Es muss ein Passwort angegeben werden.", "password_length_error": "Das Passwort muss mindestens 7 Zeichen lang sein", "password_confirm": "Bestätigen Sie", "password_confirm_error": "Die Passwörter müssen übereinstimmen", "reset_button": "Passwort zurücksetzen", "in_progress": "In Arbeit", "login_link": "<PERSON><PERSON><PERSON> hier, um sich einzuloggen.", "success_message": "Ihr Passwort wurde zurückgesetzt. Sie können sich jetzt anmelden.", "request_button": "Passwort zurücksetzen"}, "confirm_email": {"error_message": "Dies ist ein ungültiger Link", "success_message": "Ihre E-Mail ist nun bestätigt und Si<PERSON> können sich anmelden.", "login_link": "<PERSON><PERSON><PERSON> hier, um sich einzuloggen."}}, "app": {"menu": {"main": {"reports": "Berichte", "subscriptions": "Abonnements", "finance": "Fin<PERSON>zen", "settings": "Einstellungen", "customers": "<PERSON><PERSON>", "products": "Produkte", "invoices": "Re<PERSON><PERSON>ngen", "system": "System", "docs": "Dokumentation", "workflows": "Arbeitsabläufe", "developers": "<PERSON><PERSON><PERSON><PERSON>", "home": "Startseite", "customer_list": "Kundenliste", "mobile": {"show": "<PERSON><PERSON> anzeigen", "hide": "<PERSON><PERSON>"}, "tax": "<PERSON><PERSON><PERSON>", "customer_support_integrations": "Integration unterstützen"}}, "team": {"main": {"title": "Team-Einstellungen", "add_team_member": "Teammit<PERSON>ed <PERSON>nzuf<PERSON>"}, "invite": {"title": "Teammit<PERSON>ed <PERSON>nzuf<PERSON>", "close": "Schließen Sie", "email": "E-Mail", "invite_successfully_sent": "Die Einladung wurde erfolgreich versendet.", "send": "Einladung senden", "sending": "<PERSON><PERSON> von", "send_another": "Eine weitere Sendung"}, "pending_invites": {"title": "Ausstehende Einladungen", "none": "<PERSON>s gibt keine ausstehenden Einladungen", "email": "E-Mail", "invited_at": "Eingeladen am", "cancel": "Abbrechen", "cancelling": "Abbrechen"}, "members": {"email": "E-Mail", "created_at": "Angemeldet bei", "disable": "Deaktivieren Sie", "disabling": "In Arbeit", "active": "Aktiv", "disabled": "Deaktivieren Sie"}}, "plan": {"main": {"title": "Plan", "payment_schedule_yearly": "<PERSON><PERSON><PERSON><PERSON>", "payment_schedule_monthly": "<PERSON><PERSON><PERSON>", "payment_schedule_label": "Zahlungszeitplan", "select_plan": "Plan auswählen", "selected_plan": "Derzeit aktiv", "change": "Änderungen an diesem Plan", "payment_settings": "Zahlungseinstellungen", "cancel_button": "Abbrechen", "in_progress": "Verarbeitung", "features": "Eigenschaften", "your_current_plan": "Ihr aktueller Plan", "plan_options": "Optionen planen"}}, "user": {"settings": {"title": "Benutzereinstellungen", "name": "Name", "email": "E-Mail", "password": "Passwort", "locale": "Gebietsschema", "save": "Speichern", "error_message": "<PERSON>im S<PERSON>ichern der Benutzereinstellungen ist ein Problem aufgetreten. Bitte überprüfen Sie die Fehler.", "success_message": "Ihre Einstellungen wurden erfolgreich gespeichert.", "danger_zone": "Gefahrenzone", "current_password": "Aktuelles Passwort", "new_password": "Neues Passwort", "new_password_again": "Bestätigen Sie Ihr Passwort", "change_password": "Passwort ändern", "need_current_password": "Sie müssen Ihr aktuelles Passwort angeben", "need_new_password": "<PERSON><PERSON> müssen ein neues Passwort eingeben", "need_valid_password": "Das Passwort muss mehr als 8 Zeichen enthalten", "need_password_to_match": "Passwörter müssen übereinstimmen", "in_progress": "In Arbeit"}, "invite": {"title": "<PERSON><PERSON><PERSON> e<PERSON>n", "email": "E-Mail", "send": "<PERSON><PERSON> Sie", "in_progress": "In Arbeit", "success_message": "Einladung erfolgreich gesendet!", "need_email": "Es muss eine E-Mail angegeben werden", "error_message": "Einladung kann nicht gesendet werden.", "role": "<PERSON><PERSON>"}}, "billing": {"details": {"title": "Details zur Rechnungsstellung", "street_line_one": "Straße Linie Eins", "street_line_two": "Straße Linie Zwei", "city": "Stadt", "region": "Staa<PERSON>", "country": "Land", "postal_code": "<PERSON><PERSON><PERSON><PERSON>", "submit": "Speichern"}, "main": {"title": "Rechnungsstellung", "details": "Details zur Rechnungsstellung", "methods": "Zahlungsarten", "invoices": "Re<PERSON><PERSON>ngen"}, "card_form": {"name": "Name", "number": "Kartennummer", "exp_month": "Verfallsmonat", "exp_year": "Verfallsjahr", "cvc": "Sicherheitscode", "add_card": "<PERSON><PERSON> hinzufügen"}, "payment_methods": {"title": "Zahlungsarten", "card_number": "<PERSON><PERSON><PERSON>", "card_expiry": "Gültigkeitsdatum der Karte", "is_default": "Standard-Zahlungsmethode", "make_default_btn": "Standard machen", "delete_btn": "Löschen", "add_card_btn": "Neue Karte hinzufügen", "no_saved_payment_methods": "<PERSON><PERSON> sic<PERSON>"}}, "customer": {"list": {"title": "<PERSON><PERSON>", "email": "E-Mail", "country": "Land", "reference": "<PERSON><PERSON><PERSON><PERSON>", "no_customers": "Derzeit sind keine Kunden vorhanden", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "view_btn": "<PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter", "no_filters": "<PERSON><PERSON>", "country": "Land", "company_name": "Name des Unternehmens"}, "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON> laden", "error_message": "Es ist ein Fehler aufgetreten", "company_name": "Name des Unternehmens"}, "create": {"title": "Neuen Kunden anlegen", "email": "E-Mail", "street_line_one": "Straße Linie 1", "street_line_two": "Straße Linie 2", "city": "Stadt", "region": "Region", "country": "Land", "post_code": "<PERSON><PERSON><PERSON><PERSON>", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "<PERSON>nde anlegen", "show_advanced": "Fortgeschrittene", "success_message": "Erfolgreich erstellter Kunde", "address_title": "<PERSON><PERSON><PERSON>", "locale": "Gebietsschema", "billing_type": "Art der Abrechnung", "billing_type_card": "<PERSON><PERSON>", "billing_type_invoice": "<PERSON><PERSON><PERSON><PERSON>", "company_name": "Name des Unternehmens", "brand": "<PERSON><PERSON>", "tax_number": "Umsatzsteuer-Identifikationsnummer", "standard_tax_rate": "Standard-Steuersatz", "type": "<PERSON><PERSON>", "type_business": "Business", "type_individual": "Privatkunde", "help_info": {"email": "Die E-Mail für den Kunden, an den die Rechnungen gehen sollen", "locale": "Das für die Sprache zu verwendende Gebietsschema", "company": "Der Name des Unternehmens", "street_line_one": "Die erste Zeile der Rechnungsadresse", "street_line_two": "Die zweite Zeile der Rechnungsadresse", "city": "Die Stadt für die Rechnungsadresse", "region": "Die Region/das Bundesland für die Rechnungsadresse", "country": "Das Rechnungsland des Kunden - ISO 3166-1 Alpha-2 Ländercode.", "post_code": "Die Postleitzahl für die Rechnungsadresse", "reference": "Ihre interne Referenz für den Kunden", "billing_type": "Wie dem Kunden die Rechnung gestellt werden soll. <PERSON><PERSON> bedeutet, dass die Zahlungen automatisch über eine registrierte Karte erfolgen. Rechnung bedeutet, dass der Kunde eine Rechnung erhält und manuell bezahlt", "external_reference": "Die Referenz für den Kunden, die vom Zahlungsanbieter verwendet wird. <PERSON><PERSON> sie leer, es sei denn, <PERSON><PERSON> sind sehr sicher, dass Sie die richtige Referenz haben.", "brand": "Die Marke, der der Kunde angehören wird.", "tax_number": "Die Umsatzsteuer-Identifikationsnummer des Kunden", "standard_tax_rate": "Der für den Kunden anzuwendende Steuersatz für alles außer digitalen Dienstleistungen", "type": "Wenn der Kunde ein Unternehmen oder eine Privatperson ist", "invoice_format": "Das Format, das bei der Erstellung und Übermittlung einer Rechnung zu verwenden ist"}, "failed_message": "Kunde kann nicht erfolgreich angelegt werden", "invoice_format": "Rechnungsformat", "metadata": {"title": "<PERSON><PERSON><PERSON>", "name": "Name", "value": "Wert", "no_values": "<PERSON><PERSON>", "add": "<PERSON>adaten hinzufügen"}}, "view": {"title": "Kundendetails anzeigen", "update": "Update", "disable": "Deaktivieren Sie", "enable": "Aktivieren Sie", "error": {"not_found": "<PERSON><PERSON> solcher Kunde gefunden", "unknown": "Ein unbekannter Fehler ist aufgetreten"}, "main": {"title": "Wichtigste Details", "email": "E-Mail", "reference": "Interne Referenz", "external_reference": "Externe Referenz", "status": "Status", "locale": "Gebietsschema", "brand": "<PERSON><PERSON>", "billing_type": "Art der Abrechnung", "tax_number": "Umsatzsteuer-Identifikationsnummer", "standard_tax_rate": "Standard-Steuersatz", "type": "<PERSON><PERSON>", "marketing_opt_in": "Marketing Opt-In"}, "address": {"company_name": "Name des Unternehmens", "title": "<PERSON><PERSON><PERSON>", "street_line_one": "Straße Linie 1", "street_line_two": "Straße Linie 2", "city": "Stadt", "region": "Region", "post_code": "<PERSON><PERSON><PERSON><PERSON>", "country": "Land"}, "credit_notes": {"title": "Gutschriften", "list": {"amount": "Betrag", "currency": "Währung", "created_by": "<PERSON><PERSON><PERSON><PERSON> von", "created_at": "Erstellt am"}, "no_credit_notes": "<PERSON>ine Gutschriften für diesen Kunden"}, "credit": {"title": "Kreditanpassungen", "list": {"amount": "Betrag", "currency": "Währung", "created_by": "<PERSON><PERSON><PERSON><PERSON> von", "created_at": "Erstellt am"}, "no_credit": "<PERSON><PERSON> Kredit für diesen Kunden", "add_button": "Hinzufügen"}, "subscriptions": {"title": "Abonnements", "list": {"plan_name": "Plan", "status": "Status", "schedule": "Zeitplan", "created_at": "Erstellt am", "valid_until": "Nächste Abrechnung", "view": "<PERSON><PERSON><PERSON>"}, "add_new": "Neues Abonnement hinzufügen", "no_subscriptions": "Keine Abonnements"}, "subscription_events": {"title": "Ereignisse im Abonnement", "list": {"event": "Veranstaltung", "subscription": "Abonnement", "created_at": "Erstellt am"}, "no_subscription_events": "<PERSON>ine Abonnement-Ereignisse"}, "payments": {"title": "Zahlungen", "list": {"amount": "Betrag", "currency": "Währung", "status": "Status", "created_at": "Erstellt am"}, "no_payments": "Noch keine Zahlungen für diesen Kunden"}, "refunds": {"title": "Erstattungen", "list": {"amount": "Betrag", "currency": "Währung", "created_by": "<PERSON><PERSON><PERSON><PERSON> von", "created_at": "Erstellt am"}, "no_refunds": "<PERSON><PERSON> Rückerstattung für diesen Kunden"}, "payment_details": {"title": "Details zur Bezahlung", "list": {"brand": "<PERSON><PERSON>", "last_four": "Letzte Vier", "default": "Zahlungsverzug", "expiry_month": "Verfallsmonat", "expiry_year": "Verfallsjahr", "name": "Name"}, "add_token": "<PERSON><PERSON>", "add_new": "<PERSON><PERSON>", "no_payment_details": "<PERSON><PERSON>", "delete": "Löschen", "make_default": "Standard machen"}, "limits": {"title": "Grenzwerte", "list": {"feature": "Merkmal", "limit": "<PERSON><PERSON><PERSON>"}, "no_limits": "<PERSON><PERSON>"}, "features": {"title": "Eigenschaften", "list": {"feature": "Merkmal"}, "no_features": "<PERSON><PERSON>"}, "invoices": {"title": "Re<PERSON><PERSON>ngen", "list": {"amount": "Betrag", "currency": "Währung", "status": "Status", "outstanding": "<PERSON><PERSON><PERSON><PERSON>", "overdue": "Überfällig", "paid": "Be<PERSON>hlt", "created_at": "Erstellt am", "view_btn": "<PERSON><PERSON><PERSON>"}, "no_invoices": "<PERSON><PERSON>", "next": "<PERSON><PERSON>", "prev": "<PERSON><PERSON><PERSON><PERSON>"}, "invoice_delivery": {"title": "Liefer<PERSON> von <PERSON>n", "add_new": "<PERSON><PERSON>", "list": {"method": "<PERSON>e", "format": "Format", "detail": "Einzelheiten", "view": "<PERSON><PERSON><PERSON>"}, "no_delivery_methods": "<PERSON><PERSON>"}, "metric_counters": {"title": "Metrische Z<PERSON>hler", "list": {"name": "Name", "usage": "Verwendung", "cost": "Geschätzte Kosten"}, "no_counters": "<PERSON><PERSON> gibt keine met<PERSON><PERSON> Zähler"}, "usage_limits": {"title": "Grenzen der Nutzung", "add_new": "<PERSON><PERSON>", "list": {"amount": "Betrag", "warn_level": "Aktion"}, "warn_levels": {"warn": "<PERSON><PERSON><PERSON>", "disable": "Deaktivieren Sie"}, "no_limits": "<PERSON><PERSON><PERSON> diesen Kunden gibt es keine Nutzungsbeschränkungen"}, "metadata": {"title": "<PERSON><PERSON><PERSON>", "no_metadata": "<PERSON><PERSON>"}, "audit_log": "Audit-Protokoll"}, "update": {"title": "Update Kunde", "email": "E-Mail", "street_line_one": "Straße Linie 1", "street_line_two": "Straße Linie 2", "city": "Stadt", "region": "Region", "country": "Land", "post_code": "<PERSON><PERSON><PERSON><PERSON>", "reference": "<PERSON><PERSON><PERSON><PERSON>", "company_name": "Name des Unternehmens", "external_reference": "Externe Referenz", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "<PERSON>ktual<PERSON><PERSON>", "show_advanced": "Gefahrenzone", "success_message": "Erfolgreiche Aktualisierung des Kunden", "address_title": "<PERSON><PERSON><PERSON>", "tax_number": "Umsatzsteuer-Identifikationsnummer", "standard_tax_rate": "Standard-Steuersatz", "locale": "Gebietsschema", "error": {"not_found": "<PERSON><PERSON> solcher Kunde gefunden", "unknown": "Ein unbekannter Fehler ist aufgetreten"}, "billing_type": "Art der Abrechnung", "billing_type_card": "<PERSON><PERSON>", "billing_type_invoice": "<PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>", "type_business": "Business", "type_individual": "<PERSON><PERSON><PERSON>", "help_info": {"email": "Die E-Mail für den Kunden, an den die Rechnungen gehen sollen", "locale": "Das für die Sprache zu verwendende Gebietsschema", "company_name": "Der Name des Unternehmens", "street_line_one": "Die erste Zeile der Rechnungsadresse", "street_line_two": "Die zweite Zeile der Rechnungsadresse", "city": "Die Stadt für die Rechnungsadresse", "region": "Die Region/das Bundesland für die Rechnungsadresse", "country": "Das Rechnungsland des Kunden - ISO 3166-1 Alpha-2 Ländercode.", "post_code": "Die Postleitzahl für die Rechnungsadresse", "reference": "Ihre interne Referenz für den Kunden", "billing_type": "Wie dem Kunden die Rechnung gestellt werden soll. <PERSON><PERSON> bedeutet, dass die Zahlungen automatisch über eine registrierte Karte erfolgen. Rechnung bedeutet, dass der Kunde eine Rechnung erhält und manuell bezahlt", "external_reference": "Die Referenz für den Kunden, die vom Zahlungsanbieter verwendet wird. <PERSON><PERSON> sie leer, es sei denn, <PERSON><PERSON> sind sehr sicher, dass Sie die richtige Referenz haben.", "tax_number": "Die Umsatzsteuer-Identifikationsnummer des Kunden", "standard_tax_rate": "Der für den Kunden anzuwendende Steuersatz für alles außer digitalen Dienstleistungen", "type": "Wenn der Kunde ein Unternehmen oder eine Privatperson ist", "invoice_format": "Das Format, das bei der Erstellung und Übermittlung einer Rechnung zu verwenden ist", "marketing_opt_in": "<PERSON>n sich der Kunde für Marketing-E-Mails entschieden hat. Dies betrifft Newsletter-Integrationen."}, "invoice_format": "Rechnungsformat", "marketing_opt_in": "Marketing Opt-In", "metadata": {"title": "<PERSON><PERSON><PERSON>", "name": "Name", "value": "Wert", "no_values": "<PERSON><PERSON>", "add": "<PERSON>adaten hinzufügen"}}, "menu": {"title": "<PERSON><PERSON>", "customers": "<PERSON><PERSON>"}}, "product": {"list": {"title": "Produkte", "name": "Name", "physical": "Physisch", "no_products": "Derzeit sind keine Produkte vorhanden", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "filter": {"title": "Filter", "name": "Name", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter"}, "error_message": "Es ist ein Fehler aufgetreten"}, "create": {"title": "Neues Produkt erstellen", "name": "Name", "external_reference": "Externe Referenz", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "Produkt erstellen", "show_advanced": "Fortgeschrittene", "success_message": "Erfolgreich erstelltes Produkt", "failed_message": "Produkt konnte nicht erstellt werden", "tax_rate": "Steuersatz", "tax_type": "Steuerart", "physical": "Physisch", "tax_types": {"digital_services": "Digitale Dienste", "digital_goods": "<PERSON>e Güter", "physical": "Physische Waren/Dienstleistungen"}, "help_info": {"name": "Der Name des Produkts", "external_reference": "Die Referenz für das Produkt, das vom Zahlungsanbieter verwendet wird. <PERSON><PERSON> sie leer, es sei denn, <PERSON><PERSON> sind sehr sicher, dass Sie die richtige Referenz haben.", "tax_type": "Dies soll bei der korrekten Besteuerung helfen. Physische Waren und Dienstleistungen werden anders besteuert als digitale Waren. Und in einigen Ländern gibt es eine Steuer auf digitale Dienstleistungen.", "tax_rate": "Der Steuersatz, der für dieses Produkt verwendet werden soll. Er hat Vorrang vor anderen Steuersätzen.", "physical": "Ist dieses Produkt physisch?"}}, "view": {"title": "Details zum Produkt anzeigen", "update": "Update", "error": {"not_found": "Kein solches Produkt gefunden", "unknown": "Ein unbekannter Fehler ist aufgetreten"}, "main": {"title": "Wichtigste Details", "name": "Name", "physical": "Physisch", "external_reference": "Externe Referenz", "tax_rate": "Steuersatz", "tax_type": "Steuerart", "tax_types": {"digital_services": "Digitale Dienste", "digital_goods": "<PERSON>e Güter", "physical": "Physische Waren/Dienstleistungen"}}, "price": {"title": "<PERSON><PERSON>", "create": "Neuen Preis erstellen", "no_prices": "Derzeit sind keine Preise vorhanden", "hide": "Preis privat machen", "show": "<PERSON><PERSON> machen", "list": {"amount": "Betrag", "currency": "Währung", "recurring": "Wiederkehrende Zahlung", "schedule": "Zahlungszeitplan", "including_tax": "<PERSON><PERSON> inkl. <PERSON>", "public": "Öffentlicher Preis", "external_reference": "Externe Referenz", "usage": "Verwendung"}}, "subscription_plan": {"title": "Abonnement-Pläne", "create": "Neuen Plan erstellen", "no_subscription_plans": "Derzeit gibt es keine Abonnementpläne", "view": "<PERSON><PERSON><PERSON>", "list": {"name": "Name", "external_reference": "Externe Referenz", "code_name": "Code Name"}}}, "update": {"title": "Produkt aktualisieren", "name": "Name", "external_reference": "Externe Referenz", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "<PERSON>ktual<PERSON><PERSON>", "show_advanced": "Fortgeschrittene", "success_message": "Produkt erfolgreich aktualisieren", "address_title": "<PERSON><PERSON><PERSON>", "error": {"not_found": "Kein solches Produkt gefunden", "unknown": "Ein unbekannter Fehler ist aufgetreten"}, "tax_type": "Steuerart", "tax_types": {"digital_services": "Digitale Dienste", "digital_goods": "<PERSON>e Güter", "physical": "Physische Waren/Dienstleistungen"}, "tax_rate": "Steuersatz", "help_info": {"name": "Der Name des Produkts", "external_reference": "Die Referenz für das Produkt, das vom Zahlungsanbieter verwendet wird. <PERSON><PERSON> sie leer, es sei denn, <PERSON><PERSON> sind sehr sicher, dass Sie die richtige Referenz haben.", "tax_type": "Dies soll bei der korrekten Besteuerung helfen. Physische Waren und Dienstleistungen werden anders besteuert als digitale Waren. Und in einigen Ländern gibt es eine Steuer auf digitale Dienstleistungen.", "tax_rate": "Der Steuersatz, der für dieses Produkt verwendet werden soll. Er hat Vorrang vor anderen Steuersätzen."}}, "menu": {"title": "Produkt", "products": "Produkte", "features": "Eigenschaften", "vouchers": "Gutscheine", "products_list": "Produktliste", "metrics": "Metriken"}}, "price": {"create": {"title": "Neuen Preis erstellen", "amount": "Betrag", "external_reference": "Externe Referenz", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "<PERSON><PERSON> er<PERSON>", "show_advanced": "Fortgeschrittene", "success_message": "Erfolgreich erstellter Preis", "schedule_label": "Zahlungszeitplan", "currency": "Währung", "recurring": "Ist A wieder<PERSON>hrend?", "including_tax": "Beinhaltet der Preis die Steuer?", "public": "<PERSON><PERSON><PERSON><PERSON>", "help_info": {"amount": "Der Preis ist die Währung der unteren Ebene. 1,00 USD wäre also 100 und 9,99 wäre 999.", "display_amount": "Dieser Preis würde {amount} betragen.", "external_reference": "Die Referenz für das Produkt, das vom Zahlungsanbieter verwendet wird. <PERSON><PERSON> sie leer, es sei denn, <PERSON><PERSON> sind sehr sicher, dass Sie die richtige Referenz haben.", "recurring": "Ob es sich um eine wiederkehrende Zahlung oder eine einmalige Zahlung handelt.", "currency": "Die Währung, die dem Kunden in Rechnung gestellt werden soll", "schedule": "Wie oft der Kunde belastet werden soll", "including_tax": "Wenn Sie die Steuer im Preis verstecken wollen oder wenn Sie den Kunden die Steuer selbst zahlen lassen wollen", "public": "Wenn es sich um einen öffentlich anzeigbaren Preis handelt", "usage": "Wenn der Kunde auf der Grundlage seines Verbrauchs einer Kennzahl oder nach Sitzplatz abgerechnet wird.", "metric_type": "Wird die Nutzungsmetrik am Ende des Zahlungsplans zurückgesetzt und vollständig für die Rechnungsstellung verwendet oder kontinuierlich und die Differenz zwischen der letzten Rechnung und der nächsten Rechnung verwendet."}, "schedule": {"week": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON><PERSON><PERSON>"}, "metric": "<PERSON><PERSON><PERSON>", "metric_type": "<PERSON><PERSON><PERSON>", "create_metric": "<PERSON><PERSON> müssen eine Metrik erstellen", "metric_types": {"resettable": "Zurücksetzbar", "continuous": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": "<PERSON><PERSON>", "types": {"fixed_price": "Festpreis", "package": "<PERSON><PERSON>", "per_unit": "Pro Einheit/Pro Sitzplatz", "tiered_volume": "Gestaffeltes Volumen", "tiered_graduated": "Gestaffelt Gestaffelt"}, "usage": "Verwendung", "units": "Einheiten", "tiers": "<PERSON><PERSON><PERSON>", "tiers_fields": {"first_unit": "Erste Einheit", "last_unit": "Letzte Einheit", "unit_price": "Preis pro Einheit", "flat_fee": "Pauschalgebühr"}}}, "feature": {"list": {"title": "Eigenschaften", "name": "Name", "code": "Code", "reference": "<PERSON><PERSON><PERSON><PERSON>", "no_features": "Derzeit sind keine Funktionen vorhanden", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter"}, "error_message": "Es ist ein Fehler aufgetreten", "loading": "Ladefunktionen"}, "create": {"title": "Neues Feature erstellen", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "Feature erstellen", "show_advanced": "Fortgeschrittene", "success_message": "Erfolgreich erstelltes Merkmal", "address_title": "<PERSON><PERSON><PERSON>", "fields": {"name": "Name", "code": "Codename", "description": "Beschreibung"}, "help_info": {"name": "Der Name für das Merkmal", "code": "Der Codename für das Merkmal. Dieser wird bei der Registrierung einer Verwendung oder bei der Überprüfung von Grenzwerten verwendet.", "description": "Die Beschreibung für das Merkmal"}}}, "subscription_plan": {"create": {"title": "Neuen Abonnementplan erstellen", "main_section": {"title": "Wichtigste Details", "fields": {"name": "Name", "code_name": "Code Name", "user_count": "<PERSON><PERSON><PERSON> der Benutzer", "public": "Öffentlich zugänglicher Plan", "per_seat": "Pro Sitzplatz", "free": "<PERSON><PERSON><PERSON>"}, "help_info": {"name": "Der Name des Abonnementplans", "code_name": "Der Codename für den Plan, der mit der API verwendet werden soll.", "user_count": "Die Anzahl der für diesen Plan zulässigen Benutzer", "public": "Ist der Plan für die Öffentlichkeit zugänglich oder handelt es sich um einen individuellen Plan?", "free": "Ist dies ein kostenloser Plan?", "per_seat": "Wird der Plan pro Sitzplatz berechnet?"}}, "trial_section": {"title": "Details zur Studie", "fields": {"has_trial": "Hat Versuch", "is_trial_standalone": "Ist die Prüfung eigenständig", "trial_length_days": "Versuchsdauer in Tagen"}, "help_info": {"has_trial": "Wenn der Plan standardmäßig eine Probezeit hat", "trial_length_days": "Wie lange die Prüfung dauern soll (in Tagen)", "is_trial_standalone": "Wenn eine Testversion eigenständig ist, braucht sie keinen Preis und das Abonnement endet am Ende der Testphase"}}, "features_section": {"title": "Eigenschaften", "columns": {"feature": "Merkmal", "description": "Beschreibung"}, "create": {"name": "Name", "code_name": "Code Name", "description": "Beschreibung", "button": "<PERSON><PERSON><PERSON><PERSON>"}, "add_feature": "Hinzufügen", "existing": "Vorhandene Merkmale", "new": "<PERSON><PERSON>", "no_features": "<PERSON><PERSON>"}, "limits_section": {"title": "Grenzwerte", "columns": {"limit": "<PERSON><PERSON><PERSON>", "feature": "Merkmal", "description": "Beschreibung"}, "fields": {"limit": "<PERSON><PERSON><PERSON>", "feature": "Merkmal"}, "add_limit": "Hinzufügen", "no_limits": "<PERSON><PERSON>"}, "prices_section": {"title": "<PERSON><PERSON>", "columns": {"amount": "Betrag", "currency": "Währung", "schedule": "Zeitplan"}, "create": {"amount": "Betrag", "currency": "Währung", "recurring": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schedule": "Zeitplan", "including_tax": "Einsch<PERSON><PERSON><PERSON> Steuer", "public": "<PERSON><PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON>"}, "add_price": "Hinzufügen", "existing": "Bestehende Preise", "new": "<PERSON><PERSON>", "no_prices": "<PERSON><PERSON>"}, "submit_btn": "Plan erstellen"}, "view": {"title": "Details zum Abonnementplan anzeigen", "update": "Update", "error": {"not_found": "<PERSON><PERSON> solcher Abonnementplan gefunden", "unknown": "Ein unbekannter Fehler ist aufgetreten"}, "main": {"title": "Wichtigste Details", "name": "Name", "code_name": "Code Name", "per_seat": "Pro Sitzplatz", "free": "<PERSON><PERSON><PERSON>", "user_count": "<PERSON><PERSON><PERSON> der Benutzer", "public": "<PERSON><PERSON><PERSON><PERSON> zugänglich", "has_trial": "Hat Versuch", "trial_length_days": "Dauer der Prüfung", "is_trial_standalone": "Ist die Studie eigenständig?"}, "limits": {"title": "Grenzwerte", "list": {"feature": "Merkmal", "limit": "<PERSON><PERSON><PERSON>", "no_limits": "<PERSON><PERSON>"}}, "features": {"title": "Eigenschaften", "list": {"feature": "Merkmal", "no_features": "<PERSON><PERSON>"}}, "price": {"title": "<PERSON><PERSON>", "list": {"amount": "Betrag", "currency": "Währung", "recurring": "Wiederkehrende Zahlung", "schedule": "Zahlungszeitplan", "including_tax": "<PERSON><PERSON> inkl. <PERSON>", "public": "Öffentlicher Preis", "external_reference": "Externe Referenz", "usage": "Verwendung"}}}, "update": {"title": "Abonnementplan aktualisieren", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "Abonnementplan aktualisieren", "show_advanced": "Fortgeschrittene", "success_message": "Erfolgreich aktualisierter Abonnementplan", "address_title": "<PERSON><PERSON><PERSON>", "fields": {"name": "Name", "code_name": "Code Name", "user_count": "<PERSON><PERSON><PERSON> der Benutzer", "public": "Öffentlich zugänglicher Plan", "per_seat": "Pro Sitzplatz", "free": "<PERSON><PERSON><PERSON>", "prices": "<PERSON><PERSON>", "features": "Eigenschaften", "limits": "Grenzwerte", "has_trial": "Hat Versuch", "trial_length_days": "Dauer der Prüfung", "is_trial_standalone": "Ist die Studie eigenständig?"}, "help_info": {"name": "Der Name für den Plan", "code_name": "Der Codename für den Plan, der mit der API verwendet werden soll.", "user_count": "Die Anzahl der für diesen Plan zulässigen Benutzer", "public": "Ist der Plan für die Öffentlichkeit zugänglich oder handelt es sich um einen individuellen Plan?", "free": "Ist dies ein kostenloser Plan?", "per_seat": "Wird der Plan pro Sitzplatz berechnet?", "has_trial": "Wenn der Plan standardmäßig eine Probezeit hat", "trial_length_days": "Wie lange die Prüfung dauern soll (in Tagen)", "is_trial_standalone": "Wenn eine Testversion eigenständig ist, braucht sie keinen Preis und das Abonnement endet am Ende der Testphase"}, "features": {"title": "Eigenschaften", "add_feature": "Merkmal hinzufügen"}, "limits": {"title": "Grenzwerte", "add_limit": "Limits hinzufügen"}, "prices": {"title": "<PERSON><PERSON>", "add_price": "<PERSON><PERSON> hi<PERSON>"}}, "menu": {"subscription_plans": "Abonnement-Pläne", "products": "Produkte", "features": "Eigenschaften"}}, "payment_details": {"add": {"title": "Zahlungsdetails hinzufügen"}, "add_with_token": {"title": "Zahlungsdetails mit Token hinzufügen", "field": {"token": "Token"}, "help_info": {"token": "Das von Stripe bereitgestellte Token."}, "submit": "Einreichen"}}, "subscription": {"create": {"title": "Neues Abonnement erstellen", "subscription_plans": "Abonnement-Pläne", "payment_details": "Details zur Bezahlung", "no_eligible_prices": "<PERSON>s gibt keine förderfähigen Preise", "prices": "<PERSON><PERSON>", "success_message": "Erfolgreich erstelltes Abonnement", "submit_btn": "<PERSON><PERSON><PERSON><PERSON>", "trial": "Kostenlose Testversion", "trial_length_days": "Anzahl der Tage", "unknown_error": "Bei der Erstellung ist ein unbekannter Fehler aufgetreten", "seats": "<PERSON><PERSON><PERSON> der <PERSON>", "help_info": {"eligible_prices": "Wenn ein Kunde bereits ein aktives Abonnement hat, müssen alle neuen Abonnements für denselben Abrechnungszeitraum und dieselbe Währung gelten.", "trial": "Wenn ein Kunde bereits ein aktives Abonnement hat, hat er keinen Anspruch auf eine weitere kostenlose Testphase.", "no_trial": "Dieser Plan hat keine kostenlose Testphase", "seats": "<PERSON> Anzahl der Plätze, für die das Abonnement gelten soll"}}, "view": {"title": "Ansicht Abonnement", "main": {"title": "Abonnement-Daten", "status": "Status", "plan": "Plan", "plan_change": "Plan ändern", "customer": "Kunde", "main_external_reference": "Wichtigste externe Referenz", "created_at": "Erstellt am", "ended_at": "Beendet am", "valid_until": "Gültig bis", "seat_number": "Sitznummer", "change_seat": "<PERSON><PERSON> wechseln"}, "pricing": {"title": "Preisgestaltung", "price": "Pre<PERSON>", "recurring": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schedule": "Zeitplan", "change": "Ändern Sie", "no_price": "<PERSON><PERSON> gibt kein Preis für das Abonnement festgelegt"}, "payments": {"title": "Zahlungen", "amount": "Betrag", "created_at": "Erstellt am", "view": "<PERSON><PERSON><PERSON>", "no_payments": "<PERSON>s gibt noch keine Zahlungen"}, "payment_method": {"title": "Zahlungsmethode", "last_four": "Letzte Vier", "expiry_month": "Verfallsmonat", "expiry_year": "Verfallsjahr", "brand": "<PERSON><PERSON>", "invoiced": "In Rechnung gestellt"}, "subscription_events": {"title": "Ereignisse im Abonnement", "list": {"event": "Veranstaltung", "subscription": "Abonnement", "created_at": "Erstellt am"}, "no_subscription_events": "<PERSON>ine Abonnement-Ereignisse"}, "buttons": {"cancel": "Abbrechen", "payment_method": "Zahlungsdetails aktualisieren", "audit_log": "Audit-Protokoll"}, "modal": {"seats": {"seats": "<PERSON><PERSON>", "seats_help": "Die Anzahl der Sitze für den Plan", "submit": "Speichern Sie"}, "price": {"price": "<PERSON><PERSON><PERSON>", "price_help": "Der neue Preis, der bei der nächsten Rechnung berechnet wird", "submit": "Update"}, "plan": {"plan": "Neuer Plan", "plan_help": "Der Plan, auf den Si<PERSON> dieses Abonnement ändern möchten", "price": "<PERSON><PERSON><PERSON>", "price_help": "Der neue Preis, der bei der nächsten Rechnung berechnet wird", "submit": "Update", "when": {"title": "<PERSON><PERSON>", "next_cycle": "F<PERSON>r den nächsten Abrechnungszyklus verwenden", "instantly": "Unmittelbar", "specific_date": "Bestimmtes Datum"}}, "payment_method": {"payment_method": "Zahlungsdetails verwenden", "payment_method_help": "Diese Angaben werden für die nächste Abrechnung mit dem Kunden verwendet.", "update_button": "Zahlungsdetails aktualisieren", "submit": "Update"}, "cancel": {"title": "Abonnement kündigen", "cancel_btn": "Bestätigen Sie", "close_btn": "Schließen Sie", "when": {"title": "<PERSON><PERSON>", "end_of_run": "Ende des aktuellen Abrechnungszeitraums", "instantly": "Unmittelbar", "specific_date": "Bestimmtes Datum"}, "refund_type": {"title": "Erstattungsart", "none": "<PERSON><PERSON>", "prorate": "Anteilige Erstattung auf der Grundlage der Nutzung", "full": "Volle Rückerstattung"}, "cancelled_message": "Erfolg<PERSON><PERSON>"}}, "usage_estimate": {"title": "Nutzung Schätzung Kosten", "usage": "Verwendung", "estimate_cost": "Kostenvoranschlag", "metric": "<PERSON><PERSON><PERSON>"}, "metadata": {"title": "<PERSON><PERSON><PERSON>", "no_metadata": "<PERSON><PERSON>"}}, "list": {"title": "Abonnements", "email": "Kunde", "status": "Status", "plan": "Plan", "no_subscriptions": "Derzeit gibt es keine Abonnements", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "view": "<PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter"}, "error_message": "Es ist ein Fehler aufgetreten", "filters": {"status": "Status", "status_choices": {"cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "active": "Aktiv", "blocked": "<PERSON><PERSON><PERSON>", "overdue_payment_open": "Überfällige Zahlung Offen", "trial_active": "Aktive Prüfung", "trial_ended": "Versuch beendet"}}, "loading": "<PERSON><PERSON> von Abonnements..."}, "menu": {"title": "Abonnements", "subscriptions": "Abonnements", "mass_change": "Massenänderung", "subscriptions_list": "Liste der Abonnements"}, "mass_change": {"list": {"title": "Abonnements - Massenänderung", "change_date": "<PERSON><PERSON>", "status": "Status", "created_at": "Erstellt am", "no_mass_change": "Derzeit gibt es keine Massenänderungen bei den Abonnements", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "view": "<PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter"}, "error_message": "Es ist ein Fehler aufgetreten"}, "create": {"title": "Massenhafte Veränderungen schaffen", "criteria": {"title": "Kriterien", "plan": "Plan", "price": "Pre<PERSON>", "brand": "<PERSON><PERSON>", "country": "Land"}, "new": {"title": "Neue Werte", "plan": "Neuer Plan", "price": "<PERSON><PERSON><PERSON>"}, "change_date": {"title": "<PERSON><PERSON>", "help_info": "Nach dem Änderungsdatum werden alle Verlängerungen zum neuen Preis durchgeführt. Der Abonnementplan wird sofort geändert."}, "estimate": {"amount": "Dies führt zu einer geschätzten {amount} {currency} Änderung des {schedule}"}, "submit_button": "Schaltfläche \"Senden"}, "view": {"title": "Massenabonnement ändern", "criteria": {"title": "Kriterien", "plan": "Plan", "price": "Pre<PERSON>", "brand": "<PERSON><PERSON>", "country": "Land"}, "new_values": {"title": "Neue Werte", "plan": "Plan", "price": "Pre<PERSON>"}, "change_date": {"title": "<PERSON><PERSON>"}, "estimate": {"amount": "Dies führt zu einer geschätzten {amount} {currency} Änderung des {schedule}"}, "export_button": "Kundenliste exportieren", "cancel": "Abbrechen", "uncancel": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen"}}}, "payment": {"list": {"title": "Zahlungen", "no_payments": "Derzeit gibt es keine Zahlungen", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "list": {"amount": "Betrag", "currency": "Währung", "customer": "Kunde", "status": "Status", "created_at": "Erstellt am"}, "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter"}, "error_message": "Es ist ein Fehler aufgetreten"}, "view": {"title": "Details zur Bezahlung", "main": {"title": "Wichtigste Details", "amount": "Betrag", "currency": "Währung", "external_reference": "Externe Referenz", "status": "Status", "created_at": "Erstellt am"}, "customer": {"title": "Kunde", "email": "E-Mail", "more_info": "Me<PERSON> Infos", "country": "Land", "attach": "Dem Kunden beifügen"}, "refunds": {"title": "Erstattungen", "amount": "Betrag", "reason": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON> von", "created_at": "Erstellt am", "none": "<PERSON>ine Erstattungen gefunden"}, "subscriptions": {"title": "Abonnements", "plan_name": "Plan Name", "more_info": "Me<PERSON> Infos", "none": "Zahlung nicht an Abonnements gebunden"}, "receipts": {"title": "Quittungen", "created_at": "Erstellt am", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "none": "Zahlung hat keine <PERSON>g"}, "buttons": {"refund": "Ausgabe Erstattung", "generate_receipt": "<PERSON><PERSON><PERSON><PERSON> generieren"}, "modal": {"attach": {"title": "An den Kunden anhängen", "button": "Anhängen"}, "refund": {"title": "Erstattung", "amount": {"title": "Betrag", "help_info": "Dies ist der kleinere Währungsbetrag. 100 USD sind also 1,00 USD."}, "reason": {"title": "<PERSON><PERSON><PERSON>"}, "submit": "Ausgabe Erstattung", "success_message": "Erstattung erfolgreich erstellt", "error_message": "Etwas ist schief gelaufen"}}}}, "refund": {"list": {"title": "Erstattungen", "no_refunds": "Derzeit gibt es keine Erstattungen", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "list": {"amount": "Betrag", "currency": "Währung", "customer": "Kunde", "status": "Status", "created_by": "<PERSON><PERSON><PERSON><PERSON> von", "created_at": "Erstellt am"}, "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter"}, "error_message": "Es ist ein Fehler aufgetreten"}, "view": {"title": "Details zur Erstattung", "main": {"title": "Wichtigste Details", "amount": "Betrag", "currency": "Währung", "external_reference": "Externe Referenz", "status": "Status", "created_at": "Erstellt am"}, "buttons": {"refund": "Ausgabe Erstattung"}, "modal": {"refund": {"title": "Erstattung", "amount": {"title": "Betrag", "help_info": "Dies ist der kleinere Währungsbetrag. 100 USD sind also 1,00 USD."}, "reason": {"title": "<PERSON><PERSON><PERSON>"}, "submit": "Ausgabe Erstattung"}}}}, "transactions": {"menu": {"title": "Transaktionen", "payments": "Zahlungen", "refunds": "Erstattungen", "charge_backs": "Rückseiten aufladen", "invoices": "Re<PERSON><PERSON>ngen", "unpaid_invoices": "Unbezahlte Rechnungen", "checkout": "<PERSON><PERSON>", "countries": "<PERSON><PERSON><PERSON>", "tax_types": "Steuerarten"}}, "settings": {"menu": {"title": "Einstellungen", "user_settings": "Benutzereinstellungen", "invite": "Einladen", "pdf_templates": "PDF-Vorlagen", "email_templates": "E-Mail-Vorlagen", "tax_settings": "Steuerliche Einstellungen", "brand_settings": "Marke Einstellungen", "notification_settings": "Einstellungen für Benachrichtigungen", "system_settings": "System-Einstellungen", "users": "<PERSON><PERSON><PERSON>", "stripe": "Streifen", "api_keys": "API-Schlüssel", "exchange_rates": "Wechselkurse", "integrations": "Integrationen", "vat_sense": "VatSense", "audit_log": "Audit-Protokoll"}, "pdf_template": {"list": {"title": "Vorlage", "name": "Name", "locale": "Gebietsschema", "brand": "<PERSON><PERSON>", "create_btn": "<PERSON><PERSON><PERSON><PERSON>", "edit_btn": "<PERSON><PERSON><PERSON>", "no_templates": "<PERSON><PERSON>", "error_message": "Es ist ein Fehler aufgetreten", "generator": "Generatoreinstellungen aktualisieren"}, "update": {"title": "Vorlage aktualisieren - {name}", "content": "Inhalt", "save": "Speichern", "download": "Test PDF herunterladen", "template": "Vorlage", "help_info": {"template": "Verwendung der Twig-Vorlagensprache", "variable_docs": "Überprüfen Sie in der Dokumentation, welche Variablen verfügbar sind"}}, "generator_settings": {"title": "PDF-Generator-Einstellungen", "generator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tmp_dir": "Das temporäre Verzeichnis", "api_key": "Api-Schlüssel", "bin": "Speicherort", "submit": "Speichern Sie", "help_info": {"generator": "Der zu verwendende Generator. <PERSON><PERSON> <PERSON> nicht sicher sind, verwenden Sie mpdf", "tmp_dir": "Das zu verwendende temporäre Verzeichnis. Wenn Si<PERSON> nicht sicher sind, verwenden Sie /tmp", "api_key": "Der zu verwendende API-Schlüssel", "bin": "Der Standort von wkhtmltopdf"}}, "create": {"title": "Vor<PERSON> er<PERSON>", "content": "Inhalt", "save": "Speichern", "download": "Test PDF herunterladen", "template": "Vorlage", "locale": "Gebietsschema", "type": "<PERSON><PERSON>", "brand": "<PERSON><PERSON>", "help_info": {"locale": "Das Gebietsschema, für das die PDF-Vorlage bestimmt ist", "brand": "<PERSON> Marke, für die die PDF-Vorlage bestimmt ist", "type": "Die Art der PDF-<PERSON><PERSON>, für die die Vorlage bestimmt ist", "template": "Verwendung der Twig-Vorlagensprache", "variable_docs": "Überprüfen Sie in der Dokumentation, welche Variablen verfügbar sind"}}}, "brand_settings": {"list": {"title": "Marke Einstellungen", "name": "Name", "edit_btn": "<PERSON><PERSON><PERSON>", "no_brands": "<PERSON><PERSON> gibt keine <PERSON>", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "error_message": "Es ist ein Fehler aufgetreten"}, "update": {"title": "Markeneinstellungen aktualisieren - {name}", "fields": {"name": "Name", "email": "E-Mail Adresse", "company_name": "Name des Unternehmens", "street_line_one": "Straße Linie 1", "street_line_two": "Straße Linie 2", "city": "Stadt", "region": "Region", "country": "Land", "postcode": "<PERSON><PERSON><PERSON><PERSON>", "code": "Code", "tax_number": "Umsatzsteuer-Identifikationsnummer", "tax_rate": "Steuersatz", "digital_services_tax_rate": "Steuersatz für digitale Dienstleistungen", "support_email": "Unterstützung E-Mail", "support_phone_number": "Support-Telefonnummer"}, "help_info": {"name": "Der Name der Marke", "code": "Der Code, der zur Identifizierung der Marke bei API-Aufrufen verwendet wird. Dieser kann nicht aktualisiert werden.", "email": "Die E-Mail, die für den Versand von E-Mails an Markenkunden verwendet werden soll", "company_name": "Der Firmenname für die Rechnungsstellung", "street_line_one": "Die erste Zeile der Rechnungsadresse", "street_line_two": "Die zweite Zeile der Rechnungsadresse", "city": "Die Stadt für die Rechnungsadresse", "region": "Die Region/das Bundesland für die Rechnungsadresse", "country": "Das Rechnungsland des Kunden - ISO 3166-1 Alpha-2 Ländercode.", "postcode": "Die Postleitzahl für die Rechnungsadresse", "tax_number": "Die Umsatzsteuer-Identifikationsnummer für das Unternehmen/die Marke", "tax_rate": "Der Steuersatz, der für Ihr Heimatland zu verwenden ist oder wenn kein anderer Steuersatz gefunden werden kann", "digital_services_tax_rate": "Der Steuersatz, der für Ihr Heimatland zu verwenden ist oder wenn kein anderer Steuersatz für digitale Dienstleistungen gefunden werden kann", "support_email": "Die E-Mail-Adresse für den Supportkontakt", "support_phone_number": "Die Telefonnummer für den Supportkontakt"}, "general": "Allgemeine Einstellungen", "notifications": "Benachrichtigungen", "address_title": "Re<PERSON>nungsadress<PERSON>", "success_message": "<PERSON>ktual<PERSON><PERSON>", "submit_btn": "Update", "notification": {"subscription_creation": "Erstellung von Abonnements", "subscription_cancellation": "Kündigung des Abonnements", "expiring_card_warning": "Warnung bei ablaufender Karte", "expiring_card_warning_day_before": "Warnung vor ablaufender Karte - Tag davor", "invoice_created": "Rechnung erstellt", "invoice_overdue": "Überfällige Rechnung", "quote_created": "<PERSON><PERSON><PERSON>", "trial_ending_warning": "Warnung vor dem Ende des Prozesses", "before_charge_warning": "Warnung vor dem Aufladen", "before_charge_warning_options": {"none": "<PERSON><PERSON>", "all": "Alle", "yearly": "<PERSON><PERSON><PERSON><PERSON>"}, "payment_failure": "Fehlgeschlagene Zahlung"}, "support": "Kontaktinformationen zum Support"}, "create": {"title": "Markeneinstellungen erstellen", "fields": {"name": "Name", "email": "E-Mail Adresse", "company_name": "Name des Unternehmens", "street_line_one": "Straße Linie 1", "street_line_two": "Straße Linie 2", "city": "Stadt", "region": "Region", "country": "Land", "post_code": "<PERSON><PERSON><PERSON><PERSON>", "code": "Code", "tax_number": "Umsatzsteuer-Identifikationsnummer", "tax_rate": "Steuersatz", "digital_services_tax_rate": "Steuersatz für digitale Dienstleistungen", "support_email": "Unterstützung E-Mail", "support_phone_number": "Support-Telefonnummer"}, "help_info": {"name": "Der Name der Marke", "code": "Der Code, der zur Identifizierung der Marke bei API-Aufrufen verwendet wird. Er kann nicht aktualisiert werden. Meistens handelt es sich um einen alphanumerischen Kleinbuchstaben mit Unterstrichen.", "tax_number": "Die Umsatzsteuer-Identifikationsnummer für die Marke/das Unternehmen", "email": "Die E-Mail, die für den Versand von E-Mails an Markenkunden verwendet werden soll", "company_name": "Der Firmenname für die Rechnungsstellung", "street_line_one": "Die erste Zeile der Rechnungsadresse", "street_line_two": "Die zweite Zeile der Rechnungsadresse", "city": "Die Stadt für die Rechnungsadresse", "region": "Die Region/das Bundesland für die Rechnungsadresse", "country": "Das Rechnungsland des Kunden - ISO 3166-1 Alpha-2 Ländercode.", "postcode": "Die Postleitzahl für die Rechnungsadresse", "tax_rate": "Der Steuersatz, der für Ihr Heimatland verwendet werden soll oder wenn kein anderer Steuersatz gefunden werden kann", "digital_services_tax_rate": "Der Steuersatz, der für Ihr Heimatland zu verwenden ist oder wenn kein anderer Steuersatz für digitale Dienstleistungen gefunden werden kann", "support_email": "Die E-Mail-Adresse für den Supportkontakt", "support_phone_number": "Die Telefonnummer für den Supportkontakt"}, "address_title": "Re<PERSON>nungsadress<PERSON>", "success_message": "<PERSON>ktual<PERSON><PERSON>", "submit_btn": "<PERSON><PERSON><PERSON><PERSON>", "support": "Kontaktinformationen zum Support"}}, "email_template": {"list": {"title": "E-Mail-Vorlagen", "email": "E-Mail", "country": "Land", "reference": "<PERSON><PERSON><PERSON><PERSON>", "brand": "<PERSON><PERSON>", "no_customers": "Es sind derzeit keine E-Mail-Vorlagen vorhanden", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "locale": "Gebietsschema", "view_btn": "<PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter"}, "error_message": "Es ist ein Fehler aufgetreten"}, "create": {"title": "E-Mail-Vorlage erstellen", "fields": {"name": "Name", "locale": "Gebietsschema", "use_emsp_template": "EMSP-Vorlage verwenden", "subject": "<PERSON>a", "template_body": "<PERSON><PERSON><PERSON>", "template_id": "Vorlage-ID", "brand": "<PERSON><PERSON>"}, "help_info": {"name": "Für welche E-Mail ist diese Vorlage gedacht?", "locale": "Für welches Gebietsschema ist diese Vorlage gedacht?", "use_emsp_template": "Wenn das Vorlagensystem des von Ihnen verwendeten E-Mail-Anbieters verwendet werden soll. Wenn <PERSON> unsicher sind, lassen Sie das Häkchen weg", "subject": "<PERSON> Nachricht, die in der Betreffzeile erscheinen soll", "template_body": "Die TWIG-Vor<PERSON>, die verwendet werden soll, um die HTML-Datei für die E-Mail zu erstellen.", "template_id": "Die Vorlagen-ID, die Sie von Ihrem E-Mail-Dienstanbieter erhalten haben, bei dem Sie die Vorlage erstellt haben. Wenn Si<PERSON> unsicher sind, deaktivieren Sie emsp-Vorlage verwenden.", "brand": "<PERSON> Marke, für die die E-Mail-Vorlage bestimmt ist.", "variable_docs": "Überprüfen Sie in der Dokumentation, welche Variablen verfügbar sind"}, "submit_btn": "<PERSON><PERSON><PERSON><PERSON>", "success_message": "Erfolgreich erstellte E-Mail-Vorlage"}, "update": {"title": "E-Mail-Vorlage aktualisieren", "fields": {"name": "Name", "locale": "Gebietsschema", "use_emsp_template": "EMSP-Vorlage verwenden", "subject": "<PERSON>a", "template_body": "<PERSON><PERSON><PERSON>", "template_id": "Vorlage-ID"}, "help_info": {"name": "Für welche E-Mail ist diese Vorlage gedacht?", "locale": "Für welches Gebietsschema ist diese Vorlage gedacht?", "use_emsp_template": "Wenn das Vorlagensystem des von Ihnen verwendeten E-Mail-Anbieters verwendet werden soll. Wenn <PERSON> unsicher sind, lassen Sie das Häkchen weg", "subject": "<PERSON> Nachricht, die in der Betreffzeile erscheinen soll", "template_body": "Die TWIG-Vor<PERSON>, die verwendet werden soll, um die HTML-Datei für die E-Mail zu erstellen.", "template_id": "Die Vorlagen-ID, die Sie von Ihrem E-Mail-Dienstanbieter erhalten haben, bei dem Sie die Vorlage erstellt haben. Wenn Si<PERSON> unsicher sind, deaktivieren Sie emsp-Vorlage verwenden.", "variable_docs": "Überprüfen Sie in der Dokumentation, welche Variablen verfügbar sind"}, "submit_btn": "Update", "success_message": "Erfolgreich aktualisierte E-Mail-Vorlage", "test_email": "Test-E-Mail senden"}}, "notification_settings": {"update": {"title": "Einstellungen für Benachrichtigungen", "submit_btn": "Update", "success_message": "Aktualisierte Benachrichtigungseinstellungen", "fields": {"send_customer_notifications": "Kundenbenachrichtigungen senden", "emsp": "E-Mail-Dienstanbieter", "emsp_api_key": "E-Mail-Dienstanbieter - API-Schlüssel", "emsp_api_url": "E-Mail-Dienstanbieter - API URL", "emsp_domain": "E-Mail-Dienstanbieter - Domäne", "default_outgoing_email": "Standardmäßige ausgehende E-Mail"}, "help_info": {"emsp": "Welchen E-Mail-Anbieter Sie verwenden möchten. Wenn Si<PERSON> nicht sicher sind, verwenden Sie System.", "emsp_api_key": "Der API-Schlüssel, der vom E-Mail-Dienstanbieter bereitgestellt wird.", "emsp_api_url": "Die vom E-Mail-Dienstanbieter bereitgestellte API-URL.", "emsp_domain": "Die Domäne des E-Mail-Dienstanbieters.", "send_customer_notifications": "<PERSON><PERSON> <PERSON>, dass BillaBear Benachrichtigungen an Kunden sendet, z.B. über die Erstellung eines Abonnements, eine Pause, einen Zahlungseingang, etc.", "default_outgoing_email": "Die Standard-E-Mail-Ad<PERSON>e, die für den Versand von Benachrichtigungen verwendet wird, wenn keine Markeneinstellungen vorhanden sind"}}}, "system_settings": {"update": {"title": "System-Einstellungen", "submit_btn": "Update", "success_message": "Aktualisierte Systemeinstellungen", "fields": {"system_url": "System-URL", "timezone": "Zeitzone", "invoice_number_generation": "Generierung von Rechnungsnummern", "subsequential_number": "Fortlaufende Nummer", "default_invoice_due_time": "Standard-Rechnungsfälligkeitszeit", "format": "Format", "invoice_generation": "Erstellung von <PERSON>n"}, "help_info": {"system_url": "Die Basis-URL, unter der BillaBear zu finden ist.", "timezone": "Die Standardzeitzone für das System", "invoice_number_generation": "Wie die Rechnungsnummer generiert wird. Random ist eine zufällige Zeichenkette und subsequent bedeutet, dass es eine Zahl ist, die inkrementiert", "subsequential_number": "Die zuletzt verwendete Rechnungsnummer. Die nächste Rechnungsnummer wird eine Stelle höher sein", "default_invoice_due_time": "Wie viel Zeit zwischen der Rechnungserstellung und dem Fälligkeitsdatum liegt", "format": "Das Format, das für die Generierung von Rechnungsnummern verwendet werden soll. %S ist die fortlaufende Nummer und %R steht für 8 zufällige Zeichen.", "invoice_generation": "Wenn neue Rechnungen für Abonnements erstellt werden sollen"}, "invoice_number_generation": {"random": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subsequential": "Nachfolgend", "format": "Format"}, "default_invoice_due_time": {"30_days": "30 Tage", "60_days": "60 Tage", "90_days": "90 Tage", "120_days": "120 Tage"}, "invoice_generation_types": {"periodically": "Regel<PERSON><PERSON><PERSON><PERSON>", "end_of_month": "<PERSON><PERSON> des Monats"}}}, "user": {"list": {"title": "<PERSON><PERSON><PERSON>", "email": "E-Mail", "roles": "<PERSON><PERSON>", "reference": "<PERSON><PERSON><PERSON><PERSON>", "no_customers": "Derzeit sind keine Kunden vorhanden", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "invite": "Neuen Benutzer einladen", "next": "Nächste Seite", "prev": "Vorherige Seite", "view_btn": "<PERSON><PERSON><PERSON>", "list": {"email": "E-Mail", "role": "<PERSON><PERSON>"}, "invite_title": "Einladungen", "invite_list": {"email": "E-Mail", "sent_at": "Gesendet am", "role": "<PERSON><PERSON>", "copy_link": "<PERSON>", "copied_link": "<PERSON><PERSON><PERSON>"}, "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter"}, "error_message": "Es ist ein Fehler aufgetreten", "audit_log": "Audit-Protokoll"}, "update": {"title": "Benutzer aktualisieren", "fields": {"email": "E-Mail", "roles": "<PERSON><PERSON>"}, "help_info": {"email": "Die E-Mail, die der Benutzer für die Anmeldung und den Empfang von Benachrichtigungen verwenden soll.", "roles": "Worauf sollte der Benutzer Zugriff haben?"}, "submit_btn": "Update", "success_message": "Erfolgreiche Aktualisierung des Benutzers"}}, "stripe": {"main": {"title": "Streifen-Import", "edit_config": "Konfig bearbeiten", "hide_config": "Konfig ausblenden", "start_button": "Schaltfläche \"Import starten", "already_in_progress": "Import wird bereits durchgeführt", "list": {"state": "Staa<PERSON>", "last_id": "Zuletzt verarbeitete Id", "created_at": "Erstellt am", "updated_at": "Aktualisierung am", "no_results": "Bislang wurden keine Streifen importiert.", "view": "<PERSON><PERSON><PERSON>"}, "danger_zone": {"title": "Gefahrenzone", "use_stripe_billing": "Verwenden Sie Stripe Billing, um Kunden zu belasten.", "disable_billing": "Stripe-Abrechnung deaktivieren", "enable_billing": "Stripe-Abrechnung aktivieren"}, "disable_billing_modal": {"title": "Stripe-Abrechnung deaktivieren", "disable_all_subscriptions": "Indem Sie Stripe Billing deaktivieren, <PERSON><PERSON>, dass <PERSON> nicht mehr möchten, dass Stripe die Kundenabrechnung verwaltet, sondern dass BillaBear dies übernimmt. Dadurch sparen Sie Geld.", "warning": "Wenn Sie nach der Deaktivierung wieder zu Stripe Billing zurückkehren möchten, müssen Sie alle Teilnehmer manuell erneut anmelden.", "cancel": "Abbrechen", "confirm": "Bestätigen Sie"}, "webhook": {"title": "Webhook", "url": "Webhook-URL", "register_webhook": "Webhook registrieren", "deregister_webhook": "Webhook deregistrieren", "help_info": {"url": "Eine https-U<PERSON>, die für Webhook-Aufrufe öffentlich zugänglich ist."}}, "stripe_config": {"title": "Stripe API-Schlüssel", "description": "Um Stripe nutzen zu können, müssen Sie die API-Schlüssel konfigurieren.", "stripe_private_key": "Private<PERSON><PERSON><PERSON>", "help_info": {"stripe_private_key": "Der API-<PERSON>hl<PERSON><PERSON>, der für die Authentifizierung von Backend-Anfragen verwendet wird", "stripe_public_key": "Der API-Schlü<PERSON>, der für die Authentifizierung von Frontend-Anfragen verwendet wird."}, "stripe_public_key": "Öffentlicher Schlüssel", "submit_button": "Einreichen", "error": "Stripe-API-Schlüssel können nicht bestätigt werden."}}, "view_import": {"title": "Streifen-Import", "progress": "Fort<PERSON><PERSON>t", "error": "<PERSON><PERSON>", "last_updated_at": "Zuletzt aktualisiert am", "last_id_processed": "Letzte verarbeitete ID", "process": {"started": "Gestartet", "customers": "<PERSON><PERSON>", "products": "Produkte", "prices": "<PERSON><PERSON>", "subscriptions": "Abonnements", "payments": "Zahlungen", "refunds": "Erstattungen", "charge_backs": "Rückseiten aufladen", "completed": "Abgeschlossen"}}}, "api_keys": {"main": {"title": "API-Schlüssel", "add_new_button": "Neuen API-Schlüssel erstellen", "info": {"api_base_url": "API-Basis-URL"}, "list": {"name": "Name", "key": "Schlüssel", "expires_at": "Läuft ab bei", "created_at": "Erstellt am", "no_api_keys": "Derzeit gibt es keine API-Schlüssel", "disable_button": "Deaktivieren Sie"}, "create": {"title": "Neuen Schlüssel erstellen", "name": "Name", "expires": "Abgelaufen", "close": "Schließen Sie", "create_button": "<PERSON><PERSON><PERSON><PERSON>"}}}, "exchange_rates": {"title": "Wechselkurse", "list": {"currency_code": "Währung", "rate": "Bewerten Sie", "no_rates": "<PERSON><PERSON>"}}, "tax_settings": {"update": {"title": "Steuerliche Einstellungen", "submit_btn": "Einreichen", "success_message": "Aktualisierte Steuereinstellungen", "fields": {"tax_customers_with_tax_number": "Steuerliche Kunden mit Umsatzsteuer-Identifikationsnummer", "eu_business_tax_rules": "Umgang mit EU-Unternehmenssteuerregelungen", "eu_one_stop_shop_rule": "EU-Regel der einzigen Anlaufstelle", "vat_sense_enabled": "VAT Sense Aktiviert", "vat_sense_api_key": "VAT Sense API-Schlüssel", "validate_vat_ids": "VAT-IDs validieren"}, "help_info": {"tax_customers_with_tax_number": "<PERSON><PERSON> nicht angek<PERSON>, wir<PERSON>, die eine Umsatzsteuer-Identifikationsnummer angegeben haben, keine Steuer berechnet", "eu_business_tax_rules": "<PERSON><PERSON> diese Option aktiviert ist, werden Geschäftskunden, die eine Umsatzsteuer-Identifikationsnummer angegeben haben, anders behandelt als normale Kunden", "eu_one_stop_shop_rule": "Anwendung der EU-Regel der einzigen Anlaufstelle. Die EU-Länder werden unabhängig vom Schwellenwert besteuert.", "vat_sense_enabled": "Wenn Sie Ihre Steuerregeln täglich mit der VAT Sense-Datenbank synchronisieren möchten", "vat_sense_api_key": "Ihr VAT Sense API-Schlüssel. <a href=\"https://vatsense.com/signup?referral=BILLABEAR\" target='_blank'><PERSON><PERSON>e sich hier einen kostenlosen Schlüssel</a>", "validate_vat_ids": "<PERSON><PERSON>euer-IDs mit der VAT Sense API validieren möchten."}}, "vatsense": {"title": "VatSense", "fields": {"vat_sense_enabled": "VAT Sense Aktiviert", "vat_sense_api_key": "VAT Sense API-Schlüssel", "validate_vat_ids": "VAT-IDs validieren"}, "help_info": {"vat_sense_enabled": "Wenn Sie Ihre Steuerregeln täglich mit der VAT Sense-Datenbank synchronisieren möchten", "vat_sense_api_key": "Ihr VAT Sense API-Schlüssel. <a href=\"https://vatsense.com/signup?referral=BILLABEAR\" target='_blank'><PERSON><PERSON>e sich hier einen kostenlosen Schlüssel</a>", "validate_vat_ids": "<PERSON><PERSON>euer-IDs mit der VAT Sense API validieren möchten."}, "description": "Mit unserer VAT Sense-Integration können Sie Ihre Steuerregeln automatisch aktualisieren lassen, wenn sich die Steuergesetze auf der ganzen Welt ändern. Außerdem können Sie die Umsatzsteuer-Identifikationsnummern von VAT Sense validieren lassen, damit <PERSON><PERSON> sicherstellen können, dass Ihre europäischen Kunden gültige Umsatzsteuer-Identifikationsnummern haben.", "create_account": "<PERSON>e können ein kostenloses Konto erstellen.", "create_account_link": "<PERSON><PERSON> er<PERSON>"}}}, "charge_backs": {"list": {"title": "Rückseiten aufladen", "no_charge_backs": "Derzeit gibt es keine Rückbelastungen", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "view_payment": "Ansicht Bezahlung", "list": {"amount": "Betrag", "currency": "Währung", "customer": "Kunde", "status": "Status", "created_at": "Erstellt am"}, "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter"}}}, "reports": {"dashboard": {"title": "Dashboard", "subscription_count": {"title": "Aktive Abonnements"}, "subscription_creation": {"title": "Neue Abonnements"}, "subscription_cancellation": {"title": "Abgewanderte Abonnements"}, "payment_amount": {"title": "Gewonnene Einnahmen"}, "refund_amount": {"title": "Erstatteter Betrag"}, "charge_back_amount": {"title": "Strittiger Betrag"}, "estimated_mrr": "Geschätzte MRR", "estimated_arr": "Geschätzte FER", "header": {"active_subscriptions": "Aktive Abonnements", "active_customers": "Aktive Kunden", "unpaid_invoices": "Unbezahlte Rechnungen"}, "buttons": {"daily": "Tä<PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON><PERSON><PERSON>", "subscriptions": "Abonnements", "payments": "Zahlungen"}, "links": {"customers": "<PERSON><PERSON>", "subscriptions": "Abonnements", "invoices": "Re<PERSON><PERSON>ngen"}, "latest_customers": {"title": "Neueste <PERSON>", "list": {"email": "E-Mail", "creation_date": "Datum der Erstellung"}}, "latest_events": {"title": "Letzte Ereignisse", "list": {"event_type": "Veranstaltungstyp", "customer": "Kunde", "creation_date": "Datum der Erstellung"}}, "latest_payments": {"title": "Letzte Zahlungen", "list": {"amount": "Betrag", "customer": "Kunde", "creation_date": "Datum der Erstellung"}}, "payments": {"title": "Zahlungssummen"}, "loading_chart": "<PERSON><PERSON> von Diagrammdaten..."}, "expiring_cards": {"main": {"title": "Auslaufende <PERSON>", "list": {"customer_email": "Kunden-E-Mail", "card_number": "Kartennummer", "no_expiring_cards": "<PERSON><PERSON>, die bald ablaufen", "loading": "laden", "view": "<PERSON><PERSON><PERSON>"}}}, "menu": {"title": "Berichte", "dashboard": "Dashboard", "expiring_cards": "Auslaufende <PERSON>", "subscriptions": "Abonnements", "tax": "<PERSON><PERSON><PERSON>", "churn": "Abonnement-Abwanderung", "lifetime": "Lebenslang"}, "subscriptions": {"overview": {"title": "Abonnements", "plans": {"title": "Aufschlüsselung der Pläne"}, "schedules": {"title": "Aufschlüsselung des Zeitplans"}}, "churn": {"title": "Abonnement-Abwanderung", "buttons": {"daily": "Tä<PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON><PERSON><PERSON>"}}}, "vat": {"overview": {"title": "MEHRWERTSTEUER", "list": {"amount": "Betrag", "currency": "Währung", "country": "Land"}}}, "financial": {"lifetime": {"title": "Lebenszeit-Wert", "lifespan": "Lebenserwartung", "lifespan_value": "{lifespan} Jahre", "lifetime": "Lebenszeit-Wert", "customer_count": "<PERSON><PERSON><PERSON><PERSON>", "filters": {"country": "Land", "payment_schedule": "Zahlungszeitplan", "subscription_plan": "Abonnement-Plan", "brand": "<PERSON><PERSON>"}, "help_info": {"country": "Um den Lebenszeitwert von Nutzern aus diesem Land zu sehen", "payment_schedule": "Um den Lebenszeitwert von Nutzern zu se<PERSON>, die nach einem Zahlungsplan zahlen", "subscription_plan": "So sehen Sie den Lebenszeitwert von Nutzern für einen Abonnementplan", "brand": "Um den Lebenszeitwert von Nutzern für eine Marke zu ermitteln"}, "schedules": {"week": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON><PERSON><PERSON>"}, "chart": {"lifetime_values": "Lebenszeit-Wert", "customer_counts": "<PERSON><PERSON><PERSON><PERSON>"}, "submit": "Filter"}}, "tax": {"title": "Steuerbericht", "map": {"title": "Erhobene Steuer für"}, "countries": {"title": "Länder-Schwellenwerte", "transacted_amount": "<strong>Bearbeitet:</strong> {currency}{transacted_amount}", "collected_amount": "<strong>Gesammelt:</strong> {currency}{collected_amount}", "threshold_status": "<strong>Schwellenwert:</strong> {status}", "threshold_reached": "<PERSON><PERSON><PERSON><PERSON>", "threshold_not_reached": "<PERSON>cht erreicht"}, "transactions": {"title": "Beispiel exportieren", "download": "Herunterladen Export"}}}, "credit": {"create": {"title": "K<PERSON><PERSON> er<PERSON>", "amount": "Betrag", "currency": "Währung", "reason": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>", "credit": "Kredit", "debit": "Lastschrift", "help_info": {"type": "Art der Gutschriftsanpassung, Gutschrift oder Lastschrift", "amount": "Der Preis ist die Währung der unteren Ebene. 1,00 USD wäre also 100 und 9,99 wäre 999.", "display_amount": "Dieser Preis würde {amount} betragen.", "currency": "Die Währung, die dem Kunden in Rechnung gestellt werden soll", "reason": "Ein optional<PERSON>, der später hilfreich sein kann."}, "success_message": "Erfolgreich erstellter Kredit", "submit_btn": "<PERSON><PERSON><PERSON><PERSON>"}}, "invoices": {"list": {"title": "Re<PERSON><PERSON>ngen", "unpaid_title": "Unbezahlte Rechnungen", "email": "Kunden-E-Mail", "total": "Insgesamt", "currency": "Währung", "created_at": "Erstellt am", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charge": "Zahlungsversuch", "no_invoices": "Hier gibt es keine Rechnungen", "next": "<PERSON><PERSON>", "prev": "<PERSON><PERSON><PERSON><PERSON>", "view_btn": "<PERSON><PERSON><PERSON><PERSON> an<PERSON><PERSON>", "status": "Status", "paid": "Be<PERSON>hlt", "outstanding": "<PERSON><PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "button": "Filter", "email": "Kunden-E-Mail", "number": "Rechnungsnummer"}, "mark_as_paid": "Als bezahlt markieren"}, "menu": {"title": "Re<PERSON><PERSON>ngen", "invoices": "Alle auflisten", "unpaid_invoices": "Unbezahlte Liste", "create": "Rechnung erstellen", "quotes": "<PERSON><PERSON><PERSON>", "settings": "Einstellungen", "invoices_list": "Liste der Rechnungen"}, "create": {"title": "Rechnung erstellen", "create_invoice": "Rechnung erstellen", "success_message": "Rechnung erstellt", "errors": {"no_customer": "Ein Kunde wird benötigt", "nothing_to_invoice": "<PERSON><PERSON> müssen ein Abonnement oder einen einmaligen Artikel hinzufügen.", "same_currency_and_schedule": "Für die Abonnements sollte dieselbe Währung und derselbe Zeitplan verwendet werden", "currency": "Eine Währung ist erforderlich", "need_description": "Ich brauche eine Beschreibung", "need_amount": "Bedarfsmenge", "need_tax_type": "<PERSON>e benötigen eine Steuerart"}, "customer": {"create_customer": "<PERSON>nde anlegen", "fields": {"customer": "Kunde", "currency": "Währung", "due_date": "Fälligkeitsdatum"}, "help_info": {"customer": "<PERSON> Kunde, für den das Angebot gilt", "currency": "Die für die Rechnung zu verwendende Währung", "due_date": "Das Fälligkeitsdatum für die Rechnung. Wenn kein Datum angegeben ist, wird der Systemstandard verwendet."}}, "subscriptions": {"title": "Abonnements", "add_new": "Abonnement hinzufügen", "list": {"subscription_plan": "Abonnement-Plan", "price": "Pre<PERSON>", "seat_number": "Sitznummer"}, "no_subscriptions": "Keine Abonnements", "add_subscription": "Abonnement hinzufügen"}, "items": {"title": "Einmalige Posten", "add_item": "Einmaligen Artikel hinzufügen", "no_items": "<PERSON><PERSON> einmaligen Posten", "list": {"description": "Beschreibung", "amount": "Betrag", "tax_included": "Inklusive Steuer", "digital_product": "Digitales Produkt", "tax_type": "Steuerart"}, "tax_types": {"digital_services": "Digitale Dienste", "digital_goods": "<PERSON>e Güter", "physical": "Physische Waren/Dienstleistungen"}}}, "view": {"title": "<PERSON><PERSON><PERSON><PERSON> an<PERSON><PERSON>", "main": {"title": "Informationen auf der Rechnung", "created_at": "Erstellt am", "pay_link": "<PERSON>", "due_date": "Fälligkeitsdatum"}, "customer": {"title": "Kunde", "email": "E-Mail", "more_info": "Me<PERSON> Infos", "address": {"company_name": "Name des Unternehmens", "street_line_one": "Straße Linie 1", "street_line_two": "Straße Linie 2", "city": "Stadt", "region": "Region", "post_code": "<PERSON><PERSON><PERSON><PERSON>", "country": "Land"}}, "biller": {"title": "Rechnungssteller", "email": "E-Mail", "more_info": "Me<PERSON> Infos", "address": {"company_name": "Name des Unternehmens", "street_line_one": "Straße Linie 1", "street_line_two": "Straße Linie 2", "city": "Stadt", "region": "Region", "post_code": "<PERSON><PERSON><PERSON><PERSON>", "country": "Land"}}, "lines": {"title": "Artikel", "description": "Beschreibung", "tax_rate": "Steuersatz", "amount": "Betrag", "tax_exempt": "Steuerbefreit"}, "total": {"title": "<PERSON><PERSON><PERSON>", "total": "Insgesamt", "sub_total": "Zwischensumme", "tax_total": "Steuer Gesamt"}, "status": {"paid": "Rechnung erfolgreich bezahlt am {date}", "outstanding": "Die Rechnung muss noch bezahlt werden."}, "actions": {"charge_card": "Kostenpflichtige Karte", "mark_as_paid": "Als bezahlt markieren"}, "payment_failed": {"message": "Zahlung kann nicht erfolgreich durchgeführt werden"}, "payment_succeeded": {"message": "Zahlung erfolgreich durchgeführt."}, "download": "Rechnung herunterladen", "invoice_delivery": {"title": "Rechnungen Lieferungen", "method": "<PERSON>e", "detail": "Einzelheiten", "status": "Status", "created_at": "Erstellt am", "no_invoice_deliveries": "<PERSON><PERSON> Lie<PERSON>ungen auf Rechnung"}}, "settings": {"title": "Rechnungseinstellungen", "update": "Aktualisieren"}, "delivery": {"create": {"title": "Neue Rechnungslieferung erstellen", "fields": {"method": "<PERSON>e", "format": "Format", "sftp": {"port": "Hafen", "hostname": "Hostname", "directory": "Verzeichnis", "username": "<PERSON><PERSON><PERSON><PERSON>", "password": "Passwort"}, "webhook": {"method": "<PERSON>e", "url": "URL"}, "email": {"email": "E-Mail", "help_info": "<PERSON>n keine E-Mail-Adresse angegeben wird, wird standardmäßig die E-Mail-Adresse des Kunden verwendet."}}, "save": "Speichern Sie"}, "update": {"title": "Rechnungszustellung aktualisieren", "fields": {"method": "<PERSON>e", "format": "Format", "sftp": {"port": "Hafen", "hostname": "Hostname", "directory": "Verzeichnis", "username": "<PERSON><PERSON><PERSON><PERSON>", "password": "Passwort"}, "webhook": {"method": "<PERSON>e", "url": "URL"}, "email": {"email": "E-Mail", "help_info": "<PERSON>n keine E-Mail-Adresse angegeben wird, wird standardmäßig die E-Mail-Adresse des Kunden verwendet."}}, "save": "Speichern Sie"}, "format": {"pdf": "PDF", "zugferd_v1": "ZUGFeRD V1", "zugferd_v2": "ZUGFeRD V2 - XRechnung"}}, "download": {"loading_message": "Laden...", "format": "Format zum Herunterladen wählen", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "home": {"stripe_import": {"text": "Sie haben Ihre Streifendaten nicht importiert.", "link": "<PERSON><PERSON><PERSON> hier, um jetzt zu importieren", "dismiss": "<PERSON><PERSON><PERSON><PERSON>"}, "update_available": {"text": "<PERSON>s ist ein Update verfügbar", "link": "Details zur Veröffentlichung", "dismiss": "<PERSON><PERSON><PERSON><PERSON>"}, "default_tax": {"text": "Ihr Land wird standardmäßig nicht für Steuersätze unterstützt. Sie müssen einen Steuersatz für Ihre Standardmarke festlegen!", "link": "<PERSON><PERSON><PERSON>"}}, "vouchers": {"create": {"title": "Voucher erstellen", "submit": "Einreichen", "success_message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Gutschein erstellt", "fields": {"name": "Name", "type": "<PERSON><PERSON>", "type_percentage": "Prozentsatz", "type_fixed_credit": "Fester Kredit", "percentage": "Prozentsatz", "entry_type": "Eintrag Typ", "entry_type_manual": "Handbuch", "entry_type_automatic": "Automatisch", "amount": "Betrag - {currency}", "code": "Code", "entry_event": "Veranstaltung", "event_expired_card_added": "Hinzufügen einer neuen Zahlungskarte bei Warnung vor abgelaufener Karte"}, "help_info": {"name": "Der Name des Gutscheins", "type": "Prozentsatz ist ein Prozentsatz einer Rechnung und fester Kredit ist ein fester Kredit", "entry_type": "<PERSON><PERSON>, dass der Benutzer einen Code eingibt, automatisch bedeutet, dass er durch ein Ereignis ausgelöst wird", "percentage": "Der Prozentsatz von", "amount": "Der Betrag in {currency}, den der Gutschein liefert", "code": "Der Code, den der Kunde angeben muss, damit der Gutschein aktiviert wird", "entry_event": "<PERSON> E<PERSON>ignis, das eintreten muss, damit der Gutschein aktiviert wird"}}, "list": {"title": "Gutscheine", "no_vouchers": "Derzeit sind keine Gutscheine vorhanden", "create_new": "Neuen Gutschein erstellen", "list": {"name": "Name", "type": "<PERSON><PERSON>", "entry_type": "Eintrag Typ"}, "view_btn": "<PERSON><PERSON><PERSON>", "loading": "Ladeguts<PERSON>ine"}, "view": {"title": "Gutschein", "main": {"name": "Name", "type": "<PERSON><PERSON>", "disabled": "<PERSON><PERSON><PERSON>", "entry_type": "Eintrag Typ", "percentage": "Prozentsatz", "amount": "Betrag für {currency}", "code": "Code", "automatic_event": "Automatisches Ereignis"}, "disable": "Deaktivieren Sie", "enable": "Aktivieren Sie"}}, "quotes": {"create": {"title": "Ang<PERSON><PERSON> erstellen", "create_quote": "Ang<PERSON><PERSON> erstellen", "success_message": "Ang<PERSON><PERSON> erste<PERSON>t", "errors": {"no_customer": "Ein Kunde wird benötigt", "nothing_to_invoice": "<PERSON><PERSON> müssen ein Abonnement oder einen einmaligen Artikel hinzufügen.", "same_currency_and_schedule": "Für die Abonnements sollte dieselbe Währung und derselbe Zeitplan verwendet werden", "currency": "Eine Währung ist erforderlich", "need_description": "Ich brauche eine Beschreibung", "need_amount": "Bedarfsmenge", "need_tax_type": "<PERSON>e benötigen eine Steuerart"}, "customer": {"create_customer": "<PERSON>nde anlegen", "fields": {"customer": "Kunde", "currency": "Währung", "expires_at": "Läuft ab bei"}, "help_info": {"customer": "<PERSON> Kunde, für den das Angebot gilt", "currency": "Die für das Angebot zu verwendende Währung", "expires_at": "Wenn das Angebot ausläuft und nicht mehr bezahlt werden kann"}}, "subscriptions": {"title": "Abonnements", "add_new": "Abonnement hinzufügen", "list": {"subscription_plan": "Abonnement-Plan", "price": "Pre<PERSON>", "per_seat": "Pro Sitzplatz"}, "no_subscriptions": "Keine Abonnements", "add_subscription": "Abonnement hinzufügen"}, "items": {"title": "Einmalige Posten", "add_item": "Einmaligen Artikel hinzufügen", "no_items": "<PERSON><PERSON> einmaligen Posten", "list": {"description": "Beschreibung", "amount": "Betrag", "tax_included": "Inklusive Steuer", "digital_product": "Digitales Produkt", "tax_type": "Steuerart"}, "tax_types": {"digital_services": "Digitale Dienste", "digital_goods": "<PERSON><PERSON>n", "physical": "Physische Waren/Dienstleistungen"}}}, "list": {"title": "<PERSON><PERSON><PERSON>", "email": "Kunden-E-Mail", "total": "Insgesamt", "currency": "Währung", "created_at": "Erstellt am", "no_quotes": "Hier gibt es keine Angebote", "next": "<PERSON><PERSON>", "prev": "<PERSON><PERSON><PERSON><PERSON>", "view_btn": "<PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "button": "Filter", "email": "Kunden-E-Mail", "number": "Rechnungsnummer"}}, "view": {"title": "<PERSON><PERSON><PERSON> an<PERSON>", "quote": {"title": "Angebot Info", "created_by": "<PERSON><PERSON><PERSON><PERSON> von", "created_at": "Erstellt am", "expires_at": "Läuft ab bei", "pay_link": "<PERSON>"}, "status": {"paid": "Angebot erfolgreich bezahlt am {date}"}, "customer": {"title": "Kunde", "email": "E-Mail", "more_info": "Me<PERSON> Infos", "address": {"company_name": "Name des Unternehmens", "street_line_one": "Straße Linie 1", "street_line_two": "Straße Linie 2", "city": "Stadt", "region": "Region", "post_code": "<PERSON><PERSON><PERSON><PERSON>", "country": "Land"}}, "lines": {"title": "Artikel", "description": "Beschreibung", "schedule": "Zahlungszeitplan", "tax_rate": "Steuersatz", "amount": "Betrag", "one_off": "Ein<PERSON>ig", "tax_exempt": "Steuerbefreit"}, "total": {"title": "<PERSON><PERSON><PERSON>", "total": "Insgesamt", "sub_total": "Zwischensumme", "tax_total": "Steuer Gesamt"}}}, "system": {"webhooks": {"webhook_endpoint": {"list": {"title": "Webhook-Endpunkte", "add": "Endpunkt hinzufügen", "view": "<PERSON><PERSON><PERSON>", "list": {"name": "Name", "url": "URL", "status": "Status"}, "no_endpoints": "Derzeit gibt es keine Webhook-Endpunkte"}, "create": {"title": "Webhook-Endpunkt erstellen", "fields": {"name": "Name", "url": "URL"}, "help_info": {"name": "Der Name für den Webhook-Endpunkt, um ihn später leichter identifizieren zu können", "url": "Die URL, an die die Nutzdaten gesendet werden sollen"}, "create_button": "<PERSON><PERSON><PERSON><PERSON>"}, "view": {"title": "Endpunkt anzeigen", "main": {"title": "Infos", "name": "Name", "url": "URL"}}}, "main": {"title": "<PERSON><PERSON><PERSON>", "manage_endpoints": "Endpunkte verwalten", "list": {"type": "<PERSON><PERSON>", "created_at": "Erstellt am", "view_btn": "Ereignisdaten anzeigen", "loading": "<PERSON><PERSON> von Webhook-Ereignissen", "no_events": "Es sind keine Webhook-Ereignisse aufgetreten"}}, "event": {"view": {"title": "Informationen zur Veranstaltung", "main": {"title": "Ereignisdaten", "type": "Veranstaltungstyp", "payload": "Nutzlast", "created_at": "Erstellt am"}, "responses": {"title": "Endpunkt-Anforderungen", "list": {"url": "URL", "status_code": "Status Code", "body": "<PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON>", "created_at": "Erstellt am"}}, "info": {"title": "Ansicht Info<PERSON>", "error_message": "Fehlermeldung", "status_code": "Status Code", "body": "Antwortstelle", "processing_time": "Bearbeitungszeit"}}}}, "integrations": {"list": {"title": "Integrationen", "list": {"name": "Integration"}, "slack": {"name": "<PERSON><PERSON>ck", "button": "Konfigurieren Sie"}}, "slack": {"webhooks": {"list": {"title": "Slack Webhooks", "name": "Name", "webhook": "Webhook", "disable_btn": "Deaktivieren Sie", "enable_btn": "Aktivieren Sie", "no_webhooks": "<PERSON>s gibt noch keine Slack Webhooks", "next": "<PERSON><PERSON>", "prev": "<PERSON><PERSON><PERSON><PERSON>", "error_message": "Slack Webhooks können nicht abgerufen werden", "create_new": "<PERSON><PERSON><PERSON><PERSON>"}, "create": {"title": "Slack Webhook erstellen", "fields": {"name": "Name", "webhook": "Webhook Url"}, "help_info": {"name": "Der Name, der verwendet wird, um diesen Webhook innerhalb von <PERSON> zu identifizieren", "webhook": "<PERSON> von Slack bereitgestellte URL, die als Webhook verwendet werden soll"}, "save_btn": "Speichern"}}, "notifications": {"list": {"title": "Slack-Benachrichtigung", "event": "Veranstaltung", "webhook": "Webhook", "disable_btn": "Deaktivieren Sie", "template": "Vorlage", "enable_btn": "Aktivieren Sie", "no_notifications": "Es gibt noch keine Slack-Benachrichtigungen", "next": "<PERSON><PERSON>", "prev": "<PERSON><PERSON><PERSON><PERSON>", "error_message": "Slack-Benachrichtigungen können nicht abgerufen werden", "create_new": "<PERSON><PERSON><PERSON><PERSON>"}, "create": {"title": "Slack-Benachrichtigung erstellen", "fields": {"webhook": "Webhook", "event": "Veranstaltung", "template": "Vorlage"}, "help_info": {"event": "<PERSON> Ereignis, das die Benachrichtigung auslösen soll", "webhook": "Der Slack-Webhook, der für die Benachrichtigung verwendet werden soll", "template": "<PERSON> Vorlage, die beim Versand der Benachrichtigung verwendet werden soll. <a href=\"https://docs.billabear.com/user/integration/slack\" target=\"_blank\">Variablen können hier gefunden werden</a>"}, "save_btn": "Speichern"}}, "menu": {"title": "<PERSON><PERSON>ck", "webhooks": "<PERSON><PERSON><PERSON>", "notification": "Benachrichtigungen"}}}, "menu": {"title": "System-Tools", "webhooks": "<PERSON><PERSON><PERSON>", "integrations": "Integrationen"}}, "checkout": {"create": {"title": "<PERSON><PERSON> erstellen", "create_quote": "<PERSON><PERSON> erstellen", "success_message": "Checkout erste<PERSON>t", "errors": {"no_customer": "Ein Kunde wird benötigt", "nothing_to_invoice": "<PERSON><PERSON> müssen ein Abonnement oder einen einmaligen Artikel hinzufügen.", "same_currency_and_schedule": "Für die Abonnements sollte dieselbe Währung und derselbe Zeitplan verwendet werden", "currency": "Eine Währung ist erforderlich", "need_description": "Ich brauche eine Beschreibung", "need_amount": "Bedarfsmenge", "need_tax_type": "<PERSON>e benötigen eine Steuerart"}, "customer": {"create_customer": "<PERSON>nde anlegen", "fields": {"name": "Name", "permanent": "<PERSON><PERSON><PERSON>", "customer": "Kunde", "currency": "Währung", "slug": "slug", "expires_at": "Läuft ab bei", "brand": "<PERSON><PERSON>"}, "help_info": {"permanent": "Ob es sich um eine permanente oder eine einmalige Abmeldung handelt", "name": "Der identifizierende Name für die Registrierkasse", "customer": "Der Kunde, für den die Kasse bestimmt ist", "currency": "Die Währung, die für den Checkout verwendet werden soll", "expires_at": "Wenn das Angebot ausläuft und nicht mehr bezahlt werden kann", "slug": "Der Slug für die URL. <PERSON><PERSON> <PERSON> m<PERSON>, dass die Kasse eine schöne URL hat, verwenden Si<PERSON> diese.", "brand": "<PERSON> Marke, zu der die Kasse gehört"}}, "subscriptions": {"title": "Abonnements", "add_new": "Abonnement hinzufügen", "list": {"subscription_plan": "Abonnement-Plan", "price": "Pre<PERSON>", "per_seat": "Pro Sitzplatz"}, "no_subscriptions": "Keine Abonnements", "add_subscription": "Abonnement hinzufügen"}, "items": {"title": "Einmalige Posten", "add_item": "Einmaligen Artikel hinzufügen", "no_items": "<PERSON><PERSON> einmaligen Posten", "list": {"description": "Beschreibung", "amount": "Betrag", "tax_included": "Inklusive Steuer", "digital_product": "Digitales Produkt", "tax_type": "Steuerart"}, "tax_types": {"digital_services": "Digitale Dienste", "digital_goods": "<PERSON>e Güter", "physical": "Physische Waren/Dienstleistungen"}}}, "view": {"title": "<PERSON><PERSON> Ka<PERSON> gehen", "checkout": {"title": "Informationen zum Checkout", "created_by": "<PERSON><PERSON><PERSON><PERSON> von", "created_at": "Erstellt am", "expires_at": "Läuft ab bei", "pay_link": "<PERSON>", "name": "Name"}, "status": {"paid": "Angebot erfolgreich bezahlt am {date}"}, "customer": {"title": "Kunde", "email": "E-Mail", "more_info": "Me<PERSON> Infos", "address": {"company_name": "Name des Unternehmens", "street_line_one": "Straße Linie 1", "street_line_two": "Straße Linie 2", "city": "Stadt", "region": "Region", "post_code": "<PERSON><PERSON><PERSON><PERSON>", "country": "Land"}}, "lines": {"title": "Artikel", "description": "Beschreibung", "schedule": "Zahlungszeitplan", "tax_rate": "Steuersatz", "amount": "Betrag", "one_off": "Ein<PERSON>ig", "tax_exempt": "Steuerbefreit"}, "total": {"title": "<PERSON><PERSON><PERSON>", "total": "Insgesamt", "sub_total": "Zwischensumme", "tax_total": "Steuer Gesamt"}}, "list": {"title": "<PERSON><PERSON>", "email": "E-Mail", "country": "Land", "reference": "<PERSON><PERSON><PERSON><PERSON>", "no_checkouts": "Derzeit sind keine Kassen vorhanden", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "view_btn": "<PERSON><PERSON><PERSON>", "list": {"name": "Name", "created_at": "Erstellt am", "view": "<PERSON><PERSON><PERSON>"}, "filter": {"title": "Filter", "name": "Name", "button": "Filter", "search": "Filter"}, "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON> laden", "error_message": "Es ist ein Fehler aufgetreten"}}, "layout": {"topbar": {"menu": {"settings": "Einstellungen", "signout": "Abmelden"}}}, "workflows": {"cancellation_request": {"list": {"title": "Annullierungsanträge", "email": "Kunde", "status": "Status", "plan": "Plan", "no_cancellation_requests": "Derzeit liegen keine Stornierungsanfragen vor", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "view": "<PERSON><PERSON><PERSON>", "edit_button": "<PERSON><PERSON><PERSON>", "bulk_button": "Bulk-Wiederaufbereitung", "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter", "has_error": "<PERSON>"}, "error_message": "Es ist ein Fehler aufgetreten"}, "view": {"title": "Details zur Stornierungsanfrage", "subscription": {"title": "Details zum Abonnement", "name": "Plan Name", "customer": "Kunde", "original_cancellation_date": "Ursprüngliches Annullierungsdatum"}, "details": {"title": "Details zur Stornierung", "state": "Staa<PERSON>", "when": "<PERSON><PERSON>", "refund_type": "Erstattungsart", "specific_date": "Datum der Annullierung"}, "error": {"title": "<PERSON><PERSON>"}, "buttons": {"process": "Wiederholungsversuch Prozess"}}, "edit": {"title": "Stornierungsanfragen bearbeiten", "add_place": "<PERSON><PERSON> hinzufügen", "add_place_modal": {"title": "<PERSON><PERSON> hinzufügen", "from_place": "<PERSON>", "to_place": "<PERSON><PERSON>", "name": "Name", "event_handler": "<PERSON><PERSON>ignis-<PERSON>ler", "handler_options": "Handler-Optionen", "add": "Hinzufügen", "required": "<PERSON><PERSON>"}, "edit_place_modal": {"title": "<PERSON><PERSON> bear<PERSON>ten", "delete_button": "Ort löschen", "enable_button": "Aktivieren Sie", "disable_button": "Deaktivieren Sie"}}}, "menu": {"title": "Arbeitsabläufe Werkzeuge", "cancellation_requests": "Annullierungsanträge", "subscription_creation": "Erstellung von Abonnements", "payment_creation": "Erstellung von Zahlungen", "refund_created_process": "Erstattung Erstellt Prozess", "payment_failure_process": "Verfahren bei Zahlungsausfällen", "charge_back_creation": "Erstellung von Rück<PERSON>n"}, "subscription_creation": {"list": {"title": "Erstellung von Abonnements", "email": "Kunde", "status": "Status", "plan": "Plan", "no_cancellation_requests": "Derzeit gibt es keine Abonnement-Erstellung", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "view": "<PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter", "has_error": "<PERSON>"}, "error_message": "Es ist ein Fehler aufgetreten", "edit_button": "<PERSON><PERSON><PERSON>", "bulk_button": "Bulk-Wiederaufbereitung"}, "view": {"title": "Details zur Erstellung eines Abonnements", "subscription": {"title": "Details zum Abonnement", "name": "Plan Name", "customer": "Kunde", "view": "Ansicht Abonnement"}, "details": {"title": "Details zur Erstellung", "state": "Staa<PERSON>"}, "error": {"title": "<PERSON><PERSON>"}, "buttons": {"process": "Wiederholungsversuch Prozess"}}, "edit": {"title": "Erstellung von Abonnements bearbeiten", "add_place": "<PERSON><PERSON> hinzufügen", "add_place_modal": {"title": "<PERSON><PERSON> hinzufügen", "from_place": "<PERSON>", "to_place": "<PERSON><PERSON>", "name": "Name", "event_handler": "<PERSON><PERSON>ignis-<PERSON>ler", "handler_options": "Handler-Optionen", "add": "Hinzufügen", "required": "<PERSON><PERSON>"}}}, "payment_creation": {"list": {"title": "Erstellung von Zahlungen", "email": "Kunde", "status": "Status", "plan": "Plan", "no_results": "Derzeit gibt es keine Ergebnisse", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "view": "<PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter", "has_error": "<PERSON>"}, "bulk_button": "Bulk-Wiederaufbereitung", "error_message": "Es ist ein Fehler aufgetreten", "edit_button": "<PERSON><PERSON><PERSON>"}, "view": {"title": "Details zur Zahlungserstellung", "payment": {"title": "Details zur Bezahlung", "name": "Plan Name", "customer": "Kunde", "view": "Ansicht Bezahlung"}, "details": {"title": "Details zur Erstellung", "state": "Staa<PERSON>"}, "error": {"title": "<PERSON><PERSON>"}, "buttons": {"process": "Wiederholungsversuch Prozess"}}, "edit": {"title": "Zahlungserstellung bearbeiten", "add_place": "<PERSON><PERSON> hinzufügen", "add_place_modal": {"title": "<PERSON><PERSON> hinzufügen", "from_place": "<PERSON>", "to_place": "<PERSON><PERSON>", "name": "Name", "event_handler": "<PERSON><PERSON>ignis-<PERSON>ler", "handler_options": "Handler-Optionen", "add": "Hinzufügen", "required": "<PERSON><PERSON>"}}}, "refund_created_process": {"list": {"title": "Erstattung Erstellt", "email": "Kunde", "status": "Status", "plan": "Plan", "no_results": "Derzeit gibt es keine Ergebnisse", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "view": "<PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter", "has_error": "<PERSON>"}, "error_message": "Es ist ein Fehler aufgetreten", "edit_button": "<PERSON><PERSON><PERSON>", "bulk_button": "Bulk-Wiederaufbereitung"}, "view": {"title": "Erstattung Erstellt Prozessdetails", "refund": {"title": "Details zur Erstattung", "name": "Plan Name", "customer": "Kunde", "view": "Ansicht Rückerstattung"}, "details": {"title": "Details zur Erstellung", "state": "Staa<PERSON>"}, "error": {"title": "<PERSON><PERSON>"}, "buttons": {"process": "Wiederholungsversuch Prozess"}}, "edit": {"title": "Prozess \"Erstattungserstellung bearbeiten", "add_place": "<PERSON><PERSON> hinzufügen", "add_place_modal": {"title": "<PERSON><PERSON> hinzufügen", "from_place": "<PERSON>", "to_place": "<PERSON><PERSON>", "name": "Name", "event_handler": "<PERSON><PERSON>ignis-<PERSON>ler", "handler_options": "Handler-Optionen", "add": "Hinzufügen", "required": "<PERSON><PERSON>"}, "edit_place_modal": {"title": "<PERSON><PERSON> bear<PERSON>ten", "disable_button": "Deaktivieren Sie", "enable_button": "Aktivieren Sie"}}}, "payment_failure_process": {"list": {"title": "Fehlgeschlagene Zahlung", "email": "Kunde", "status": "Status", "plan": "Plan", "no_results": "Derzeit gibt es keine Ergebnisse", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "view": "<PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter", "has_error": "<PERSON>"}, "error_message": "Es ist ein Fehler aufgetreten"}, "view": {"title": "Details zum Prozess des Zahlungsausfalls", "payment": {"title": "Details zum Zahlungsversuch", "amount": "Betrag", "customer": "Kunde", "view": "<PERSON><PERSON><PERSON><PERSON> an<PERSON><PERSON>"}, "details": {"title": "Details zur Erstellung", "state": "Staa<PERSON>"}, "error": {"title": "<PERSON><PERSON>"}, "buttons": {"process": "Wiederholungsversuch Prozess"}}}, "charge_back_creation": {"list": {"title": "Erstellung von Rück<PERSON>n", "email": "Kunde", "status": "Status", "plan": "Plan", "no_results": "Derzeit gibt es keine Ergebnisse", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "view": "<PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "email": "E-Mail", "reference": "<PERSON><PERSON><PERSON><PERSON>", "external_reference": "Externe Referenz", "button": "Filter", "search": "Filter", "has_error": "<PERSON>"}, "error_message": "Es ist ein Fehler aufgetreten", "edit_button": "<PERSON><PERSON><PERSON>", "bulk_button": "Bulk-Wiederaufbereitung"}, "view": {"title": "Details zur Erstellung von Rückbelastungen", "payment": {"title": "Details zur Bezahlung", "name": "Plan Name", "customer": "Kunde", "view": "Ansicht Bezahlung"}, "details": {"title": "Details zur Erstellung", "state": "Staa<PERSON>"}, "error": {"title": "<PERSON><PERSON>"}, "buttons": {"process": "Wiederholungsversuch Prozess"}}, "edit": {"title": "Chargeback-Erstellung bearbeiten", "add_place": "<PERSON><PERSON> hinzufügen", "add_place_modal": {"title": "<PERSON><PERSON> hinzufügen", "from_place": "<PERSON>", "to_place": "<PERSON><PERSON>", "name": "Name", "event_handler": "<PERSON><PERSON>ignis-<PERSON>ler", "handler_options": "Handler-Optionen", "add": "Hinzufügen", "required": "<PERSON><PERSON>"}}}}, "country": {"list": {"title": "<PERSON><PERSON><PERSON>", "no_countries": "Derzeit gibt es keine Länder", "create_new": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste Seite", "prev": "Vorherige Seite", "list": {"name": "Name", "iso_code": "Code", "tax_threshold": "Steuerlicher Schwellenwert", "collecting": "Steuererhebung"}, "view": "<PERSON><PERSON><PERSON>", "filter": {"title": "Filter", "name": "Name", "code": "Code", "button": "Filter", "search": "Filter", "collecting": "Steuererhebung"}, "error_message": "Es ist ein Fehler aufgetreten"}, "create": {"title": "Neues Land erstellen", "country": {"fields": {"name": "Name", "iso_code": "Ländercode", "currency": "Währung", "threshold": "Schwellenwert", "in_eu": "In der EU?", "tax_year": "Beginn des Steuerjahres", "collecting": "<PERSON><PERSON><PERSON> e<PERSON>", "tax_number": "Umsatzsteuer-Identifikationsnummer"}, "help_info": {"name": "Der Name des Landes", "iso_code": "Der ISO-Code für das Land", "currency": "Die Berichtswährung für das Land", "threshold": "Die Steuerschwelle für das Land", "in_eu": "Ist das Land Mitglied der EU?", "tax_year": "Das Datum des Beginns des Steuerjahres für das Land", "collecting": "Wenn Steuern immer für dieses Land erhoben werden sollen", "tax_number": "Ihre Umsatzsteuer-Identifikationsnummer für dieses Land."}}, "create_button": "<PERSON><PERSON><PERSON><PERSON>"}, "view": {"title": "Ansicht Land", "fields": {"name": "Name", "iso_code": "Ländercode", "threshold": "Schwellenwert", "currency": "Währung", "in_eu": "In Eu", "start_of_tax_year": "Beginn des Steuerjahres", "enabled": "Aktiviert", "collecting": "Steuererhebung", "tax_number": "Umsatzsteuer-Identifikationsnummer", "transaction_threshold": "Transaktionsschwellenwert", "threshold_type": "Schwellenwert Typ"}, "edit_button": "<PERSON><PERSON><PERSON>", "tax_rule": {"title": "Steuerliche Vorschriften", "rate": "Steuersatz", "type": "Steuerart", "default": "Ist Standard", "start_date": "Datum des Beginns", "end_date": "Enddatum", "no_tax_rules": "<PERSON><PERSON>", "add": "Steuervorschrift hinzufügen", "edit": "<PERSON><PERSON><PERSON>"}, "add_tax_rule": {"tax_rate": "Steuersatz", "tax_type": "Steuerart", "valid_from": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "valid_until": "Gültig bis", "title": "Steuervorschrift hinzufügen", "default": "Standard-Steuerregel", "save": "Speichern", "select_tax_type": "Steuerart auswählen"}, "edit_tax_rule": {"tax_rate": "Steuersatz", "tax_type": "Steuerart", "valid_from": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "valid_until": "Gültig bis", "title": "Steuervorschrift bearbeiten", "default": "Standard-Steuerregel", "save": "Update", "select_tax_type": "Steuerart auswählen"}, "states": {"title": "Staa<PERSON>", "add": "Neuen Staat hinzufügen", "name": "Name", "code": "Code", "collecting": "Steuern eintreiben?", "threshold": "Schwellenwert", "view": "<PERSON><PERSON><PERSON>", "no_states": "<PERSON><PERSON> gibt keine Staaten"}}, "edit": {"title": "Land bearbeiten", "country": {"fields": {"name": "Name", "iso_code": "Ländercode", "currency": "Währung", "threshold": "Schwellenwert", "in_eu": "In der EU?", "tax_year": "Beginn des Steuerjahres", "enabled": "Aktiviert", "collecting": "<PERSON><PERSON><PERSON> e<PERSON>", "tax_number": "Umsatzsteuer-Identifikationsnummer", "transaction_threshold": "Transaktionsschwellenwert", "threshold_type": "Schwellenwert Typ", "threshold_types": {"rolling": "<PERSON><PERSON><PERSON>", "calendar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rolling_quarterly": "Walzen nach Quartalen", "rolling_accounting": "Rolling nach Rechnungsjahr"}}, "help_info": {"name": "Der Name des Landes", "iso_code": "Der ISO-Code für das Land", "currency": "Die Berichtswährung für das Land", "threshold": "Die Steuerschwelle für das Land", "in_eu": "Ist das Land Mitglied der EU?", "tax_year": "Das Datum des Beginns des Steuerjahres für das Land", "enabled": "Wenn das Land für Kundenanmeldungen aktiviert ist", "collecting": "Wenn Steuern immer für dieses Land erhoben werden sollen", "tax_number": "Ihre Umsatzsteuer-Identifikationsnummer für dieses Land.", "transaction_threshold": "Wie hoch ist die Transaktionsschwelle für den Staat?", "threshold_type": "Wie der Zeitraum für die Schwellenwertberechnung bestimmt wird"}}, "update_button": "Update"}}, "tax_type": {"list": {"title": "Steuerarten", "create_new": "<PERSON><PERSON>", "error_message": "Es ist ein Fehler aufgetreten", "list": {"name": "Name", "make_default": "Standard machen", "is_default": "Ist Standard", "default": "Standard", "update": "Update"}, "no_tax_types": "Derzeit gibt es keine Steuerarten"}, "create": {"title": "Steuerart er<PERSON>", "tax_type": {"fields": {"name": "Name", "vat_sense_type": "VAT Sense Typ"}, "help_info": {"name": "Die Bezeichnung für die Steuer", "vat_sense_type": "Die Steuerart im System von VAT Sense"}}, "create_button": "<PERSON><PERSON><PERSON><PERSON>"}, "update": {"title": "Steuerart aktualisieren", "tax_type": {"fields": {"name": "Name", "vat_sense_type": "VAT Sense Typ"}, "help_info": {"name": "Die Bezeichnung für die Steuer", "vat_sense_type": "Die Steuerart im System von VAT Sense"}}, "update_button": "Update"}}, "finance": {"integration": {"title": "Integrationen", "fields": {"integration": "Integration", "api_key": "API-Schlüssel", "enabled": "Aktiviert"}, "buttons": {"connect": "Verbindung über OAuth", "disconnect": "Trennen Sie die Verbindung", "save": "Speichern Sie"}, "settings": {"title": "Einstellungen"}, "xero": {"account_id": "Kontocode für Zahlungen"}, "errors": {"required": "<PERSON><PERSON> ist erford<PERSON>lich", "invalid": "<PERSON><PERSON>ld ist ungültig", "complete_error": "<PERSON><PERSON>, diese Einstellungen zu speichern, ist ein Fehler aufgetreten. Bitte versuchen Si<PERSON> es erneut."}}, "menu": {"integration": "Integration"}}, "tax": [], "state": {"view": {"title": "Ansicht Staat", "edit": "<PERSON><PERSON><PERSON>", "fields": {"name": "Name", "code": "Code", "threshold": "Schwellenwert", "collecting": "Sammeln", "transaction_threshold": "Transaktionsschwellenwert", "threshold_type": "Schwellenwert Typ"}, "tax_rule": {"title": "Steuerliche Vorschriften", "rate": "Steuersatz", "type": "Steuerart", "default": "Ist Standard", "start_date": "Datum des Beginns", "end_date": "Enddatum", "no_tax_rules": "<PERSON><PERSON>", "add": "Steuervorschrift hinzufügen", "edit": "<PERSON><PERSON><PERSON>"}, "add_tax_rule": {"tax_rate": "Steuersatz", "tax_type": "Steuerart", "valid_from": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "valid_until": "Gültig bis", "title": "Steuervorschrift hinzufügen", "default": "Standard-Steuerregel", "save": "Speichern", "select_tax_type": "Steuerart auswählen"}, "edit_tax_rule": {"tax_rate": "Steuersatz", "tax_type": "Steuerart", "valid_from": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "valid_until": "Gültig bis", "title": "Steuervorschrift bearbeiten", "default": "Standard-Steuerregel", "save": "Update", "select_tax_type": "Steuerart auswählen"}}, "create": {"title": "Neue Region erstellen", "state": {"fields": {"name": "Name", "code": "Code", "collecting": "Sammeln", "threshold": "Schwellenwert"}, "help_info": {"name": "Der Name des Staates", "code": "Der Code, der oft als Abkürzung für den Zustand verwendet wird", "collecting": "Wenn wir ständig Steuern für den Staat eintreiben", "threshold": "Was die wirtschaftliche Schwelle für den Staat ist"}}, "create_button": "<PERSON><PERSON><PERSON><PERSON>"}, "edit": {"title": "<PERSON><PERSON><PERSON> bearbeiten", "state": {"fields": {"name": "Name", "code": "Code", "collecting": "Sammeln", "threshold": "Schwellenwert", "transaction_threshold": "Transaktionsschwellenwert", "threshold_type": "Schwellenwert Typ", "threshold_types": {"rolling": "<PERSON><PERSON><PERSON>", "calendar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rolling_quarterly": "Walzen nach Quartalen", "rolling_accounting": "Rolling nach Rechnungsjahr"}}, "help_info": {"name": "Der Name des Staates", "code": "Der Code, der oft als Abkürzung für den Zustand verwendet wird", "collecting": "Wenn wir ständig Steuern für den Staat eintreiben", "threshold": "Was die wirtschaftliche Schwelle für den Staat ist", "transaction_threshold": "Wie hoch ist die Transaktionsschwelle für den Staat?", "threshold_type": "Wie der Zeitraum für die Schwellenwertberechnung bestimmt wird"}}, "update_button": "Update"}}, "onboarding": {"main": {"bar": {"message": "Stripe muss konfiguriert werden, bevor <PERSON> verwenden können"}, "dialog": {"title": "Onboarding", "has_stripe_key": {"text": "Gültige Stripe-API-Schlüssel eingeben", "button": "<PERSON><PERSON> e<PERSON>"}, "has_stripe_imports": {"text": "<PERSON><PERSON> von <PERSON> importieren", "button": "Importieren", "dismiss": "<PERSON><PERSON><PERSON><PERSON>"}, "has_product": {"text": "Erstes Produkt erstellen", "button": "Produkt erstellen"}, "has_subscription_plan": {"text": "<PERSON><PERSON> er<PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON>"}, "has_customer": {"text": "<PERSON><PERSON>legen", "button": "<PERSON><PERSON><PERSON><PERSON>"}, "has_subscription": {"text": "Erstes Abonnement erstellen", "button": "<PERSON><PERSON><PERSON><PERSON>"}}, "error": "Etwas ist schief gelaufen!"}}, "default_error_message": "Etwas ist schief gelaufen!", "metric": {"list": {"title": "<PERSON><PERSON><PERSON>", "create": "erstellen.", "name": "Name", "no_metrics": "Es gibt noch keine Metriken!", "filter": {"name": "Name"}, "view_btn": "<PERSON><PERSON><PERSON>"}, "create": {"title": "<PERSON><PERSON>", "fields": {"name": "Name", "code": "Code", "type": "<PERSON><PERSON>", "aggregation_method": "Aggregationsmethode", "aggregation_property": "Aggregationseigenschaft", "ingestion": "Verschlucken", "filters": "Filter"}, "help_info": {"name": "Der Name der Metrik", "code": "Der Code, der in Api-Aufrufen verwendet werden soll. Nur Kleinbuchstaben, Zahlen und Unterstriche.", "type": "Wenn der Zähler für den Kunden am Ende eines Abonnementzeitraums zurückgesetzt werden soll", "aggregation_method": "Wie die Ereignisse, die an BillaBear gesendet werden, zusammengefasst werden sollen.", "aggregation_property": "Welche Eigenschaft in den Ereignisdaten für die Aggregation verwendet werden soll.", "ingestion": "Wie oft Ereignisse verarbeitet werden sollen", "filters": "<PERSON> Filter, die auf die bei der Aggregation auszuschließenden Ereignis-Payloads angewendet werden sollen"}, "aggregation_methods": {"count": "Zählen Sie", "sum": "Summe", "latest": "Neueste", "unique_count": "Einzigartige Anzahl", "max": "Max"}, "ingestion": {"real_time": "Echtzeit", "hourly": "<PERSON><PERSON><PERSON><PERSON>", "daily": "Tä<PERSON><PERSON>"}, "filter": {"name": "Name", "value": "Wert", "type": "<PERSON><PERSON>", "no_filters": "<PERSON><PERSON>"}, "filter_type": {"inclusive": "Inklusive", "exclusive": "Exklusiv"}, "create_button": "erstellen."}, "view": {"title": "<PERSON><PERSON><PERSON>", "main": {"name": "Name", "code": "Code", "type": "<PERSON><PERSON>", "aggregation_method": "Aggregationsmethode", "aggregation_property": "Aggregationseigenschaft", "event_ingestion": "Verschlucken"}, "filters": {"title": "Filter", "name": "Name", "value": "Wert", "type": "<PERSON><PERSON>", "inclusive": "Inklusive", "exclusive": "Exklusiv"}, "update": "Update"}, "update": {"title": "Metrik aktualisieren", "update_button": "Speichern Sie"}}, "usage_limit": {"create": {"title": "Verwendungsbeschränkung erstellen", "fields": {"amount": "Betrag", "action": "Aktion"}, "help_info": {"amount": "Der Betrag, auf den Si<PERSON> den Kunden beschränken wollen, bevor <PERSON> ergriffen werden", "action": "Die Aktion, die bei Überschreiten des Limits erfolgen soll."}, "actions": {"warn": "<PERSON><PERSON><PERSON>", "disable": "Deaktivieren Sie"}, "submit": "erstellen."}}, "customer_support": {"integration": {"title": "Integrierte Kundenbetreuung", "fields": {"integration": "Integration", "api_key": "API-Schlüssel", "enabled": "Aktiviert"}, "buttons": {"connect": "Verbindung über OAuth", "disconnect": "Trennen Sie die Verbindung", "save": "Speichern Sie"}, "settings": {"title": "Einstellungen"}, "errors": {"required": "<PERSON><PERSON> ist erford<PERSON>lich", "invalid": "<PERSON><PERSON>ld ist ungültig", "complete_error": "<PERSON><PERSON>, diese Einstellungen zu speichern, ist ein Fehler aufgetreten. Bitte versuchen Si<PERSON> es erneut."}, "zendesk": {"token": "Token", "subdomain": "Subdomain", "username": "<PERSON><PERSON><PERSON><PERSON>"}, "freshdesk": {"subdomain": "Subdomain", "api_key": "API-Schlüssel"}}}, "integrations": {"newsletter": {"title": "Newsletter-Integration", "fields": {"marketing_list": "Marketing-Liste", "announcement_list": "Ankündigungsliste"}, "no_lists": "Keine Listen verfügbar. Geben Si<PERSON> zu<PERSON>t die Verbindungsdaten ein.", "errors": {"list_required": "<PERSON>e können erst dann aktivieren, wenn Sie eine Liste ausgewählt haben. Geben Sie die Verbindungsdaten ein, speichern Si<PERSON> und wählen Sie dann eine Liste aus."}, "mailchimp": {"fields": {"server_prefix": "Server-Präfix"}}}, "menu": {"main": "Integrationen", "accounting": "Buchhaltung", "customer_support": "Kundenbetreuung", "newsletter": "Newsletter", "notifications": "Benachrichtigungen", "crm": "CRM"}, "general": {"fields": {"integration": "Integration", "api_key": "API-Schlüssel", "enabled": "Aktiviert"}, "buttons": {"connect": "Verbindung über OAuth", "disconnect": "Trennen Sie die Verbindung", "save": "Speichern Sie"}, "settings": {"title": "Einstellungen"}, "errors": {"required": "<PERSON><PERSON> ist erford<PERSON>lich", "invalid": "<PERSON><PERSON>ld ist ungültig", "complete_error": "<PERSON><PERSON>, diese Einstellungen zu speichern, ist ein Fehler aufgetreten. Bitte versuchen Si<PERSON> es erneut."}}, "crm": {"title": "CRM-Integrationen", "fields": {"integration": "Integration"}, "buttons": {"connect": "Verbindung über Oauth", "disconnect": "Trennen Sie die Verbindung", "save": "Speichern Sie"}}}, "compliance": {"audit": {"all": {"title": "Audit-Protokoll", "log": "Protokoll", "date": "Datum", "billing_admin": "Angemeldeter Abrechnungsadministrator", "no_billing_admin": "Dies wurde nicht von einem Verwalter der Rechnungsstellung getan", "display_name": "Name anzeigen", "context": "Log-Kontext", "no_logs": "<PERSON><PERSON> gefunden"}, "customer": {"title": "Kundenauditprotokoll - {Name}"}, "billing_admin": {"title": "Billing Admin Audit Log - {Name}"}}}}, "install": {"title": "Installieren Sie", "submit_button": "Installieren Sie", "user": {"title": "<PERSON><PERSON><PERSON>", "email": "E-Mail", "password": "Passwort"}, "settings": {"title": "System-Einstellungen", "default_brand": "Standard-Markenname", "from_email": "Standard-E-Mail-<PERSON><PERSON><PERSON> von", "timezone": "Zeitzone", "webhook_url": "Basis-Url", "currency": "Währung", "country": "Land"}, "complete_text": "BillaBear wurde installiert! Sie können sich nun mit den von Ihnen angegebenen Daten anmelden.", "login_link": "<PERSON><PERSON><PERSON> Si<PERSON> hier zum Anmelden", "unknown_error": "Unbek<PERSON><PERSON> Fehler.", "stripe": {"no_api_key": "Sie müssen einen Stripe-API-Schlüssel in der ENV-Variablen STRIPE_PRIVATE_API_KEY angeben.", "doc_link": "Weitere Informationen über die Einrichtung von BillaBear.", "invalid_api_key": "Der Stripe-API-Schlüssel ist ungültig", "support_link": "<PERSON>e können hier um Hilfe bitten."}}}