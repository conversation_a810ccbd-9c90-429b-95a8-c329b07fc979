Feature: Create Credit

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   |
      | <EMAIL>   | UK      | cust_dfugfdu       | Customer Two   |
      | <EMAIL> | UK      | cust_dljkjng       | Customer three |

  Scenario: Create credit
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a credit for "<EMAIL>" for 1000 in the currency "USD"
    Then there should be a credit for "<EMAIL>" for 1000 in the currency "USD"
    And there should be a credit created by "<EMAIL>"

  Scenario: Create debit
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the following credit transactions exist:
      | Customer                 | Type   | Amount | Currency |
      | <EMAIL> | credit | 1000   | USD      |
    When I create a debit for "<EMAIL>" for 1000 in the currency "USD"
    Then the credit amount for "<EMAIL>" should be 0