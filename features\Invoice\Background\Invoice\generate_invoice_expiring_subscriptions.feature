Feature: Generate new invoices

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
      | Product Two | prod_jf9j542       |
    And there are the following tax types:
      | Name             | Physical |
      | Digital Goods    | False    |
      | Digital Services | False    |
      | Physical         | True     |
    And that the following countries exist:
      | Name           | ISO Code | Threshold | Currency | In EU |
      | United Kingdom | GB       | 1770      | GBP      | False |
      | United States  | US       | 0         | USD      | False |
      | Germany        | DE       | 0         | EUR      | True  |
    And the following country tax rules exist:
      | Country        | Tax Type      | Tax Rate | Valid From |
      | United States  | Digital Goods | 17.5     | -10 days   |
      | United Kingdom | Digital Goods | 20       | -10 days   |
      | Germany        | Digital Goods | 17.5     | -10 days   |
      | Germany        | Physical      | 10.5     | -10 days   |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name             | Test Plan |
      | Public           | True      |
      | Per Seat         | False     |
      | User Count       | 10        |
      | Standalone Trial | true      |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Two |
      | Public     | True     |
      | Per Seat   | False    |
      | User Count | 10       |
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      | Billing Type | Payment Reference | Tax Number |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   | invoice      | null              | FJDKSLfjdf |
      | <EMAIL>   | GB      | cust_dfugfdu       | Customer Two   | card         | ref_valid         | ssdfds     |
      | <EMAIL> | GB      | cust_mlklfdu       | Customer Three | card         | ref_valid         | gfdgsfd    |
      | <EMAIL>  | GB      | cust_dkkoadu       | Customer Four  | card         | ref_fails         | 35435 43   |
      | <EMAIL>  | GB      | cust_ddsjfu        | Customer Five  | card         | ref_valid         | dfadf      |
      | <EMAIL>   | GB      | cust_jliujoi       | Customer Six   | card         | ref_fails         | fdsafd     |


  Scenario:
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Cancelled |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |
    And stripe billing is disabled
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And the subscription for "<EMAIL>" will expire in a month
    And the subscription for "<EMAIL>" will expire in a year
    But the subscription for "<EMAIL>" will expire today
    And the subscription for "<EMAIL>" will expire today
    And the subscription for "<EMAIL>" will expire today
    And the payment amount stats for the day should be 33000 in the currency "USD"

  Scenario:
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                  | Next Charge | Status |
      | Test Plan         | 1000         | USD            | week           | <EMAIL> | +3 Minutes  | Active |
    And stripe billing is disabled
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And there the latest invoice for "<EMAIL>" will not be marked as paid
    And there is a payment attempt for "<EMAIL>" will exist

  Scenario:
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Cancelled |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |
    And stripe billing is enabled
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And the subscription for "<EMAIL>" will expire today
    And the subscription for "<EMAIL>" will expire today
    But the subscription for "<EMAIL>" will expire today
    And the subscription for "<EMAIL>" will expire today
    And the subscription for "<EMAIL>" will expire today

  Scenario:
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Cancelled |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |
    And stripe billing is enabled
    And the following credit transactions exist:
      | Customer                 | Type   | Amount | Currency |
      | <EMAIL> | credit | 100    | USD      |
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And the latest invoice for "<EMAIL>" will have amount due as 900

  Scenario:
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Cancelled |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |
    And stripe billing is enabled
    And the following credit transactions exist:
      | Customer                 | Type   | Amount | Currency |
      | <EMAIL> | credit | 1100   | USD      |
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And the latest invoice for "<EMAIL>" will have amount due as 0
    And the credit amount for "<EMAIL>" should be 100

  Scenario:
    Given that the tax settings for tax customers with tax number is true
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Cancelled |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |
    And stripe billing is enabled
    And the following credit transactions exist:
      | Customer                 | Type  | Amount | Currency |
      | <EMAIL> | debit | 100    | USD      |
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And the latest invoice for "<EMAIL>" will have amount due as 1100
    And the latest invoice for "<EMAIL>" will have tax amount due

  Scenario: No tax
    Given that the tax settings for tax customers with tax number is false
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Cancelled |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |
    And stripe billing is enabled
    And the following credit transactions exist:
      | Customer                 | Type  | Amount | Currency |
      | <EMAIL> | debit | 100    | USD      |
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And the latest invoice for "<EMAIL>" will have amount due as 1100
    And the latest invoice for "<EMAIL>" will not have tax amount due

  Scenario: Has voucher
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Cancelled |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |
    And stripe billing is enabled
    And the following vouchers exist:
      | Name        | Type       | Entry Type | Code | Percentage Value | USD | GBP | Disabled |
      | Voucher One | Percentage | Automatic  | n/a  | 50               | n/a | n/a | false    |
    And the customer "<EMAIL>" has the voucher "Voucher One" applied
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And the latest invoice for "<EMAIL>" will have amount due as 500

  Scenario: Has voucher that was used
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Cancelled |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |
    And stripe billing is enabled
    And the following vouchers exist:
      | Name        | Type       | Entry Type | Code | Percentage Value | USD | GBP | Disabled |
      | Voucher One | Percentage | Automatic  | n/a  | 50               | n/a | n/a | false    |
    And the customer "<EMAIL>" has the voucher "Voucher One" applied that has been used
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And the latest invoice for "<EMAIL>" will have amount due as 1000


  Scenario:
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Cancelled |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |
    And stripe billing is enabled
    And the following credit transactions exist:
      | Customer                 | Type   | Amount | Currency |
      | <EMAIL> | credit | 100    | USD      |
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And the latest invoice for "<EMAIL>" will have amount due as 900
    And the latest invoice for "<EMAIL>" will be due in "30 days"

  Scenario: Handle credit in the wrong currency
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Cancelled |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |
    And stripe billing is enabled
    And the following credit transactions exist:
      | Customer                 | Type   | Amount | Currency |
      | <EMAIL> | credit | 100    | GBP      |
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week

  Scenario: Only subscription
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 | Next Charge | Status       |
      | Test Plan         | 1000         | USD            | week           | <EMAIL> | +3 Minutes  | trial_active |
    When the background task to reinvoice active subscriptions
    Then there will be no error logs for invoice

  Scenario: Subscription with Metadata
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                  | Next Charge | Status | Seats | Metadata         |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Active | 50    | {"key": "value"} |
    And stripe billing is disabled
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And there the latest invoice for "<EMAIL>" will be for 50000 "USD"
    And the latest invoice for "<EMAIL>" should have metadata '{"key": "value"}'
