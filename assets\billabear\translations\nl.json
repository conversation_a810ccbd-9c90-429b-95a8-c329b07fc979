{"global": {"loading": "Laden", "country": {"AU": "Australië", "BE": "België", "CA": "Canada", "HR": "Kroatië", "CZ": "Tsjechië", "DK": "<PERSON><PERSON><PERSON><PERSON>", "EE": "Estland", "FI": "Finland", "FR": "<PERSON><PERSON><PERSON>", "DE": "Duitsland", "GR": "Griekenland", "HU": "<PERSON><PERSON><PERSON>", "IS": "IJsland", "LV": "Letland", "LI": "Liechtenstein", "LT": "<PERSON><PERSON><PERSON><PERSON>", "LU": "Luxemburg", "GB": "Verenigd Koninkrijk", "US": "Verenigde Staten", "NL": "Nederland", "RO": "Roemenië", "SK": "<PERSON><PERSON><PERSON>", "SI": "Slovenië", "ES": "Spanje", "SE": "Zweden", "AF": "Afghanistan", "AL": "Albanië", "DZ": "Algerije", "AD": "Andorra", "AO": "Angola", "AI": "<PERSON><PERSON><PERSON>", "AQ": "Antarctica", "AG": "Antigua en Barbuda", "AR": "Argentinië", "AM": "Armenië", "AW": "Aruba", "AT": "Oostenrijk", "AZ": "Azerbeidzjan", "BS": "<PERSON><PERSON><PERSON>'s", "BH": "<PERSON><PERSON><PERSON>", "BD": "Bangladesh", "BB": "Barbados", "BY": "Wit-Rusland", "BZ": "Belize", "BJ": "Benin", "BM": "Bermuda", "BT": "Bhutan", "BO": "Bolivia", "BA": "Bosnië en Herzegovina", "BW": "Botswana", "BR": "Brazilië", "IO": "Brits Territorium in de Indische Oceaan", "BN": "Brunei Darussalam", "BG": "Bulgarije", "BF": "Burkina Faso", "BI": "Burundi", "CV": "Cabo Verde", "KH": "Cambodja", "CM": "Kameroen", "KY": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CF": "Centraal-Afrikaanse Republiek", "TD": "Chad", "CL": "<PERSON><PERSON>", "CN": "China", "CX": "Kerstmis Eiland", "CC": "Cocos (Keeling) Eilanden", "CO": "Colombia", "KM": "<PERSON><PERSON>", "CG": "Congo", "CD": "Congo, Democratische Republiek Congo", "CK": "<PERSON>", "CR": "Costa Rica", "CI": "Ivoorkust", "CU": "Cuba", "CY": "Cyprus", "DJ": "Djibouti", "DM": "Dominica", "DO": "<PERSON><PERSON><PERSON>", "EC": "Ecuador", "EG": "Egypte", "SV": "El Salvador", "GQ": "Equatoriaal-Guinea", "ER": "Eritrea", "SZ": "<PERSON><PERSON><PERSON><PERSON>", "ET": "Ethiopië", "FK": "Falklandeilanden (Malvinas)", "FO": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FJ": "Fiji", "GF": "Frans <PERSON>", "PF": "Frans Polynesië", "GA": "Gabon", "GM": "Gambia", "GE": "Georgië", "GH": "Ghana", "GI": "Gibraltar", "GL": "Groenland", "GD": "Grenada", "GP": "Guadeloupe", "GT": "Guatemala", "GG": "Guernsey", "GN": "Guinee", "GW": "Guinee-Bissau", "GY": "Guyana", "HT": "<PERSON><PERSON><PERSON>", "HN": "Honduras", "HK": "Hongkong", "IN": "India", "ID": "Indonesië", "IR": "Iran, Islamitische Republiek", "IQ": "<PERSON><PERSON>", "IE": "Ierland", "IM": "Eiland Man", "IL": "<PERSON><PERSON><PERSON>", "IT": "Italië", "JM": "Jamaica", "JP": "Japan", "JE": "Jersey", "JO": "Jordan", "KZ": "Kazachstan", "KE": "Ken<PERSON>", "KI": "Kiribati", "KP": "Korea, Democratische Volksrepubliek", "KR": "Korea", "KW": "Koeweit", "KG": "Kirgizië", "LA": "Democratische Volksrepubliek Laos", "LB": "Libanon", "LS": "Lesotho", "LR": "Liberia", "LY": "Libië", "MO": "Macau", "MG": "<PERSON><PERSON><PERSON>", "MW": "Malawi", "MY": "Maleisië", "MV": "Malediven", "ML": "Mali", "MT": "Malta", "MH": "Marshalleil<PERSON><PERSON>", "MQ": "Martinique", "MR": "Mauritanië", "MU": "Mauritius", "YT": "Mayotte", "MX": "Mexico", "FM": "Micronesië, Federale Staten van", "MD": "Moldavië, Republiek", "MC": "Monaco", "MN": "Mongolië", "ME": "Montenegro", "MS": "Montserrat", "MA": "<PERSON><PERSON><PERSON>", "MZ": "Mozambique", "MM": "Myanmar", "NA": "Namibië", "NR": "Nauru", "NP": "Nepal", "NC": "Nieuw-Caledonië", "NZ": "Nieuw-Zeeland", "NI": "Nicaragua", "NE": "Niger", "NG": "Nigeria", "NU": "Niue", "NF": "Norfolk Eiland", "MK": "Noord-Macedonië", "NO": "Noorwegen", "OM": "Oman", "PK": "Pakistan", "PW": "<PERSON><PERSON>", "PS": "Palestina, Staat", "PA": "Panama", "PG": "Papoea-Nieuw-Guinea", "PY": "Paraguay", "PE": "Peru", "PH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PN": "Pitcairn", "PL": "<PERSON><PERSON>", "PT": "Portugal", "QA": "Qatar", "RE": "Réunion", "RU": "Russische Federatie", "RW": "Rwanda", "BL": "<PERSON>", "SH": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> en <PERSON>", "KN": "Saint Kitts en Nevis", "LC": "Saint Lucia", "MF": "<PERSON><PERSON> Ma<PERSON>n (Frans deel)", "PM": "Saint-<PERSON> en Miquelon", "VC": "Saint <PERSON> en de Grenadines", "WS": "Samoa", "SM": "San Marino", "ST": "Sao Tomé en Principe", "SA": "Saoedi-Arabië", "SN": "Senegal", "RS": "Servië", "SC": "<PERSON><PERSON><PERSON><PERSON>", "SL": "Sierra Leone", "SG": "Singapore", "SX": "Sint Maarten (Nederlands deel)", "SB": "Salomoneilanden", "SO": "Somalië", "ZA": "Zuid-Afrika", "GS": "Zuid-Georgië en de Zuidelijke Sandwicheilanden", "SS": "Zuid-Soedan", "LK": "Sri Lanka", "SD": "Soedan", "SR": "Suriname", "SJ": "Spitsbergen en Jan Mayen", "CH": "Zwitserland", "SY": "Arabische Republiek Syrië", "TW": "Taiwan, provincie China", "TJ": "Tadzjikistan", "TZ": "Tanzania, Verenigde Republiek", "TH": "Thailand", "TL": "Oost-Timor", "TG": "Togo", "TK": "Tokelau", "TO": "Tonga", "TT": "Trinidad en Tobago", "TN": "Tunesië", "TR": "<PERSON><PERSON><PERSON><PERSON>", "TM": "Turkmenistan", "TC": "Turks- en Caicoseilanden", "TV": "Tuvalu", "UG": "Oeganda", "UA": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AE": "Verenigde Arabische Emiraten", "UM": "Amerikaanse ondergeschikte afgelegen eilanden", "UY": "Uruguay", "UZ": "Oezbekistan", "VU": "Vanuatu", "VE": "Venezuela", "VN": "Vietnam", "VG": "Maagdeneilanden, Britse", "VI": "Maagdeneilanden, VS", "WF": "Wallis en Futuna", "EH": "Westelijke Sahara", "YE": "<PERSON><PERSON>", "ZM": "Zambia", "ZW": "Zimbabwe"}, "select_country": "Selecteer land"}, "public": {"login": {"title": "Inloggen", "email": "E-mail", "password": "Wachtwoord", "login_button": "Inloggen", "remember_me_label": "<PERSON><PERSON><PERSON> mij", "forgot_password_link": "Wachtwoord vergeten?", "signup_link": "Aanmelden voor een account", "logging_in": "Inloggen"}, "signup": {"title": "Aanmelden", "email": "E-mail", "email_error": "E-mail moet worden opgegeven", "email_invalid_error": "Er moet een geldig e-mailadres worden opgegeven", "password": "Wachtwoord", "password_error": "Wachtwoord moet worden opgegeven", "password_confirm": "Wachtwoord bevestigen", "password_confirm_error": "Het wachtwoord moet overeenkomen met", "signup_button": "Aanmelden", "signing_up": "In uitvoering", "remember_me_label": "<PERSON><PERSON><PERSON> mij", "forgot_password_link": "Wachtwoord vergeten?", "login_link": "Heb je al een account? Nu inloggen.", "success_message": "Je hebt je succesvol aangemeld. Controleer uw e-mail."}, "forgot_password": {"title": "Wachtwoord opnieuw instellen", "email": "E-mail", "email_error": "Er moet een e-mailadres worden opgegeven.", "in_progress": "In uitvoering", "login_link": "Wachtwoord vergeten? Inloggen", "success_message": "Controleer uw e-mail", "request_button": "Wachtwoord opnieuw instellen"}, "forgot_password_confirm": {"title": "Wachtwoord opnieuw instellen", "password": "Wachtwoord", "password_error": "Er moet een wachtwoord worden opgegeven.", "password_length_error": "Het wachtwoord moet minstens 7 tekens lang zijn", "password_confirm": "Bevestig", "password_confirm_error": "De wachtwoorden moeten overeenkomen", "reset_button": "Wachtwoord opnieuw instellen", "in_progress": "In uitvoering", "login_link": "<PERSON><PERSON> hier om in te loggen.", "success_message": "<PERSON>w wachtwoord is opnieuw ingesteld. U kunt nu inloggen.", "request_button": "Wachtwoord opnieuw instellen"}, "confirm_email": {"error_message": "Dit is een ongeldige link", "success_message": "Uw e-mailadres is nu bevestigd en u kunt inloggen.", "login_link": "<PERSON><PERSON> hier om in te loggen."}}, "app": {"menu": {"main": {"reports": "Rapporten", "subscriptions": "Abonnementen", "finance": "Financiën", "settings": "Instellingen", "customers": "Klanten", "products": "<PERSON><PERSON>", "invoices": "<PERSON><PERSON><PERSON>", "system": "Systeem", "docs": "Documentatie", "workflows": "Werkstromen", "developers": "Ontwikkelaars", "home": "Home", "customer_list": "Klantenlijst", "mobile": {"show": "<PERSON><PERSON> weer<PERSON>", "hide": "<PERSON>u verbergen"}, "tax": "Belasting", "customer_support_integrations": "Ondersteuning Integraties"}}, "team": {"main": {"title": "Team Instellingen", "add_team_member": "<PERSON><PERSON> toe<PERSON>n"}, "invite": {"title": "<PERSON><PERSON> toe<PERSON>n", "close": "Sluit", "email": "E-mail", "invite_successfully_sent": "De uitnodiging is succesvol verzonden.", "send": "Uitnodiging versturen", "sending": "Verzenden", "send_another": "Nog een sturen"}, "pending_invites": {"title": "In afwachting van uitnodigingen", "none": "<PERSON>r zijn geen uitnodigingen in behandeling", "email": "E-mail", "invited_at": "Uitgenodigd bij", "cancel": "<PERSON><PERSON><PERSON>", "cancelling": "<PERSON><PERSON><PERSON>"}, "members": {"email": "E-mail", "created_at": "<PERSON><PERSON><PERSON><PERSON> bij", "disable": "Uitschakelen", "disabling": "In uitvoering", "active": "Actief", "disabled": "Uitschakelen"}}, "plan": {"main": {"title": "Plan", "payment_schedule_yearly": "Jaarlijks", "payment_schedule_monthly": "Ma<PERSON><PERSON><PERSON><PERSON>", "payment_schedule_label": "Betalingsschema", "select_plan": "Selecteer plan", "selected_plan": "<PERSON>eel <PERSON>", "change": "Dit plan wijzigen", "payment_settings": "Betalingsinstellingen", "cancel_button": "<PERSON><PERSON><PERSON>", "in_progress": "Verwerking", "features": "Kenmerken", "your_current_plan": "Je huidige plan", "plan_options": "Planopties"}}, "user": {"settings": {"title": "Gebruikersinstellingen", "name": "<PERSON><PERSON>", "email": "E-mail", "password": "Wachtwoord", "locale": "Lokaal", "save": "Sla", "error_message": "Er is een probleem opgetreden bij het op<PERSON><PERSON> van de gebruikersinstellingen. Controleer de fouten.", "success_message": "Uw instellingen zijn opgeslagen.", "danger_zone": "Gevarenzone", "current_password": "<PERSON><PERSON><PERSON> wa<PERSON>", "new_password": "<PERSON><PERSON><PERSON> wa<PERSON>", "new_password_again": "Wachtwoord bevestigen", "change_password": "Wachtwoord wijzigen", "need_current_password": "U moet uw huidige wachtwoord opgeven", "need_new_password": "U moet een nieuw wachtwoord opgeven", "need_valid_password": "Wachtwoord moet meer dan 8 tekens bevatten", "need_password_to_match": "Wachtwoorden moeten overeenkomen", "in_progress": "In uitvoering"}, "invite": {"title": "Gebruiker uitnodigen", "email": "E-mail", "send": "<PERSON><PERSON><PERSON>", "in_progress": "In uitvoering", "success_message": "Uitnodiging succesvol verzonden!", "need_email": "Er moet een e-mail worden opgegeven", "error_message": "Kan uitnodiging niet verzenden.", "role": "Rol"}}, "billing": {"details": {"title": "Factureringsgegevens", "street_line_one": "Straat Lijn Eén", "street_line_two": "Straat Lijn Twee", "city": "Stad", "region": "Staa<PERSON>", "country": "Land", "postal_code": "Postcode", "submit": "Sla"}, "main": {"title": "Facturering", "details": "Factureringsgegevens", "methods": "Betaalmethoden", "invoices": "<PERSON><PERSON><PERSON>"}, "card_form": {"name": "<PERSON><PERSON>", "number": "Kaartnummer", "exp_month": "Afloopmaand", "exp_year": "Vervaldatum Jaar", "cvc": "Beveiligingscode", "add_card": "<PERSON><PERSON>"}, "payment_methods": {"title": "Betaalmethoden", "card_number": "Aantal", "card_expiry": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "is_default": "Standaard betalingsmethode", "make_default_btn": "<PERSON>aard maken", "delete_btn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add_card_btn": "<PERSON><PERSON><PERSON>", "no_saved_payment_methods": "<PERSON><PERSON> <PERSON>ige betaalmethoden"}}, "customer": {"list": {"title": "Klanten", "email": "E-mail", "country": "Land", "reference": "Referentie", "no_customers": "<PERSON><PERSON> <PERSON>ijn <PERSON>eel geen bestaande klanten", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "view_btn": "Bekijk", "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter", "no_filters": "Geen filters", "country": "Land", "company_name": "Bedrijfsnaam"}, "loading": "Resultaten laden", "error_message": "Er is een fout opgetreden", "company_name": "Bedrijfsnaam"}, "create": {"title": "Nieuwe klant aanmaken", "email": "E-mail", "street_line_one": "Straat Lijn 1", "street_line_two": "Straatlijn 2", "city": "Stad", "region": "Regio", "country": "Land", "post_code": "Postcode", "reference": "Referentie", "external_reference": "Externe referentie", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "<PERSON><PERSON>", "show_advanced": "Geavanceerd", "success_message": "Succesvol klant gemaakt", "address_title": "<PERSON><PERSON>", "locale": "Lokaal", "billing_type": "Type factuur", "billing_type_card": "<PERSON><PERSON>", "billing_type_invoice": "<PERSON><PERSON><PERSON><PERSON>", "company_name": "Bedrijfsnaam", "brand": "Merk", "tax_number": "Belastingnummer", "standard_tax_rate": "Standaard belastingtarief", "type": "Type klant", "type_business": "<PERSON><PERSON><PERSON><PERSON>", "type_individual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "help_info": {"email": "Het e-mail<PERSON><PERSON> van de k<PERSON> waar de facturen naartoe moeten gaan", "locale": "De locale die moet worden gebruikt voor taal", "company": "<PERSON> van het bed<PERSON>", "street_line_one": "De eerste regel van het factuuradres", "street_line_two": "De tweede regel van het factuuradres", "city": "De stad voor het factuuradres", "region": "De regio/staat voor het factuuradres", "country": "Het factureringsland van de klant - ISO 3166-1 alpha-2 landcode.", "post_code": "De postcode voor het factuuradres", "reference": "Uw interne referentie voor de klant", "billing_type": "Hoe de klant gefactureerd moet worden. Kaart betekent dat betalingen automatisch worden gedaan via een kaart die is geregistreerd. Factuur betekent dat ze een factuur ontvangen en handmatig betalen", "external_reference": "De referentie voor de klant die wordt gebruikt door de betalingsprovider. Laat leeg tenzij je heel zeker weet dat je de juiste referentie hebt.", "brand": "Het merk waartoe de klant behoort.", "tax_number": "Het belastingnummer voor de klant", "standard_tax_rate": "Het belastingtarief dat moet worden toegepast voor de klant voor alles behalve digitale diensten", "type": "<PERSON><PERSON> <PERSON> een bed<PERSON> of particulier is", "invoice_format": "Het formaat dat moet worden gebruikt bij het maken en afleveren van een factuur"}, "failed_message": "Kan geen klant a<PERSON>ken", "invoice_format": "Factuurformaat", "metadata": {"title": "Metagegevens", "name": "<PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "no_values": "<PERSON>n metagegevenswaarden", "add": "Metagegevens <PERSON>"}}, "view": {"title": "Klantgegevens be<PERSON>jken", "update": "Update", "disable": "Uitschakelen", "enable": "Inschakelen", "error": {"not_found": "<PERSON><PERSON> dergelijke klant gevonden", "unknown": "Er is een onbekende fout opgetreden"}, "main": {"title": "Belangrijkste gegevens", "email": "E-mail", "reference": "Interne referentie", "external_reference": "Externe referentie", "status": "Status", "locale": "Lokaal", "brand": "Merk", "billing_type": "Type factuur", "tax_number": "Belastingnummer", "standard_tax_rate": "Standaard belastingtarief", "type": "Type", "marketing_opt_in": "Marketing Opt-in"}, "address": {"company_name": "Bedrijfsnaam", "title": "<PERSON><PERSON>", "street_line_one": "Straat Lijn 1", "street_line_two": "Straatlijn 2", "city": "Stad", "region": "Regio", "post_code": "Postcode", "country": "Land"}, "credit_notes": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>'s", "list": {"amount": "Bedrag", "currency": "Valuta", "created_by": "Gemaakt door", "created_at": "Gemaakt op"}, "no_credit_notes": "<PERSON><PERSON>'s voor deze klant"}, "credit": {"title": "Kredietaanpassingen", "list": {"amount": "Bedrag", "currency": "Valuta", "created_by": "Gemaakt door", "created_at": "Gemaakt op"}, "no_credit": "<PERSON>n krediet voor deze klant", "add_button": "Voeg  toe"}, "subscriptions": {"title": "Abonnementen", "list": {"plan_name": "Plan", "status": "Status", "schedule": "<PERSON><PERSON><PERSON>", "created_at": "Gemaakt op", "valid_until": "Volgende gefactureerd", "view": "Bekijk"}, "add_new": "Nieuw abonnement toevoegen", "no_subscriptions": "<PERSON><PERSON>"}, "subscription_events": {"title": "Abonnement Evenementen", "list": {"event": "Evenement", "subscription": "Abonnement", "created_at": "Gemaakt op"}, "no_subscription_events": "<PERSON><PERSON>evenement<PERSON>"}, "payments": {"title": "<PERSON>lingen", "list": {"amount": "Bedrag", "currency": "Valuta", "status": "Status", "created_at": "Gemaakt op"}, "no_payments": "Nog geen betalingen voor deze klant"}, "refunds": {"title": "Terugbetalingen", "list": {"amount": "Bedrag", "currency": "Valuta", "created_by": "Gemaakt door", "created_at": "Gemaakt op"}, "no_refunds": "<PERSON>n restitutie voor deze klant"}, "payment_details": {"title": "Betalingsgegevens", "list": {"brand": "Merk", "last_four": "Laatste vier", "default": "Wanbetaling", "expiry_month": "Afloopmaand", "expiry_year": "Vervaldatum Jaar", "name": "<PERSON><PERSON>"}, "add_token": "Met token", "add_new": "<PERSON><PERSON><PERSON>", "no_payment_details": "<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "make_default": "<PERSON>aard maken"}, "limits": {"title": "<PERSON><PERSON><PERSON>", "list": {"feature": "<PERSON><PERSON><PERSON>", "limit": "Beperk"}, "no_limits": "<PERSON><PERSON> g<PERSON>"}, "features": {"title": "Kenmerken", "list": {"feature": "<PERSON><PERSON><PERSON>"}, "no_features": "<PERSON><PERSON> functies"}, "invoices": {"title": "<PERSON><PERSON><PERSON>", "list": {"amount": "Bedrag", "currency": "Valuta", "status": "Status", "outstanding": "Uitmuntend", "overdue": "Achterstallig", "paid": "<PERSON><PERSON>", "created_at": "Gemaakt op", "view_btn": "Bekijk"}, "no_invoices": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige"}, "invoice_delivery": {"title": "Factuurlevering", "add_new": "<PERSON><PERSON><PERSON>", "list": {"method": "<PERSON>e", "format": "Form<PERSON><PERSON>", "detail": "Detail", "view": "Bekijk"}, "no_delivery_methods": "<PERSON><PERSON> leveringsmetho<PERSON>"}, "metric_counters": {"title": "Metrische tellers", "list": {"name": "<PERSON><PERSON>", "usage": "Gebruik", "cost": "Geschatte kosten"}, "no_counters": "<PERSON>r zijn geen metrische tellers"}, "usage_limits": {"title": "Gebruiksbeperkingen", "add_new": "<PERSON><PERSON><PERSON>", "list": {"amount": "Bedrag", "warn_level": "<PERSON><PERSON>"}, "warn_levels": {"warn": "Waar<PERSON>u<PERSON>", "disable": "uitschakelen"}, "no_limits": "<PERSON>r zijn geen gebruikslimieten voor deze klant"}, "metadata": {"title": "Metagegevens", "no_metadata": "<PERSON><PERSON> meta<PERSON>s"}, "audit_log": "Controlelogboek"}, "update": {"title": "Update klant", "email": "E-mail", "street_line_one": "Straat Lijn 1", "street_line_two": "Straatlijn 2", "city": "Stad", "region": "Regio", "country": "Land", "post_code": "Postcode", "reference": "Referentie", "company_name": "Bedrijfsnaam", "external_reference": "Externe referentie", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "Bijgewerkt", "show_advanced": "Geavanceerd", "success_message": "Klant succesvol bijwerken", "address_title": "<PERSON><PERSON>", "tax_number": "Belastingnummer", "standard_tax_rate": "Standaard belastingtarief", "locale": "Lokaal", "error": {"not_found": "<PERSON><PERSON> dergelijke klant gevonden", "unknown": "Er is een onbekende fout opgetreden"}, "billing_type": "Type factuur", "billing_type_card": "<PERSON><PERSON>", "billing_type_invoice": "<PERSON><PERSON><PERSON><PERSON>", "type": "Type klant", "type_business": "<PERSON><PERSON><PERSON><PERSON>", "type_individual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "help_info": {"email": "Het e-mail<PERSON><PERSON> van de k<PERSON> waar de facturen naartoe moeten gaan", "locale": "De locale die moet worden gebruikt voor taal", "company_name": "<PERSON> van het bed<PERSON>", "street_line_one": "De eerste regel van het factuuradres", "street_line_two": "De tweede regel van het factuuradres", "city": "De stad voor het factuuradres", "region": "De regio/staat voor het factuuradres", "country": "Het factureringsland van de klant - ISO 3166-1 alpha-2 landcode.", "post_code": "De postcode voor het factuuradres", "reference": "Uw interne referentie voor de klant", "billing_type": "Hoe de klant gefactureerd moet worden. Kaart betekent dat betalingen automatisch worden gedaan via een kaart die is geregistreerd. Factuur betekent dat ze een factuur ontvangen en handmatig betalen", "external_reference": "De referentie voor de klant die wordt gebruikt door de betalingsprovider. Laat leeg tenzij je heel zeker weet dat je de juiste referentie hebt.", "tax_number": "Het belastingnummer voor de klant", "standard_tax_rate": "Het belastingtarief dat moet worden toegepast voor de klant voor alles behalve digitale diensten", "type": "<PERSON><PERSON> <PERSON> een bed<PERSON> of particulier is", "invoice_format": "Het formaat dat moet worden gebruikt bij het maken en afleveren van een factuur", "marketing_opt_in": "Als de klant zich heeft aangemeld voor marketinge-mails. Dit heeft invloed op nieuwsbriefintegraties."}, "invoice_format": "Factuurformaat", "marketing_opt_in": "Marketing Opt-in", "metadata": {"title": "Metagegevens", "name": "<PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "no_values": "<PERSON>n metagegevenswaarden", "add": "Metagegevens <PERSON>"}}, "menu": {"title": "Klanten", "customers": "Klanten"}}, "product": {"list": {"title": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "physical": "Fysiek", "no_products": "<PERSON>r <PERSON>aan momenteel geen producten", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "filter": {"title": "Filters", "name": "<PERSON><PERSON>", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter"}, "error_message": "Er is een fout opgetreden"}, "create": {"title": "Nieuw product maken", "name": "<PERSON><PERSON>", "external_reference": "Externe referentie", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "Product maken", "show_advanced": "Geavanceerd", "success_message": "Succesvol gecreëerd product", "failed_message": "Product niet gemaakt", "tax_rate": "Belastingtarief", "tax_type": "Type belasting", "physical": "Fysiek", "tax_types": {"digital_services": "<PERSON><PERSON> diensten", "digital_goods": "<PERSON><PERSON> goederen", "physical": "Fysieke goederen/diensten"}, "help_info": {"name": "De naam van het product", "external_reference": "De referentie voor het product dat wordt gebruikt door de betalingsprovider. Laat leeg tenzij je heel zeker weet dat je de juiste referentie hebt.", "tax_type": "Dit is om te helpen bij het correct belasten. Fysieke goederen en diensten worden anders belast dan digitale goederen. En in sommige landen hebben ze een belasting op digitale diensten.", "tax_rate": "Het belastingtarief dat moet worden gebruikt voor dit product. Dit heeft voorrang op andere belastingtarieven.", "physical": "Is dit product fysiek?"}}, "view": {"title": "Bekijk productgegevens", "update": "Update", "error": {"not_found": "Geen dergelijk product gevonden", "unknown": "Er is een onbekende fout opgetreden"}, "main": {"title": "Belangrijkste gegevens", "name": "<PERSON><PERSON>", "physical": "Fysiek", "external_reference": "Externe referentie", "tax_rate": "Belastingtarief", "tax_type": "Type belasting", "tax_types": {"digital_services": "<PERSON><PERSON> diensten", "digital_goods": "<PERSON><PERSON> goederen", "physical": "Fysieke goederen/diensten"}}, "price": {"title": "Prijzen", "create": "Nieuwe prijs maken", "no_prices": "<PERSON><PERSON> <PERSON>ijn <PERSON> geen prijzen", "hide": "<PERSON>ri<PERSON><PERSON> priv<PERSON>n", "show": "<PERSON>ri<PERSON><PERSON> maken", "list": {"amount": "Bedrag", "currency": "Valuta", "recurring": "<PERSON><PERSON><PERSON><PERSON><PERSON> betaling", "schedule": "Betalingsschema", "including_tax": "Prijs inclusief belasting", "public": "Openbare prijs", "external_reference": "Externe referentie", "usage": "Gebruik"}}, "subscription_plan": {"title": "Abonnementen", "create": "Nieuw plan maken", "no_subscription_plans": "<PERSON><PERSON> zijn momenteel geen abonnementen", "view": "Bekijk", "list": {"name": "<PERSON><PERSON>", "external_reference": "Externe referentie", "code_name": "Code Naam"}}}, "update": {"title": "Product bijwerken", "name": "<PERSON><PERSON>", "external_reference": "Externe referentie", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "Bijgewerkt", "show_advanced": "Geavanceerd", "success_message": "Product succesvol bijwerken", "address_title": "<PERSON><PERSON>", "error": {"not_found": "Geen dergelijk product gevonden", "unknown": "Er is een onbekende fout opgetreden"}, "tax_type": "Type belasting", "tax_types": {"digital_services": "<PERSON><PERSON> diensten", "digital_goods": "<PERSON><PERSON> goederen", "physical": "Fysieke goederen/diensten"}, "tax_rate": "Belastingtarief", "help_info": {"name": "De naam van het product", "external_reference": "De referentie voor het product dat wordt gebruikt door de betalingsprovider. Laat leeg tenzij je heel zeker weet dat je de juiste referentie hebt.", "tax_type": "Dit is om te helpen bij het correct belasten. Fysieke goederen en diensten worden anders belast dan digitale goederen. En in sommige landen hebben ze een belasting op digitale diensten.", "tax_rate": "Het belastingtarief dat moet worden gebruikt voor dit product. Dit heeft voorrang op andere belastingtarieven."}}, "menu": {"title": "Product", "products": "<PERSON><PERSON>", "features": "Kenmerken", "vouchers": "Bonnen", "products_list": "Productlijst", "metrics": "Metriek"}}, "price": {"create": {"title": "Nieuwe prijs maken", "amount": "Bedrag", "external_reference": "Externe referentie", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "<PERSON><PERSON><PERSON><PERSON> prijs", "show_advanced": "Geavanceerd", "success_message": "Succesvol aangemaakte prijs", "schedule_label": "Betalingsschema", "currency": "Valuta", "recurring": "Is A terugkerend?", "including_tax": "Is de prijs inclusief btw?", "public": "<PERSON><PERSON><PERSON>", "help_info": {"amount": "De prijs is de munteenheid van het lagere niveau. Dus 1,00 USD is 100 en 9,99 is 999.", "display_amount": "Deze prijs zou {amount} zijn.", "external_reference": "De referentie voor het product dat wordt gebruikt door de betalingsprovider. Laat leeg tenzij je heel zeker weet dat je de juiste referentie hebt.", "recurring": "Is dit een terugkerende betaling of een<PERSON>ig?", "currency": "De valuta waarin de k<PERSON> moet betalen", "schedule": "<PERSON>e vaak de klant moet betalen", "including_tax": "Als je de belasting in de prijs wilt verbergen of als je de klant zelf de belasting wilt laten betalen", "public": "Als dit een openbaar weergegeven prijs is", "usage": "Als de klant wordt gefactureerd op basis van het gebruik van een metriek of per stoel.", "metric_type": "Als de verbruiksmetriek wordt gereset aan het einde van het betalingsschema en volledig wordt gebruikt voor facturering of continu en het verschil tussen de laatste factuur en de volgende factuur wordt gebruikt."}, "schedule": {"week": "Wekelijks", "month": "Ma<PERSON><PERSON><PERSON><PERSON>", "year": "Jaarlijks"}, "metric": "<PERSON><PERSON><PERSON>", "metric_type": "Metrisch type", "create_metric": "U moet een Metric maken", "metric_types": {"resettable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "continuous": "Doorlopend"}, "type": "Type", "types": {"fixed_price": "Vaste prijs", "package": "Pakket", "per_unit": "Per eenheid/plaats", "tiered_volume": "Trapsgewijs volume", "tiered_graduated": "Trapsgewijs"}, "usage": "Gebruik", "units": "Eenheden", "tiers": "Niveaus", "tiers_fields": {"first_unit": "Eerste eenheid", "last_unit": "Laatste eenheid", "unit_price": "Prijs per eenheid", "flat_fee": "Vaste vergo<PERSON>"}}}, "feature": {"list": {"title": "Kenmerken", "name": "<PERSON><PERSON>", "code": "Code", "reference": "Referentie", "no_features": "<PERSON>r <PERSON>aan momenteel geen functies", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter"}, "error_message": "Er is een fout opgetreden", "loading": "Functies voor laden"}, "create": {"title": "Nieuwe functie maken", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "<PERSON><PERSON><PERSON> maken", "show_advanced": "Geavanceerd", "success_message": "Functie succesvol gemaakt", "address_title": "<PERSON><PERSON>", "fields": {"name": "<PERSON><PERSON>", "code": "Codenaam", "description": "Beschrijving"}, "help_info": {"name": "De naam voor de functie", "code": "De codenaam voor de functie. Deze wordt gebruikt bij het registreren van een gebruik of bij het controleren van limieten.", "description": "De beschrijving voor de functie"}}}, "subscription_plan": {"create": {"title": "Nieuw abonnement maken", "main_section": {"title": "Belangrijkste gegevens", "fields": {"name": "<PERSON><PERSON>", "code_name": "Code Naam", "user_count": "Aantal gebruikers", "public": "Openbaar plan", "per_seat": "Per stoel", "free": "<PERSON><PERSON><PERSON>"}, "help_info": {"name": "De naam van het abonnement", "code_name": "De codenaam voor het plan dat moet worden gebruikt met de API.", "user_count": "Het aantal toegestane gebruikers voor dit plan", "public": "Is het plan beschikbaar voor het publiek of een aangepast plan?", "free": "Is dit een gratis plan?", "per_seat": "Wordt het plan aangerekend per stoel?"}}, "trial_section": {"title": "Details", "fields": {"has_trial": "<PERSON><PERSON><PERSON> proef", "is_trial_standalone": "Is proef op zichzelf staand", "trial_length_days": "<PERSON><PERSON> van het proces in dagen"}, "help_info": {"has_trial": "Als het plan standaard een proefperiode heeft", "trial_length_days": "Hoe lang het proces moet duren in dagen", "is_trial_standalone": "Als een Proefabonnement standalone is, heeft het geen prijs nodig en wordt het abonnement gepauzeerd aan het einde van het Proefabonnement"}}, "features_section": {"title": "Kenmerken", "columns": {"feature": "<PERSON><PERSON><PERSON>", "description": "Beschrijving"}, "create": {"name": "<PERSON><PERSON>", "code_name": "Code Naam", "description": "Beschrijving", "button": "<PERSON><PERSON>"}, "add_feature": "Voeg  toe", "existing": "Bestaande functies", "new": "<PERSON><PERSON><PERSON> maken", "no_features": "<PERSON><PERSON> functies"}, "limits_section": {"title": "<PERSON><PERSON><PERSON>", "columns": {"limit": "Beperk", "feature": "<PERSON><PERSON><PERSON>", "description": "Beschrijving"}, "fields": {"limit": "Beperk", "feature": "<PERSON><PERSON><PERSON>"}, "add_limit": "Voeg  toe", "no_limits": "<PERSON><PERSON> g<PERSON>"}, "prices_section": {"title": "Prijzen", "columns": {"amount": "Bedrag", "currency": "Valuta", "schedule": "<PERSON><PERSON><PERSON>"}, "create": {"amount": "Bedrag", "currency": "Valuta", "recurring": "Terugkerend", "schedule": "<PERSON><PERSON><PERSON>", "including_tax": "Inclusief belasting", "public": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON>"}, "add_price": "Voeg  toe", "existing": "Bestaande pri<PERSON>zen", "new": "<PERSON><PERSON><PERSON> maken", "no_prices": "<PERSON><PERSON>"}, "submit_btn": "Plan maken"}, "view": {"title": "Details abonnementsplan bekijken", "update": "Update", "error": {"not_found": "Geen abonnement gevonden", "unknown": "Er is een onbekende fout opgetreden"}, "main": {"title": "Belangrijkste gegevens", "name": "<PERSON><PERSON>", "code_name": "Code Naam", "per_seat": "Per stoel", "free": "<PERSON><PERSON><PERSON>", "user_count": "Aantal gebruikers", "public": "Publiekelijk beschik<PERSON>ar", "has_trial": "<PERSON><PERSON><PERSON> proef", "trial_length_days": "<PERSON><PERSON><PERSON><PERSON>", "is_trial_standalone": "Is trial standalone?"}, "limits": {"title": "<PERSON><PERSON><PERSON>", "list": {"feature": "<PERSON><PERSON><PERSON>", "limit": "Beperk", "no_limits": "<PERSON><PERSON> g<PERSON>"}}, "features": {"title": "Kenmerken", "list": {"feature": "<PERSON><PERSON><PERSON>", "no_features": "<PERSON><PERSON> functies"}}, "price": {"title": "Prijzen", "list": {"amount": "Bedrag", "currency": "Valuta", "recurring": "<PERSON><PERSON><PERSON><PERSON><PERSON> betaling", "schedule": "Betalingsschema", "including_tax": "Prijs inclusief belasting", "public": "Openbare prijs", "external_reference": "Externe referentie", "usage": "Gebruik"}}}, "update": {"title": "Abonnementsplan bijwerken", "advance": "v<PERSON><PERSON><PERSON>", "submit_btn": "Abonnementsplan bijwerken", "show_advanced": "Geavanceerd", "success_message": "Abonnementsplan succesvol bijgewerkt", "address_title": "<PERSON><PERSON>", "fields": {"name": "<PERSON><PERSON>", "code_name": "Code Naam", "user_count": "Aantal gebruikers", "public": "Openbaar plan", "per_seat": "Per stoel", "free": "<PERSON><PERSON><PERSON>", "prices": "Prijzen", "features": "Kenmerken", "limits": "<PERSON><PERSON><PERSON>", "has_trial": "<PERSON><PERSON><PERSON> proef", "trial_length_days": "<PERSON><PERSON><PERSON><PERSON>", "is_trial_standalone": "Is trial standalone?"}, "help_info": {"name": "De naam voor het plan", "code_name": "De codenaam voor het plan dat moet worden gebruikt met de API.", "user_count": "Het aantal toegestane gebruikers voor dit plan", "public": "Is het plan beschikbaar voor het publiek of een aangepast plan?", "free": "Is dit een gratis plan?", "per_seat": "Wordt het plan aangerekend per stoel?", "has_trial": "Als het plan standaard een proefperiode heeft", "trial_length_days": "Hoe lang het proces moet duren in dagen", "is_trial_standalone": "Als een Proefabonnement standalone is, heeft het geen prijs nodig en wordt het abonnement gepauzeerd aan het einde van het Proefabonnement"}, "features": {"title": "Kenmerken", "add_feature": "<PERSON><PERSON><PERSON>"}, "limits": {"title": "<PERSON><PERSON><PERSON>", "add_limit": "Grenzen toe<PERSON>n"}, "prices": {"title": "Prijzen", "add_price": "<PERSON><PERSON><PERSON><PERSON>"}}, "menu": {"subscription_plans": "Abonnementen", "products": "<PERSON><PERSON>", "features": "Kenmerken"}}, "payment_details": {"add": {"title": "Betalingsgegevens <PERSON>"}, "add_with_token": {"title": "Betalingsdetail toevoegen met token", "field": {"token": "Penning"}, "help_info": {"token": "Het token verstrekt door Stripe."}, "submit": "<PERSON><PERSON><PERSON>  in"}}, "subscription": {"create": {"title": "Nieuw abonnement maken", "subscription_plans": "Abonnementen", "payment_details": "Betalingsgegevens", "no_eligible_prices": "<PERSON><PERSON> zijn geen in aanmerking komende prijzen", "prices": "Prijzen", "success_message": "Abonnement succesvol aangemaakt", "submit_btn": "<PERSON><PERSON>", "trial": "<PERSON><PERSON><PERSON> proef<PERSON>", "trial_length_days": "Aantal dagen", "unknown_error": "Er is een onbekende fout opgetreden tijdens het aanmaken", "seats": "Aantal zitplaatsen", "help_info": {"eligible_prices": "Als een klant al een actief abonnement heeft, moeten nieuwe abonnementen voor dezelfde factureringsperiode en valuta zijn.", "trial": "Als een klant al een actief abonnement heeft, komt hij niet in aanmerking voor nog een gratis proefperiode.", "no_trial": "Dit plan heeft geen gratis proefperiode", "seats": "Het aantal zitplaatsen waarvoor het abonnement moet gelden"}}, "view": {"title": "Bekijk inschrijving", "main": {"title": "Abonnementsgegevens", "status": "Status", "plan": "Plan", "plan_change": "Veranderingsplan", "customer": "<PERSON><PERSON>", "main_external_reference": "Belangrijkste externe referentie", "created_at": "Gemaakt op", "ended_at": "Eindigde op", "valid_until": "<PERSON><PERSON><PERSON> tot", "seat_number": "Zitplaatsnummer", "change_seat": "<PERSON><PERSON><PERSON> ve<PERSON>eren"}, "pricing": {"title": "Prijzen", "price": "<PERSON><PERSON><PERSON><PERSON>", "recurring": "Terugkerend", "schedule": "<PERSON><PERSON><PERSON>", "change": "<PERSON><PERSON>", "no_price": "Er is geen prijs vastgesteld voor een abonnement"}, "payments": {"title": "<PERSON>lingen", "amount": "Bedrag", "created_at": "Gemaakt op", "view": "Bekijk", "no_payments": "<PERSON>r zijn nog geen betalingen"}, "payment_method": {"title": "Betaalmethode", "last_four": "Laatste vier", "expiry_month": "Afloopmaand", "expiry_year": "Vervaldatum Jaar", "brand": "Kaarttype", "invoiced": "Gefactureerd"}, "subscription_events": {"title": "Abonnement Evenementen", "list": {"event": "Evenement", "subscription": "Abonnement", "created_at": "Gemaakt op"}, "no_subscription_events": "<PERSON><PERSON>evenement<PERSON>"}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "payment_method": "Betalingsgegevens bijwerken", "audit_log": "Controlelogboek"}, "modal": {"seats": {"seats": "Zetels", "seats_help": "Het aantal zetels voor het plan", "submit": "Sla"}, "price": {"price": "Nieuwe prijs", "price_help": "De nieuwe prijs die in rekening wordt gebracht bij de volgende factuur", "submit": "Update"}, "plan": {"plan": "Nieuw plan", "plan_help": "Het plan waarin u dit abonnement wilt wijzigen", "price": "Nieuwe prijs", "price_help": "De nieuwe prijs die in rekening wordt gebracht bij de volgende factuur", "submit": "Update", "when": {"title": "<PERSON><PERSON>", "next_cycle": "Gebruik voor volgende factureringscyclus", "instantly": "Direct", "specific_date": "Specifieke datum"}}, "payment_method": {"payment_method": "Betalingsgegevens geb<PERSON>n", "payment_method_help": "Deze gegevens worden gebruikt voor de volgende keer dat we de klant belasten.", "update_button": "Betalingsgegevens bijwerken", "submit": "Update"}, "cancel": {"title": "Abonnement annuleren", "cancel_btn": "Bevestig", "close_btn": "Sluit", "when": {"title": "<PERSON><PERSON>", "end_of_run": "Einde huidige factureringsperiode", "instantly": "Direct", "specific_date": "Specifieke datum"}, "refund_type": {"title": "Type terugbetaling", "none": "<PERSON><PERSON>", "prorate": "Restitutie pro rata op basis van gebruik", "full": "Volledige terugbetaling"}, "cancelled_message": "Succesvol gean<PERSON><PERSON>rd"}}, "usage_estimate": {"title": "Gebruiksschatting Kosten", "usage": "Gebruik", "estimate_cost": "<PERSON><PERSON> schatten", "metric": "<PERSON><PERSON><PERSON>"}, "metadata": {"title": "Metagegevens", "no_metadata": "<PERSON><PERSON> meta<PERSON>s"}}, "list": {"title": "Abonnementen", "email": "<PERSON><PERSON>", "status": "Status", "plan": "Plan", "no_subscriptions": "<PERSON><PERSON> zijn momenteel geen abonnementen", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "view": "Bekijk", "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter"}, "error_message": "Er is een fout opgetreden", "filters": {"status": "Status", "status_choices": {"cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "active": "Actief", "blocked": "Geblokkeerd", "overdue_payment_open": "Achterstallige betaling Open", "trial_active": "Actief onderzoek", "trial_ended": "<PERSON><PERSON>"}}, "loading": "Abonnementen worden geladen..."}, "menu": {"title": "Abonnementen", "subscriptions": "Abonnementen", "mass_change": "<PERSON><PERSON>", "subscriptions_list": "Inschrijvingslijst"}, "mass_change": {"list": {"title": "Abonnementen - Massawijziging", "change_date": "Wijzigingsdatum", "status": "Status", "created_at": "Gemaakt op", "no_mass_change": "<PERSON><PERSON> <PERSON>ijn <PERSON>eel geen wijzigingen in de abonnementenmassa", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "view": "Bekijk", "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter"}, "error_message": "Er is een fout opgetreden"}, "create": {"title": "<PERSON><PERSON><PERSON><PERSON> massale verandering", "criteria": {"title": "Criteria", "plan": "Plan", "price": "<PERSON><PERSON><PERSON><PERSON>", "brand": "Merk", "country": "Land"}, "new": {"title": "<PERSON><PERSON><PERSON> wa<PERSON>en", "plan": "Nieuw plan", "price": "Nieuwe prijs"}, "change_date": {"title": "Wijzigingsdatum", "help_info": "Na de wijzigingsdatum gelden alle verlengingen voor de nieuwe prijs. Het abonnement wordt onmiddellijk gewijzigd."}, "estimate": {"amount": "Dit zal een geschatte {amount} {currency} wijziging in de {schedule} opleveren"}, "submit_button": "Verzendknop"}, "view": {"title": "Massale abonnementswijziging", "criteria": {"title": "Criteria", "plan": "Plan", "price": "<PERSON><PERSON><PERSON><PERSON>", "brand": "Merk", "country": "Land"}, "new_values": {"title": "<PERSON><PERSON><PERSON> wa<PERSON>en", "plan": "Plan", "price": "<PERSON><PERSON><PERSON><PERSON>"}, "change_date": {"title": "Wijzigingsdatum"}, "estimate": {"amount": "Dit zal een geschatte {amount} {currency} wijziging in de {schedule} opleveren"}, "export_button": "Klantenlijst exporteren", "cancel": "<PERSON><PERSON><PERSON>", "uncancel": "<PERSON><PERSON><PERSON>"}}}, "payment": {"list": {"title": "<PERSON>lingen", "no_payments": "<PERSON><PERSON> <PERSON>ijn <PERSON> geen <PERSON>en", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "list": {"amount": "Bedrag", "currency": "Valuta", "customer": "<PERSON><PERSON>", "status": "Status", "created_at": "Gemaakt op"}, "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter"}, "error_message": "Er is een fout opgetreden"}, "view": {"title": "Betalingsgegevens", "main": {"title": "Belangrijkste gegevens", "amount": "Bedrag", "currency": "Valuta", "external_reference": "Externe referentie", "status": "Status", "created_at": "Gemaakt op"}, "customer": {"title": "<PERSON><PERSON>", "email": "E-mail", "more_info": "Meer informatie", "country": "Land", "attach": "<PERSON><PERSON> k<PERSON> bevestigen"}, "refunds": {"title": "Terugbetalingen", "amount": "Bedrag", "reason": "Reden", "created_by": "Gemaakt door", "created_at": "Gemaakt op", "none": "<PERSON><PERSON> restituties gevonden"}, "subscriptions": {"title": "Abonnementen", "plan_name": "<PERSON>", "more_info": "Meer informatie", "none": "<PERSON>ling niet gekoppeld aan abonne<PERSON>en"}, "receipts": {"title": "Ontvangsten", "created_at": "Gemaakt op", "download": "Downloaden", "none": "Betaling heeft geen ontvangstbewijzen"}, "buttons": {"refund": "Terugbetaling", "generate_receipt": "Ontvangstbewijs genereren"}, "modal": {"attach": {"title": "<PERSON><PERSON> k<PERSON> bevestigen", "button": "Bevestig"}, "refund": {"title": "Terugbetaling", "amount": {"title": "Bedrag", "help_info": "<PERSON><PERSON> is de kleine munteenheid. Dus 100 USD is 1,00 USD."}, "reason": {"title": "Reden"}, "submit": "Terugbetaling", "success_message": "Restitutie succesvol aangemaakt", "error_message": "Er ging iets mis"}}}}, "refund": {"list": {"title": "Terugbetalingen", "no_refunds": "<PERSON><PERSON> <PERSON>ijn <PERSON> geen te<PERSON>", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "list": {"amount": "Bedrag", "currency": "Valuta", "customer": "<PERSON><PERSON>", "status": "Status", "created_by": "Gemaakt door", "created_at": "Gemaakt op"}, "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter"}, "error_message": "Er is een fout opgetreden"}, "view": {"title": "Terugbetaling Details", "main": {"title": "Belangrijkste gegevens", "amount": "Bedrag", "currency": "Valuta", "external_reference": "Externe referentie", "status": "Status", "created_at": "Gemaakt op"}, "buttons": {"refund": "Terugbetaling"}, "modal": {"refund": {"title": "Terugbetaling", "amount": {"title": "Bedrag", "help_info": "<PERSON><PERSON> is de kleine munteenheid. Dus 100 USD is 1,00 USD."}, "reason": {"title": "Reden"}, "submit": "Terugbetaling"}}}}, "transactions": {"menu": {"title": "Transacties", "payments": "<PERSON>lingen", "refunds": "Terugbetalingen", "charge_backs": "<PERSON><PERSON>", "invoices": "<PERSON><PERSON><PERSON>", "unpaid_invoices": "Onbetaalde facturen", "checkout": "<PERSON><PERSON>", "countries": "Landen", "tax_types": "Soorten belastingen"}}, "settings": {"menu": {"title": "Instellingen", "user_settings": "Gebruikersinstellingen", "invite": "Uitnodigen", "pdf_templates": "PDF-s<PERSON><PERSON><PERSON>n", "email_templates": "E-mail sjablonen", "tax_settings": "Belastinginstellingen", "brand_settings": "Merkinstellingen", "notification_settings": "Instellingen meldingen", "system_settings": "Systeeminstellingen", "users": "Gebruikers", "stripe": "Streep", "api_keys": "API-sleutels", "exchange_rates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "integrations": "Integraties", "vat_sense": "VatSense", "audit_log": "Controlelogboek"}, "pdf_template": {"list": {"title": "Sjabloon", "name": "<PERSON><PERSON>", "locale": "Lokaal", "brand": "Merk", "create_btn": "<PERSON><PERSON>", "edit_btn": "Bewerk", "no_templates": "<PERSON><PERSON>", "error_message": "Er is een fout opgetreden", "generator": "Generatorinstellingen bijwerken"}, "update": {"title": "Sjabloon bijwerken - {name}", "content": "<PERSON><PERSON><PERSON>", "save": "Sla", "download": "Download Test PDF", "template": "Sjabloon", "help_info": {"template": "De templatingtaal Twig gebruiken", "variable_docs": "Controleer de documentatie om te zien welke variabelen beschikbaar zijn"}}, "generator_settings": {"title": "Instellingen PDF-generator", "generator": "Generator", "tmp_dir": "De tijdelijke directory", "api_key": "Api-sleutel", "bin": "Plaats bak", "submit": "Sla", "help_info": {"generator": "De generator die moet worden gebruikt. Als je het niet zeker weet, gebruik dan mpdf", "tmp_dir": "De tijdelijke map die moet worden gebruikt. Als je het niet zeker weet, gebruik dan /tmp", "api_key": "De API-sleutel die moet worden gebruikt", "bin": "De locatie van wkhtmltopdf"}}, "create": {"title": "Sjabloon maken", "content": "<PERSON><PERSON><PERSON>", "save": "Sla", "download": "Download Test PDF", "template": "Sjabloon", "locale": "Lokaal", "type": "Type", "brand": "Merk", "help_info": {"locale": "De locale waar de PDF-sjabloon voor is", "brand": "Het merk waar de PDF-sjabloon voor is", "type": "Het type PDF waar de sjabloon voor is", "template": "De templatingtaal Twig gebruiken", "variable_docs": "Controleer de documentatie om te zien welke variabelen beschikbaar zijn"}}}, "brand_settings": {"list": {"title": "Merkinstellingen", "name": "<PERSON><PERSON>", "edit_btn": "Bewerk", "no_brands": "<PERSON>r bestaan geen merken", "create_new": "<PERSON><PERSON>", "error_message": "Er is een fout opgetreden"}, "update": {"title": "Merkinstellingen bijwerken - {name}", "fields": {"name": "<PERSON><PERSON>", "email": "E-mailadres", "company_name": "Bedrijfsnaam", "street_line_one": "Straat Lijn 1", "street_line_two": "Straatlijn 2", "city": "Stad", "region": "Regio", "country": "Land", "postcode": "Postcode", "code": "Code", "tax_number": "Belastingnummer", "tax_rate": "Belastingtarief", "digital_services_tax_rate": "Belastingtarief digitale diensten", "support_email": "Ondersteuning per e-mail", "support_phone_number": "Telefoonnummer voor ondersteuning"}, "help_info": {"name": "De na<PERSON> van het merk", "code": "De code die moet worden gebruikt om het merk te identificeren in API-oproepen. Dit kan niet worden bijgewerkt.", "email": "Het e-mailadres dat moet worden gebruikt bij het verzenden van e-mails naar de merkklant", "company_name": "De bedrijfsnaam voor facturering", "street_line_one": "De eerste regel van het factuuradres", "street_line_two": "De tweede regel van het factuuradres", "city": "De stad voor het factuuradres", "region": "De regio/staat voor het factuuradres", "country": "Het factureringsland van de klant - ISO 3166-1 alpha-2 landcode.", "postcode": "De postcode voor het factuuradres", "tax_number": "Het belastingnummer voor het bedrijf/merk", "tax_rate": "Het belastingtarief dat moet worden gebruikt voor je thuisland of wanneer er geen ander belastingtarief kan worden gevonden", "digital_services_tax_rate": "Het belastingtarief dat moet worden gebruikt voor je thuisland of wanneer er geen ander belastingtarief kan worden gevonden voor digitale diensten", "support_email": "Het e-mailadres voor ondersteuning", "support_phone_number": "Het telefoonnummer voor ondersteuning"}, "general": "Algemene instellingen", "notifications": "Meldingen", "address_title": "Factu<PERSON><PERSON><PERSON>", "success_message": "Bijgewerkt", "submit_btn": "Update", "notification": {"subscription_creation": "Abonnement maken", "subscription_cancellation": "Abonnement annuleren", "expiring_card_warning": "Waarschuwing voor verlopen kaart", "expiring_card_warning_day_before": "Waarschuwing voor verlopen kaart - dag ervoor", "invoice_created": "Aangemaak<PERSON> factuur", "invoice_overdue": "Factuur te laat", "quote_created": "Citaat gemaakt", "trial_ending_warning": "Waarschuwing voor einde proef", "before_charge_warning": "Waarschuwing voor opladen", "before_charge_warning_options": {"none": "<PERSON><PERSON>", "all": "Alle", "yearly": "Jaarlijks"}, "payment_failure": "Mislukte betaling"}, "support": "Contactgegevens voor ondersteuning"}, "create": {"title": "Merkinstellingen maken", "fields": {"name": "<PERSON><PERSON>", "email": "E-mailadres", "company_name": "Bedrijfsnaam", "street_line_one": "Straat Lijn 1", "street_line_two": "Straatlijn 2", "city": "Stad", "region": "Regio", "country": "Land", "post_code": "Postcode", "code": "Code", "tax_number": "Belastingnummer", "tax_rate": "Belastingtarief", "digital_services_tax_rate": "Belastingtarief digitale diensten", "support_email": "Ondersteuning per e-mail", "support_phone_number": "Telefoonnummer voor ondersteuning"}, "help_info": {"name": "De na<PERSON> van het merk", "code": "De code die moet worden gebruikt om het merk te identificeren in API-oproepen. Deze kan niet worden bijgewerkt. Meestal kleine alfanumerieke letters met alleen underscores.", "tax_number": "Het belastingnummer voor het merk/bedrijf", "email": "Het e-mailadres dat moet worden gebruikt bij het verzenden van e-mails naar de merkklant", "company_name": "De bedrijfsnaam voor facturering", "street_line_one": "De eerste regel van het factuuradres", "street_line_two": "De tweede regel van het factuuradres", "city": "De stad voor het factuuradres", "region": "De regio/staat voor het factuuradres", "country": "Het factureringsland van de klant - ISO 3166-1 alpha-2 landcode.", "postcode": "De postcode voor het factuuradres", "tax_rate": "Het belastingtarief dat moet worden gebruikt voor je thuisland of wanneer er geen ander belastingtarief kan worden gevonden", "digital_services_tax_rate": "Het belastingtarief dat moet worden gebruikt voor je thuisland of wanneer er geen ander belastingtarief kan worden gevonden voor digitale diensten", "support_email": "Het e-mailadres voor ondersteuning", "support_phone_number": "Het telefoonnummer voor ondersteuning"}, "address_title": "Factu<PERSON><PERSON><PERSON>", "success_message": "Bijgewerkt", "submit_btn": "<PERSON><PERSON>", "support": "Contactgegevens voor ondersteuning"}}, "email_template": {"list": {"title": "E-mail Sjablonen", "email": "E-mail", "country": "Land", "reference": "Referentie", "brand": "Merk", "no_customers": "<PERSON>r bestaan momenteel geen e-mailsjablonen", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "locale": "Lokaal", "view_btn": "Bekijk", "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter"}, "error_message": "Er is een fout opgetreden"}, "create": {"title": "E-mailsjabloon maken", "fields": {"name": "<PERSON><PERSON>", "locale": "Lokaal", "use_emsp_template": "Gebruik EMSP-sjabloon", "subject": "Onderwerp", "template_body": "Sjabloon", "template_id": "Sjabloon-ID", "brand": "Merk"}, "help_info": {"name": "Voor welke e-mail is deze sjab<PERSON><PERSON> bedoeld?", "locale": "<PERSON>oor welke taal is deze sjabloon.", "use_emsp_template": "Als het sjabloonsysteem van de e-mailserviceprovider die je gebruikt moet worden gebruikt. Als je het niet zeker weet, laat het dan uitgevinkt", "subject": "Het bericht dat in het onderwerp moet worden gezet", "template_body": "De TWIG-s<PERSON><PERSON><PERSON><PERSON> die moet worden gebruikt om de html voor de e-mail te genereren.", "template_id": "De sjabloon-ID die u hebt gekregen van uw e-mailserviceprovider waar u de sjabloon hebt gemaakt. Schakel het selectievakje Gebruik emsp-sjabloon uit als u dit niet zeker weet.", "brand": "Het merk waar de e-mailsjabloon voor is.", "variable_docs": "Controleer de documentatie om te zien welke variabelen beschikbaar zijn"}, "submit_btn": "<PERSON><PERSON>", "success_message": "E-mailsjabloon succesvol gemaakt"}, "update": {"title": "E-mailsjabloon bijwerken", "fields": {"name": "<PERSON><PERSON>", "locale": "Lokaal", "use_emsp_template": "Gebruik EMSP-sjabloon", "subject": "Onderwerp", "template_body": "Sjabloon", "template_id": "Sjabloon-ID"}, "help_info": {"name": "Voor welke e-mail is deze sjab<PERSON><PERSON> bedoeld?", "locale": "<PERSON>oor welke taal is deze sjabloon.", "use_emsp_template": "Als het sjabloonsysteem van de e-mailserviceprovider die je gebruikt moet worden gebruikt. Als je het niet zeker weet, laat het dan uitgevinkt", "subject": "Het bericht dat in het onderwerp moet worden gezet", "template_body": "De TWIG-s<PERSON><PERSON><PERSON><PERSON> die moet worden gebruikt om de html voor de e-mail te genereren.", "template_id": "De sjabloon-ID die u hebt gekregen van uw e-mailserviceprovider waar u de sjabloon hebt gemaakt. Schakel het selectievakje Gebruik emsp-sjabloon uit als u dit niet zeker weet.", "variable_docs": "Controleer de documentatie om te zien welke variabelen beschikbaar zijn"}, "submit_btn": "Update", "success_message": "E-mailsjabloon succesvol bijgewerkt", "test_email": "<PERSON><PERSON> verz<PERSON>en"}}, "notification_settings": {"update": {"title": "Instellingen meldingen", "submit_btn": "Update", "success_message": "Instellingen voor meldingen bijgewerkt", "fields": {"send_customer_notifications": "Kennisgevingen naar klanten sturen", "emsp": "Email Service Provider", "emsp_api_key": "Aanbieder van e-maildiensten - API-sleutel", "emsp_api_url": "Aanbieder van e-maildiensten - API URL", "emsp_domain": "E-mailprovider - domein", "default_outgoing_email": "Standaard uitgaande e-mail"}, "help_info": {"emsp": "Welke e-mailp<PERSON>ider je wilt gebruiken. Als je het niet zeker weet, gebruik dan het systeem.", "emsp_api_key": "De API-sleutel geleverd door de e-mailserviceprovider.", "emsp_api_url": "De API URL geleverd door de e-mailserviceprovider.", "emsp_domain": "Het domein van de e-mailserviceprovider.", "send_customer_notifications": "Als u wilt dat BillaBear meldingen stuurt naar klanten zoals het aanmaken van een abonnement, gepau<PERSON>rd, betalingsontvangst, enz.", "default_outgoing_email": "Het standaard e-mailadres dat wordt gebruikt voor het verzenden van meldingen als er geen merkinstellingen bestaan"}}}, "system_settings": {"update": {"title": "Systeeminstellingen", "submit_btn": "Update", "success_message": "Systeeminstellingen bijgewerkt", "fields": {"system_url": "Systeem URL", "timezone": "Tijdzone", "invoice_number_generation": "Factuurnummer genereren", "subsequential_number": "Volgnummer", "default_invoice_due_time": "Standaard vervaldatum factuur", "format": "Form<PERSON><PERSON>", "invoice_generation": "<PERSON><PERSON><PERSON><PERSON> genereren"}, "help_info": {"system_url": "De basis url waarop BillaBear te vinden is.", "timezone": "<PERSON>aard tijdzone voor het systeem", "invoice_number_generation": "Hoe het factuurnummer wordt gegenereerd. Random is een willekeurige tekenreeks en subsequent betekent dat het een getal is dat oploopt", "subsequential_number": "Het laatst gebruikte factuurnummer. Het volgende factuurnummer is <PERSON><PERSON> cijfer hoger", "default_invoice_due_time": "Hoe lang er zit tussen het aanmaken van een factuur en de vervaldatum", "format": "Het formaat dat moet worden gebruikt voor het genereren van factuurnummers. %S is het volgnummer en %R voor 8 willekeurige tekens.", "invoice_generation": "Wanneer nieuwe facturen voor abonnementen moeten worden gegenereerd"}, "invoice_number_generation": {"random": "<PERSON><PERSON><PERSON><PERSON> getal", "subsequential": "Latere", "format": "Form<PERSON><PERSON>"}, "default_invoice_due_time": {"30_days": "30 dagen", "60_days": "60 dagen", "90_days": "90 dagen", "120_days": "120 dagen"}, "invoice_generation_types": {"periodically": "Periodiek", "end_of_month": "Einde maand"}}}, "user": {"list": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "E-mail", "roles": "<PERSON><PERSON>", "reference": "Referentie", "no_customers": "<PERSON><PERSON> <PERSON>ijn <PERSON>eel geen bestaande klanten", "create_new": "<PERSON><PERSON>", "invite": "Nieuwe gebruiker uitnodigen", "next": "Volgende", "prev": "Vorige pagina", "view_btn": "Bekijk", "list": {"email": "E-mail", "role": "<PERSON><PERSON>"}, "invite_title": "<PERSON>digt  uit", "invite_list": {"email": "E-mail", "sent_at": "Verzonden op", "role": "<PERSON><PERSON>", "copy_link": "<PERSON>", "copied_link": "Gekopieerd"}, "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter"}, "error_message": "Er is een fout opgetreden", "audit_log": "Controlelogboek"}, "update": {"title": "Gebruiker bijwerken", "fields": {"email": "E-mail", "roles": "<PERSON><PERSON>"}, "help_info": {"email": "Het e-mailadres dat de gebruiker moet gebruiken om in te loggen en om meldingen naar te ontvangen.", "roles": "Waar moet de gebruiker toegang toe hebben."}, "submit_btn": "Update", "success_message": "De gebruiker succesvol bijgewerkt"}}, "stripe": {"main": {"title": "Stripe importeren", "edit_config": "Configuratie bewerken", "hide_config": "Verberg Config", "start_button": "Knop Import starten", "already_in_progress": "Import is al bezig", "list": {"state": "Staa<PERSON>", "last_id": "Laatste verwerkte id", "created_at": "Gemaakt op", "updated_at": "Update bij", "no_results": "Tot nu toe zijn er nog geen stripe-importen geweest.", "view": "Bekijk"}, "danger_zone": {"title": "Gevarenzone", "use_stripe_billing": "Gebruik Stripe Billing om klanten te factureren.", "disable_billing": "Stripe facturering uitschakelen", "enable_billing": "Stripe facturering inschakelen"}, "disable_billing_modal": {"title": "Stripe facturering uitschakelen", "disable_all_subscriptions": "Door Stripe Billing uit te schakelen, geeft u aan dat u niet langer wilt dat Stripe de betaling aan klanten beheert, maar dat BillaBear dit doet. Dit bespaart u geld.", "warning": "Als je Stripe Billing weer wilt geb<PERSON>iken, moet je iedereen handmatig opnieuw aanmelden.", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Bevestig"}, "webhook": {"title": "Webhook", "url": "Webhook URL", "register_webhook": "Webhook registreren", "deregister_webhook": "Webhook afmelden", "help_info": {"url": "Een https URL die openbaar beschikbaar is voor webhook-oproepen."}}, "stripe_config": {"title": "Stripe API sleutels", "description": "Om Stripe te kunnen geb<PERSON>iken, moet je de API-sleutels configureren.", "stripe_private_key": "<PERSON><PERSON><PERSON><PERSON> sleutel", "help_info": {"stripe_private_key": "De API-sleutel die moet worden gebruikt om backendverzoeken te verifiëren", "stripe_public_key": "De API-sleutel die moet worden gebruikt om aanvragen aan de voorkant te verifiëren."}, "stripe_public_key": "<PERSON><PERSON><PERSON> sleutel", "submit_button": "<PERSON><PERSON><PERSON>  in", "error": "Stripe API Keys kunnen niet worden bevestigd."}}, "view_import": {"title": "Stripe importeren", "progress": "V<PERSON>uitgang", "error": "Fout", "last_updated_at": "Laatst bijgewerkt op", "last_id_processed": "Laatst verwerkte ID", "process": {"started": "<PERSON><PERSON><PERSON> met", "customers": "Klanten", "products": "<PERSON><PERSON>", "prices": "Prijzen", "subscriptions": "Abonnementen", "payments": "<PERSON>lingen", "refunds": "Terugbetalingen", "charge_backs": "<PERSON><PERSON>", "completed": "Voltooid"}}}, "api_keys": {"main": {"title": "API-sleutels", "add_new_button": "Nieuwe API-sleutel aanmaken", "info": {"api_base_url": "API basis URL"}, "list": {"name": "<PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON><PERSON>", "expires_at": "Verloopt op", "created_at": "Gemaakt op", "no_api_keys": "<PERSON><PERSON> <PERSON>ijn <PERSON> geen <PERSON>-sleutels", "disable_button": "Uitschakelen"}, "create": {"title": "<PERSON><PERSON><PERSON> sleutel maken", "name": "<PERSON><PERSON>", "expires": "Verloopt op", "close": "Sluit", "create_button": "<PERSON><PERSON>"}}}, "exchange_rates": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list": {"currency_code": "Valuta", "rate": "<PERSON><PERSON><PERSON><PERSON>", "no_rates": "<PERSON><PERSON> ta<PERSON>n"}}, "tax_settings": {"update": {"title": "Belastinginstellingen", "submit_btn": "<PERSON><PERSON><PERSON>  in", "success_message": "Belastinginstellingen bijgewerkt", "fields": {"tax_customers_with_tax_number": "Klanten met belastingnummer", "eu_business_tax_rules": "<PERSON><PERSON><PERSON><PERSON> met de belastingregels voor bedrijven in de EU", "eu_one_stop_shop_rule": "EU-éénloketregel", "vat_sense_enabled": "VAT Sense ingeschakeld", "vat_sense_api_key": "VAT Sense API-key", "validate_vat_ids": "BTW-ID's valideren"}, "help_info": {"tax_customers_with_tax_number": "Als dit niet is aangevinkt, wordt er geen belasting in rekening gebracht aan klanten die een fiscaal nummer hebben opgegeven", "eu_business_tax_rules": "Als deze optie is ingeschakeld, worden zakelijke klanten die een btw-nummer hebben opgegeven anders behandeld dan normale klanten", "eu_one_stop_shop_rule": "Pas de éénloketregel van de EU toe. Waar EU-landen worden belast ongeacht de drempel.", "vat_sense_enabled": "Als je je belastingregels dagelijks wilt synchroniseren met de VAT Sense-database", "vat_sense_api_key": "Uw VAT Sense API-sleutel. <a href=\"https://vatsense.com/signup?referral=BILLABEAR\" target='_blank'>Krijg hier een gratis exemplaar</a>", "validate_vat_ids": "Als je Tax ids wilt valideren met VAT Sense API."}}, "vatsense": {"title": "VatSense", "fields": {"vat_sense_enabled": "VAT Sense ingeschakeld", "vat_sense_api_key": "VAT Sense API-key", "validate_vat_ids": "BTW-ID's valideren"}, "help_info": {"vat_sense_enabled": "Als je je belastingregels dagelijks wilt synchroniseren met de VAT Sense-database", "vat_sense_api_key": "Uw VAT Sense API-sleutel. <a href=\"https://vatsense.com/signup?referral=BILLABEAR\" target='_blank'>Krijg hier een gratis exemplaar</a>", "validate_vat_ids": "Als je Tax ids wilt valideren met VAT Sense API."}, "description": "Met onze VAT Sense-integratie kun je je belastingregels automatisch laten bijwerken als er wijzigingen zijn in de belastingwetgeving over de hele wereld. Je kunt btw-identificatienummers ook laten valideren door VAT Sense, zodat je ervoor kunt zorgen dat Europese klanten geldige btw-identificatienummers hebben.", "create_account": "Je kunt een gratis account aanmaken.", "create_account_link": "Account aan<PERSON>ken"}}}, "charge_backs": {"list": {"title": "<PERSON><PERSON>", "no_charge_backs": "<PERSON><PERSON> <PERSON>ijn <PERSON> geen te<PERSON>", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "view_payment": "<PERSON>ling bekijken", "list": {"amount": "Bedrag", "currency": "Valuta", "customer": "<PERSON><PERSON>", "status": "Status", "created_at": "Gemaakt op"}, "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter"}}}, "reports": {"dashboard": {"title": "Dashboard", "subscription_count": {"title": "Actieve abonne<PERSON>en"}, "subscription_creation": {"title": "Nieuwe abonnementen"}, "subscription_cancellation": {"title": "Veranderde abonnementen"}, "payment_amount": {"title": "Opbrengsten"}, "refund_amount": {"title": "Terugbetaald bedrag"}, "charge_back_amount": {"title": "Betwist bedrag"}, "estimated_mrr": "Geschatte MRR", "estimated_arr": "Geschatte ARR", "header": {"active_subscriptions": "Actieve abonne<PERSON>en", "active_customers": "<PERSON><PERSON><PERSON> k<PERSON>n", "unpaid_invoices": "Onbetaalde facturen"}, "buttons": {"daily": "Dagelijks", "monthly": "Ma<PERSON><PERSON><PERSON><PERSON>", "yearly": "Jaarlijks", "subscriptions": "Abonnementen", "payments": "<PERSON>lingen"}, "links": {"customers": "Klanten", "subscriptions": "Abonnementen", "invoices": "<PERSON><PERSON><PERSON>"}, "latest_customers": {"title": "Laatste klanten", "list": {"email": "E-mail", "creation_date": "A<PERSON>maak<PERSON><PERSON>"}}, "latest_events": {"title": "Laatste gebeurtenissen", "list": {"event_type": "Type evenement", "customer": "<PERSON><PERSON>", "creation_date": "A<PERSON>maak<PERSON><PERSON>"}}, "latest_payments": {"title": "Laatste betalingen", "list": {"amount": "Bedrag", "customer": "<PERSON><PERSON>", "creation_date": "A<PERSON>maak<PERSON><PERSON>"}}, "payments": {"title": "Totalen betalingen"}, "loading_chart": "Grafiekgegevens laden..."}, "expiring_cards": {"main": {"title": "Vervallen kaarten", "list": {"customer_email": "Klant e-mail", "card_number": "Kaartnummer", "no_expiring_cards": "<PERSON>n kaarten die verlopen", "loading": "laden", "view": "Bekijk"}}}, "menu": {"title": "Rapporten", "dashboard": "Dashboard", "expiring_cards": "Vervallen kaarten", "subscriptions": "Abonnementen", "tax": "Belasting", "churn": "Abonnement opzeggen", "lifetime": "Levenslang"}, "subscriptions": {"overview": {"title": "Abonnementen", "plans": {"title": "Opsplitsing van plannen"}, "schedules": {"title": "Opsplitsing van schema"}}, "churn": {"title": "Abonnement opzeggen", "buttons": {"daily": "Dagelijks", "monthly": "Ma<PERSON><PERSON><PERSON><PERSON>", "yearly": "Jaarlijks"}}}, "vat": {"overview": {"title": "BTW", "list": {"amount": "Bedrag", "currency": "Valuta", "country": "Land"}}}, "financial": {"lifetime": {"title": "Levens<PERSON>ur", "lifespan": "Levens<PERSON>ur", "lifespan_value": "{lifespan} jaar", "lifetime": "Levens<PERSON>ur", "customer_count": "Aantal klanten", "filters": {"country": "Land", "payment_schedule": "Betalingsschema", "subscription_plan": "Abonnement", "brand": "Merk"}, "help_info": {"country": "De levenslange waarde van gebruikers uit dit land bekijken", "payment_schedule": "De levenslange waarde bekijken van gebruikers die betalen volgens een betalingsschema", "subscription_plan": "De levenslange waarde van gebruikers voor een abonnement bekijken", "brand": "De levenslange waarde van gebruikers voor een merk bekijken"}, "schedules": {"week": "Wekelijks", "month": "Ma<PERSON><PERSON><PERSON><PERSON>", "year": "Jaarlijks"}, "chart": {"lifetime_values": "Levens<PERSON>ur", "customer_counts": "Aantal klanten"}, "submit": "Filter"}}, "tax": {"title": "Belastingrapport", "map": {"title": "Belasting geïnd voor"}, "countries": {"title": "Land Drempels", "transacted_amount": "<strong>Transacted:</strong> {currency}{transacted_amount}", "collected_amount": "<strong>Geïnd:</strong> {currency}{collected_amount}", "threshold_status": "<strong><PERSON><PERSON><PERSON>:</strong> {status}", "threshold_reached": "Bereikt", "threshold_not_reached": "<PERSON><PERSON> bere<PERSON>t"}, "transactions": {"title": "Voorbeeld exporteren", "download": "Downloaden"}}}, "credit": {"create": {"title": "<PERSON><PERSON><PERSON> c<PERSON>", "amount": "Bedrag", "currency": "Valuta", "reason": "Reden", "type": "Type", "credit": "<PERSON><PERSON><PERSON>", "debit": "Debet", "help_info": {"type": "Type kredietaanpassing, credit of debet", "amount": "De prijs is de munteenheid van het lagere niveau. Dus 1,00 USD is 100 en 9,99 is 999.", "display_amount": "Deze prijs zou {amount} zijn.", "currency": "De valuta waarin de k<PERSON> moet betalen", "reason": "<PERSON>en optionele reden die later nuttig kan zijn."}, "success_message": "Succesvol krediet gecreëerd", "submit_btn": "<PERSON><PERSON>"}}, "invoices": {"list": {"title": "<PERSON><PERSON><PERSON>", "unpaid_title": "Onbetaalde facturen", "email": "Klant e-mail", "total": "Totaal", "currency": "Valuta", "created_at": "Gemaakt op", "download": "Downloaden", "charge": "Poging tot betaling", "no_invoices": "<PERSON><PERSON> zijn hier geen <PERSON>uren", "next": "Volgende", "prev": "Vorige", "view_btn": "<PERSON><PERSON><PERSON>ur be<PERSON>", "status": "Status", "paid": "<PERSON><PERSON>", "outstanding": "Uitmuntend", "filter": {"title": "Filters", "button": "Filters", "email": "Klant e-mail", "number": "Factuurnummer"}, "mark_as_paid": "<PERSON><PERSON><PERSON> als <PERSON>"}, "menu": {"title": "<PERSON><PERSON><PERSON>", "invoices": "Toon alle", "unpaid_invoices": "Onbetaalde lijst", "create": "<PERSON><PERSON><PERSON><PERSON> maken", "quotes": "Citaten", "settings": "Instellingen", "invoices_list": "Factuurlijst"}, "create": {"title": "<PERSON><PERSON><PERSON><PERSON> maken", "create_invoice": "<PERSON><PERSON><PERSON><PERSON> maken", "success_message": "<PERSON><PERSON><PERSON><PERSON>", "errors": {"no_customer": "Er is een klant nodig", "nothing_to_invoice": "Je moet een abonnement of een eenmalig item toevoegen.", "same_currency_and_schedule": "Dezelfde valuta en hetzelfde schema moeten worden gebruikt voor abonnementen", "currency": "<PERSON>en valuta is vereist", "need_description": "Heb een beschrij<PERSON> nodig", "need_amount": "Behoefte bedrag", "need_tax_type": "Belastingtype nodig"}, "customer": {"create_customer": "<PERSON><PERSON>", "fields": {"customer": "<PERSON><PERSON>", "currency": "Valuta", "due_date": "Vervaldatum"}, "help_info": {"customer": "De <PERSON> voor wie de offerte is", "currency": "De valuta die moet worden gebruikt voor de factuur", "due_date": "De vervaldatum van de factuur. Als er geen vervaldatum is opgegeven, wordt de standaarddatum van het systeem gebruikt."}}, "subscriptions": {"title": "Abonnementen", "add_new": "Abonnement toevoegen", "list": {"subscription_plan": "Abonnement", "price": "<PERSON><PERSON><PERSON><PERSON>", "seat_number": "Zitplaatsnummer"}, "no_subscriptions": "<PERSON><PERSON>", "add_subscription": "Abonnement toevoegen"}, "items": {"title": "Eenmalige posten", "add_item": "Eenmalig item toevoegen", "no_items": "Geen eenmalige items", "list": {"description": "Beschrijving", "amount": "Bedrag", "tax_included": "Belasting inbegrepen", "digital_product": "Digitaal product", "tax_type": "Type belasting"}, "tax_types": {"digital_services": "<PERSON><PERSON> diensten", "digital_goods": "<PERSON><PERSON> goederen", "physical": "Fysieke goederen/diensten"}}}, "view": {"title": "<PERSON><PERSON><PERSON>ur be<PERSON>", "main": {"title": "Factuurgegevens", "created_at": "Gemaakt op", "pay_link": "Betaal link", "due_date": "Vervaldatum"}, "customer": {"title": "<PERSON><PERSON>", "email": "E-mail", "more_info": "Meer informatie", "address": {"company_name": "Bedrijfsnaam", "street_line_one": "Straat Lijn 1", "street_line_two": "Straatlijn 2", "city": "Stad", "region": "Regio", "post_code": "Postcode", "country": "Land"}}, "biller": {"title": "<PERSON><PERSON>", "email": "E-mail", "more_info": "Meer informatie", "address": {"company_name": "Bedrijfsnaam", "street_line_one": "Straat Lijn 1", "street_line_two": "Straatlijn 2", "city": "Stad", "region": "Regio", "post_code": "Postcode", "country": "Land"}}, "lines": {"title": "Items", "description": "Beschrijving", "tax_rate": "Belastingtarief", "amount": "Bedrag", "tax_exempt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "total": {"title": "Totalen", "total": "Totaal", "sub_total": "Subtotaal", "tax_total": "Belasting Totaal"}, "status": {"paid": "Factuur succesvol betaald op {date}", "outstanding": "Factuur moet nog worden betaald."}, "actions": {"charge_card": "Belastingskaart", "mark_as_paid": "<PERSON><PERSON><PERSON> als <PERSON>"}, "payment_failed": {"message": "Kan betaling niet succesvol uitvoeren"}, "payment_succeeded": {"message": "Betaling succesvol uitgevoerd."}, "download": "Factuur downloaden", "invoice_delivery": {"title": "Factuur Leveringen", "method": "<PERSON>e", "detail": "Detail", "status": "Status", "created_at": "Gemaakt op", "no_invoice_deliveries": "<PERSON><PERSON>"}}, "settings": {"title": "Factuurinstellingen", "update": "Update"}, "delivery": {"create": {"title": "Nieuwe factuur aanmaken Aflevering", "fields": {"method": "<PERSON>e", "format": "Form<PERSON><PERSON>", "sftp": {"port": "Haven", "hostname": "Hostnaam", "directory": "Directory", "username": "Gebruikersnaam", "password": "Wachtwoord"}, "webhook": {"method": "<PERSON>e", "url": "URL"}, "email": {"email": "E-mail", "help_info": "Als er geen e-mailadres is opgegeven, wordt standaard het e-mailadres van de klant gebruikt."}}, "save": "Sla"}, "update": {"title": "Factuuraflevering bijwerken", "fields": {"method": "<PERSON>e", "format": "Form<PERSON><PERSON>", "sftp": {"port": "Haven", "hostname": "Hostnaam", "directory": "Directory", "username": "Gebruikersnaam", "password": "Wachtwoord"}, "webhook": {"method": "<PERSON>e", "url": "URL"}, "email": {"email": "E-mail", "help_info": "Als er geen e-mailadres is opgegeven, wordt standaard het e-mailadres van de klant gebruikt."}}, "save": "Sla"}, "format": {"pdf": "PDF", "zugferd_v1": "ZUGFeRD V1", "zugferd_v2": "ZUGFeRD V2 - Technische gegevens"}}, "download": {"loading_message": "Aan het laden...", "format": "<PERSON><PERSON> het formaat om te downloaden", "download": "Downloaden"}}, "home": {"stripe_import": {"text": "U hebt uw streepgegevens niet geïmporteerd.", "link": "<PERSON><PERSON> hier om nu te importeren", "dismiss": "Ontsla"}, "update_available": {"text": "Er is een update be<PERSON><PERSON><PERSON>", "link": "Details vrijgeven", "dismiss": "Ontsla"}, "default_tax": {"text": "Je land wordt standaard niet ondersteund voor belastingtarieven. Je moet een belastingtarief instellen op je standaardmerk!", "link": "<PERSON><PERSON><PERSON> merken"}}, "vouchers": {"create": {"title": "Tegoedbon maken", "submit": "<PERSON><PERSON><PERSON>  in", "success_message": "<PERSON>cc<PERSON><PERSON><PERSON>, voucher aangemaakt", "fields": {"name": "<PERSON><PERSON>", "type": "Type", "type_percentage": "Percentage", "type_fixed_credit": "Vast krediet", "percentage": "Percentage", "entry_type": "Type invoer", "entry_type_manual": "<PERSON><PERSON><PERSON>", "entry_type_automatic": "Automatisch", "amount": "Bedrag - {amount}", "code": "Code", "entry_event": "Evenement", "event_expired_card_added": "Nieuwe <PERSON>t toevoegen tijdens waarschuwing verlopen kaart"}, "help_info": {"name": "<PERSON> van de bon", "type": "Percentage is een percentage van een factuur en vast krediet geeft een vast krediet", "entry_type": "Handmatig betekent dat de gebruiker een code invoert, automatisch betekent dat het wordt geactiveerd door een gebeurtenis", "percentage": "Het percentage korting", "amount": "Het bedrag in {amount} dat de voucher oplevert", "code": "De code die de klant moet invoeren om de voucher te activeren", "entry_event": "De gebeurtenis die moet plaatsvinden om de voucher te activeren"}}, "list": {"title": "Bonnen", "no_vouchers": "<PERSON><PERSON> <PERSON>ijn <PERSON>eel geen vouchers", "create_new": "Nieuwe tegoedbon maken", "list": {"name": "<PERSON><PERSON>", "type": "Type", "entry_type": "Type invoer"}, "view_btn": "Bekijk", "loading": "<PERSON><PERSON><PERSON><PERSON>"}, "view": {"title": "Voucher", "main": {"name": "<PERSON><PERSON>", "type": "Type", "disabled": "Uitgeschakeld", "entry_type": "Type invoer", "percentage": "Percentage", "amount": "Bedrag voor {amount}", "code": "Code", "automatic_event": "Automatisch evenement"}, "disable": "Uitschakelen", "enable": "Inschakelen"}}, "quotes": {"create": {"title": "Offerte maken", "create_quote": "Offerte maken", "success_message": "Citaat gemaakt", "errors": {"no_customer": "Er is een klant nodig", "nothing_to_invoice": "Je moet een abonnement of een eenmalig item toevoegen.", "same_currency_and_schedule": "Dezelfde valuta en hetzelfde schema moeten worden gebruikt voor abonnementen", "currency": "<PERSON>en valuta is vereist", "need_description": "Heb een beschrij<PERSON> nodig", "need_amount": "Behoefte bedrag", "need_tax_type": "Belastingtype nodig"}, "customer": {"create_customer": "<PERSON><PERSON>", "fields": {"customer": "<PERSON><PERSON>", "currency": "Valuta", "expires_at": "Verloopt op"}, "help_info": {"customer": "De <PERSON> voor wie de offerte is", "currency": "De valuta die moet worden gebruikt voor de offerte", "expires_at": "<PERSON><PERSON> de offerte verloopt en niet kan worden betaald"}}, "subscriptions": {"title": "Abonnementen", "add_new": "Abonnement toevoegen", "list": {"subscription_plan": "Abonnement", "price": "<PERSON><PERSON><PERSON><PERSON>", "per_seat": "Per stoel"}, "no_subscriptions": "<PERSON><PERSON>", "add_subscription": "Abonnement toevoegen"}, "items": {"title": "Eenmalige posten", "add_item": "Eenmalig item toevoegen", "no_items": "Geen eenmalige items", "list": {"description": "Beschrijving", "amount": "Bedrag", "tax_included": "Belasting inbegrepen", "digital_product": "Digitaal product", "tax_type": "Type belasting"}, "tax_types": {"digital_services": "<PERSON><PERSON> diensten", "digital_goods": "<PERSON><PERSON> goederen", "physical": "Fysieke goederen/diensten"}}}, "list": {"title": "Citaten", "email": "Klant e-mail", "total": "Totaal", "currency": "Valuta", "created_at": "Gemaakt op", "no_quotes": "<PERSON>r zijn hier geen citaten", "next": "Volgende", "prev": "Vorige", "view_btn": "Bekijk", "filter": {"title": "Filters", "button": "Filters", "email": "Klant e-mail", "number": "Factuurnummer"}}, "view": {"title": "Offerte bekijken", "quote": {"title": "Offerte Info", "created_by": "Gemaakt door", "created_at": "Gemaakt op", "expires_at": "Verloopt op", "pay_link": "Betaal link"}, "status": {"paid": "Offerte succesvol betaald op {date}"}, "customer": {"title": "<PERSON><PERSON>", "email": "E-mail", "more_info": "Meer informatie", "address": {"company_name": "Bedrijfsnaam", "street_line_one": "Straat Lijn 1", "street_line_two": "Straatlijn 2", "city": "Stad", "region": "Regio", "post_code": "Postcode", "country": "Land"}}, "lines": {"title": "Items", "description": "Beschrijving", "schedule": "Betalingsschema", "tax_rate": "Belastingtarief", "amount": "Bedrag", "one_off": "<PERSON><PERSON><PERSON><PERSON>", "tax_exempt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "total": {"title": "Totalen", "total": "Totaal", "sub_total": "Subtotaal", "tax_total": "Belasting Totaal"}}}, "system": {"webhooks": {"webhook_endpoint": {"list": {"title": "Webhook Eindpunten", "add": "Eindpunt toevoegen", "view": "Bekijk", "list": {"name": "<PERSON><PERSON>", "url": "URL", "status": "Status"}, "no_endpoints": "<PERSON><PERSON> zijn momenteel geen webhook-eindpunten"}, "create": {"title": "Webhook eindpunt maken", "fields": {"name": "<PERSON><PERSON>", "url": "URL"}, "help_info": {"name": "De naam voor het webhook eindpunt om het later te helpen identificeren", "url": "De URL waar de payloads naartoe gestuurd moeten worden"}, "create_button": "<PERSON><PERSON>"}, "view": {"title": "Eindpunt bekijken", "main": {"title": "Info", "name": "<PERSON><PERSON>", "url": "URL"}}}, "main": {"title": "Webhooks", "manage_endpoints": "Eindpunten beheren", "list": {"type": "Type", "created_at": "Gemaakt op", "view_btn": "Gebeurtenisgegevens bekijken", "loading": "Webhook gebeurtenissen laden", "no_events": "<PERSON>r zijn geen webhookgebeurtenissen opgetreden"}}, "event": {"view": {"title": "Evenement Informatie", "main": {"title": "Gebeurtenisgegevens", "type": "Type evenement", "payload": "Lading", "created_at": "Gemaakt op"}, "responses": {"title": "Verzoeken voor eindpunten", "list": {"url": "URL", "status_code": "Statuscode", "body": "<PERSON><PERSON><PERSON>", "error": "Fout", "view": "Bekijk", "created_at": "Gemaakt op"}}, "info": {"title": "Info aanvragen", "error_message": "Foutmelding", "status_code": "Statuscode", "body": "Antwoordinstantie", "processing_time": "Verwerkingstijd"}}}}, "integrations": {"list": {"title": "Integraties", "list": {"name": "Integratie"}, "slack": {"name": "<PERSON><PERSON>ck", "button": "Configureer"}}, "slack": {"webhooks": {"list": {"title": "Slack Webhooks", "name": "<PERSON><PERSON>", "webhook": "Webhook", "disable_btn": "Uitschakelen", "enable_btn": "Inschakelen", "no_webhooks": "<PERSON>r zijn nog geen slack webhooks", "next": "Volgende", "prev": "Vorige", "error_message": "Kan geen slack webhooks ophalen", "create_new": "<PERSON><PERSON>"}, "create": {"title": "Maak Slack Webhook", "fields": {"name": "<PERSON><PERSON>", "webhook": "Webhook URL"}, "help_info": {"name": "De naam die wordt gebruikt om deze webhook binnen BillaBear te identificeren", "webhook": "De URL die door Slack is verstrekt om als webhook te worden gebruikt"}, "save_btn": "Sla"}}, "notifications": {"list": {"title": "Slack-melding", "event": "Evenement", "webhook": "Webhook", "disable_btn": "Uitschakelen", "template": "Sjabloon", "enable_btn": "Inschakelen", "no_notifications": "<PERSON>r zijn nog geen slackmeldingen", "next": "Volgende", "prev": "Vorige", "error_message": "Kan geen slackmeldingen ophalen", "create_new": "<PERSON><PERSON>"}, "create": {"title": "Slack-melding maken", "fields": {"webhook": "Webhook", "event": "Evenement", "template": "Sjabloon"}, "help_info": {"event": "De gebeurtenis die de melding moet triggeren", "webhook": "De slack webhook die moet worden gebruikt voor de notificatie", "template": "De sjabloon die moet worden gebruikt bij het verzenden van meldingen. <a href=\"https://docs.billabear.com/user/integration/slack\" target=\"_blank\">Variabelen zijn hier te vinden</a>"}, "save_btn": "Sla"}}, "menu": {"title": "<PERSON><PERSON>ck", "webhooks": "Webhooks", "notification": "Meldingen"}}}, "menu": {"title": "Systeem Gereedschap", "webhooks": "Webhooks", "integrations": "Integraties"}}, "checkout": {"create": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> maken", "create_quote": "<PERSON><PERSON><PERSON><PERSON><PERSON> maken", "success_message": "A<PERSON>rek<PERSON>n gema<PERSON>t", "errors": {"no_customer": "Er is een klant nodig", "nothing_to_invoice": "Je moet een abonnement of een eenmalig item toevoegen.", "same_currency_and_schedule": "Dezelfde valuta en hetzelfde schema moeten worden gebruikt voor abonnementen", "currency": "<PERSON>en valuta is vereist", "need_description": "Heb een beschrij<PERSON> nodig", "need_amount": "Behoefte bedrag", "need_tax_type": "Belastingtype nodig"}, "customer": {"create_customer": "<PERSON><PERSON>", "fields": {"name": "<PERSON><PERSON>", "permanent": "Permanent", "customer": "<PERSON><PERSON>", "currency": "Valuta", "slug": "<PERSON><PERSON>", "expires_at": "Verloopt op", "brand": "Merk"}, "help_info": {"permanent": "Als de checkout permanent is of eenmalig", "name": "De identificatienaam voor de kassa", "customer": "<PERSON> voor wie de kassa is", "currency": "De valuta die moet worden gebruikt bij het afrekenen", "expires_at": "<PERSON><PERSON> de offerte verloopt en niet kan worden betaald", "slug": "De slug voor de URL. Als je wilt dat de kassa een mooie url heeft, gebruik je deze.", "brand": "Het merk waar de kassa bij hoort"}}, "subscriptions": {"title": "Abonnementen", "add_new": "Abonnement toevoegen", "list": {"subscription_plan": "Abonnement", "price": "<PERSON><PERSON><PERSON><PERSON>", "per_seat": "Per stoel"}, "no_subscriptions": "<PERSON><PERSON>", "add_subscription": "Abonnement toevoegen"}, "items": {"title": "Eenmalige posten", "add_item": "Eenmalig item toevoegen", "no_items": "Geen eenmalige items", "list": {"description": "Beschrijving", "amount": "Bedrag", "tax_included": "Belasting inbegrepen", "digital_product": "Digitaal product", "tax_type": "Type belasting"}, "tax_types": {"digital_services": "<PERSON><PERSON> diensten", "digital_goods": "<PERSON><PERSON> goederen", "physical": "Fysieke goederen/diensten"}}}, "view": {"title": "<PERSON><PERSON>", "checkout": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_by": "Gemaakt door", "created_at": "Gemaakt op", "expires_at": "Verloopt op", "pay_link": "Betaal link", "name": "<PERSON><PERSON>"}, "status": {"paid": "Offerte succesvol betaald op {date}"}, "customer": {"title": "<PERSON><PERSON>", "email": "E-mail", "more_info": "Meer informatie", "address": {"company_name": "Bedrijfsnaam", "street_line_one": "Straat Lijn 1", "street_line_two": "Straatlijn 2", "city": "Stad", "region": "Regio", "post_code": "Postcode", "country": "Land"}}, "lines": {"title": "Items", "description": "Beschrijving", "schedule": "Betalingsschema", "tax_rate": "Belastingtarief", "amount": "Bedrag", "one_off": "<PERSON><PERSON><PERSON><PERSON>", "tax_exempt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "total": {"title": "Totalen", "total": "Totaal", "sub_total": "Subtotaal", "tax_total": "Belasting Totaal"}}, "list": {"title": "Checkouts", "email": "E-mail", "country": "Land", "reference": "Referentie", "no_checkouts": "<PERSON><PERSON> <PERSON>ijn <PERSON>eel geen checkouts", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "view_btn": "Bekijk", "list": {"name": "<PERSON><PERSON>", "created_at": "Gemaakt op", "view": "Bekijk"}, "filter": {"title": "Filters", "name": "<PERSON><PERSON>", "button": "Filters", "search": "Filter"}, "loading": "Resultaten laden", "error_message": "Er is een fout opgetreden"}}, "layout": {"topbar": {"menu": {"settings": "Instellingen", "signout": "Afmelden"}}}, "workflows": {"cancellation_request": {"list": {"title": "Annuleringsverzoeken", "email": "<PERSON><PERSON>", "status": "Status", "plan": "Plan", "no_cancellation_requests": "<PERSON><PERSON> zijn momenteel geen annuleringsverzoeken", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "view": "Bekijk", "edit_button": "Bewerk", "bulk_button": "Opnieuw verwerken in bulk", "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter", "has_error": "Heeft fout"}, "error_message": "Er is een fout opgetreden"}, "view": {"title": "Details annuleringsverzoek", "subscription": {"title": "Inschrijving Details", "name": "<PERSON>", "customer": "<PERSON><PERSON>", "original_cancellation_date": "Oorspronkelijke annuleringsdatum"}, "details": {"title": "Annuleringsgegevens", "state": "Staa<PERSON>", "when": "<PERSON><PERSON>", "refund_type": "Type terugbetaling", "specific_date": "Annuleringsdatum"}, "error": {"title": "Fout"}, "buttons": {"process": "Opnieuw proberen"}}, "edit": {"title": "Annuleringsverzoeken bewerken", "add_place": "Plaats toevoegen", "add_place_modal": {"title": "Plaats toevoegen", "from_place": "<PERSON>", "to_place": "<PERSON>ar plaats", "name": "<PERSON><PERSON>", "event_handler": "Gebeurtenis handler", "handler_options": "Handler-opties", "add": "Voeg  toe", "required": "Is vereist"}, "edit_place_modal": {"title": "Plaats bewerken", "delete_button": "Plaats verwijderen", "enable_button": "Inschakelen", "disable_button": "Uitschakelen"}}}, "menu": {"title": "Hulpmiddelen voor workflows", "cancellation_requests": "Annuleringsverzoeken", "subscription_creation": "Abonnement maken", "payment_creation": "<PERSON><PERSON> a<PERSON>", "refund_created_process": "Proces voor terugbetaling", "payment_failure_process": "Proces voor mislukte betalingen", "charge_back_creation": "Charge Back Creation"}, "subscription_creation": {"list": {"title": "Abonnement maken", "email": "<PERSON><PERSON>", "status": "Status", "plan": "Plan", "no_cancellation_requests": "<PERSON><PERSON> zijn momenteel geen abonnementen", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "view": "Bekijk", "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter", "has_error": "Heeft fout"}, "error_message": "Er is een fout opgetreden", "edit_button": "Bewerk", "bulk_button": "Opnieuw verwerken in bulk"}, "view": {"title": "Details abonnement aanmaken", "subscription": {"title": "Inschrijving Details", "name": "<PERSON>", "customer": "<PERSON><PERSON>", "view": "Bekijk inschrijving"}, "details": {"title": "Creatie details", "state": "Staa<PERSON>"}, "error": {"title": "Fout"}, "buttons": {"process": "Opnieuw proberen"}}, "edit": {"title": "Abonnement aanmaken bewerken", "add_place": "Plaats toevoegen", "add_place_modal": {"title": "Plaats toevoegen", "from_place": "<PERSON>", "to_place": "<PERSON>ar plaats", "name": "<PERSON><PERSON>", "event_handler": "Gebeurtenis handler", "handler_options": "Handler-opties", "add": "Voeg  toe", "required": "Is vereist"}}}, "payment_creation": {"list": {"title": "<PERSON><PERSON> a<PERSON>", "email": "<PERSON><PERSON>", "status": "Status", "plan": "Plan", "no_results": "<PERSON><PERSON> zijn <PERSON>eel geen resultaten", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "view": "Bekijk", "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter", "has_error": "Heeft fout"}, "bulk_button": "Opnieuw verwerken in bulk", "error_message": "Er is een fout opgetreden", "edit_button": "Bewerk"}, "view": {"title": "Details aanmaken betaling", "payment": {"title": "Betalingsgegevens", "name": "<PERSON>", "customer": "<PERSON><PERSON>", "view": "<PERSON>ling bekijken"}, "details": {"title": "Creatie details", "state": "Staa<PERSON>"}, "error": {"title": "Fout"}, "buttons": {"process": "Opnieuw proberen"}}, "edit": {"title": "Betaling aanmaken bewerken", "add_place": "Plaats toevoegen", "add_place_modal": {"title": "Plaats toevoegen", "from_place": "<PERSON>", "to_place": "<PERSON>ar plaats", "name": "<PERSON><PERSON>", "event_handler": "Gebeurtenis handler", "handler_options": "Handler-opties", "add": "Voeg  toe", "required": "Is vereist"}}}, "refund_created_process": {"list": {"title": "Restitutie <PERSON>", "email": "<PERSON><PERSON>", "status": "Status", "plan": "Plan", "no_results": "<PERSON><PERSON> zijn <PERSON>eel geen resultaten", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "view": "Bekijk", "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter", "has_error": "Heeft fout"}, "error_message": "Er is een fout opgetreden", "edit_button": "Bewerk", "bulk_button": "Opnieuw verwerken in bulk"}, "view": {"title": "Procesdetails voor terugbetaling", "refund": {"title": "Terugbetaling Details", "name": "<PERSON>", "customer": "<PERSON><PERSON>", "view": "Bekijk terugbetaling"}, "details": {"title": "Creatie details", "state": "Staa<PERSON>"}, "error": {"title": "Fout"}, "buttons": {"process": "Opnieuw proberen"}}, "edit": {"title": "Proces voor aangemaakte restitutie bewerken", "add_place": "Plaats toevoegen", "add_place_modal": {"title": "Plaats toevoegen", "from_place": "<PERSON>", "to_place": "<PERSON>ar plaats", "name": "<PERSON><PERSON>", "event_handler": "Gebeurtenis handler", "handler_options": "Handler-opties", "add": "Voeg  toe", "required": "Is vereist"}, "edit_place_modal": {"title": "Plaats bewerken", "disable_button": "Uitschakelen", "enable_button": "Inschakelen"}}}, "payment_failure_process": {"list": {"title": "Mislukte betaling", "email": "<PERSON><PERSON>", "status": "Status", "plan": "Plan", "no_results": "<PERSON><PERSON> zijn <PERSON>eel geen resultaten", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "view": "Bekijk", "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter", "has_error": "Heeft fout"}, "error_message": "Er is een fout opgetreden"}, "view": {"title": "Details betalingsfoutenproces", "payment": {"title": "Details betaalpoging", "amount": "Bedrag", "customer": "<PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON>ur be<PERSON>"}, "details": {"title": "Creatie details", "state": "Staa<PERSON>"}, "error": {"title": "Fout"}, "buttons": {"process": "Opnieuw proberen"}}}, "charge_back_creation": {"list": {"title": "Charge Back Creation", "email": "<PERSON><PERSON>", "status": "Status", "plan": "Plan", "no_results": "<PERSON><PERSON> zijn <PERSON>eel geen resultaten", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "view": "Bekijk", "filter": {"title": "Filters", "email": "E-mail", "reference": "Referentie", "external_reference": "Externe referentie", "button": "Filters", "search": "Filter", "has_error": "Heeft fout"}, "error_message": "Er is een fout opgetreden", "edit_button": "Bewerk", "bulk_button": "Opnieuw verwerken in bulk"}, "view": {"title": "Charge Back <PERSON><PERSON><PERSON>", "payment": {"title": "Betalingsgegevens", "name": "<PERSON>", "customer": "<PERSON><PERSON>", "view": "<PERSON>ling bekijken"}, "details": {"title": "Creatie details", "state": "Staa<PERSON>"}, "error": {"title": "Fout"}, "buttons": {"process": "Opnieuw proberen"}}, "edit": {"title": "Chargeback aanmaken bewerken", "add_place": "Plaats toevoegen", "add_place_modal": {"title": "Plaats toevoegen", "from_place": "<PERSON>", "to_place": "<PERSON>ar plaats", "name": "<PERSON><PERSON>", "event_handler": "Gebeurtenis handler", "handler_options": "Handler-opties", "add": "Voeg  toe", "required": "Is vereist"}}}}, "country": {"list": {"title": "Landen", "no_countries": "<PERSON><PERSON> <PERSON>ijn <PERSON>eel geen landen", "create_new": "<PERSON><PERSON>", "next": "Volgende", "prev": "Vorige pagina", "list": {"name": "<PERSON><PERSON>", "iso_code": "Code", "tax_threshold": "Belastingdrempel", "collecting": "Belasting innen"}, "view": "Bekijk", "filter": {"title": "Filters", "name": "<PERSON><PERSON>", "code": "Code", "button": "Filters", "search": "Filter", "collecting": "Belasting innen"}, "error_message": "Er is een fout opgetreden"}, "create": {"title": "Nieuw land maken", "country": {"fields": {"name": "<PERSON><PERSON>", "iso_code": "Landcode", "currency": "Valuta", "threshold": "<PERSON><PERSON><PERSON>", "in_eu": "In de EU?", "tax_year": "<PERSON><PERSON> van het belasting<PERSON><PERSON>", "collecting": "Belasting innen", "tax_number": "Belastingnummer"}, "help_info": {"name": "De na<PERSON> van het land", "iso_code": "De ISO-code voor het land", "currency": "De rapporteringsvaluta voor het land", "threshold": "De belastingdrempel voor het land", "in_eu": "Maakt het land deel uit van de EU", "tax_year": "De datum voor het begin van het belastingjaar voor het land", "collecting": "Als er altijd belasting geïnd moet worden voor dit land", "tax_number": "Je fiscaal nummer voor dit land."}}, "create_button": "<PERSON><PERSON>"}, "view": {"title": "Land bekijken", "fields": {"name": "<PERSON><PERSON>", "iso_code": "Landcode", "threshold": "<PERSON><PERSON><PERSON>", "currency": "Valuta", "in_eu": "In Eu", "start_of_tax_year": "<PERSON><PERSON> van het belasting<PERSON><PERSON>", "enabled": "Ingeschakeld", "collecting": "Belasting innen", "tax_number": "Belastingnummer", "transaction_threshold": "Dr<PERSON><PERSON> transactie", "threshold_type": "Type drempel"}, "edit_button": "Bewerk", "tax_rule": {"title": "Belastingregels", "rate": "Belastingtarief", "type": "Type belasting", "default": "Is standaard", "start_date": "Startdatum", "end_date": "Einddatum", "no_tax_rules": "<PERSON><PERSON> belast<PERSON>", "add": "Belastingregel toevoegen", "edit": "Bewerk"}, "add_tax_rule": {"tax_rate": "Belastingtarief", "tax_type": "Type belasting", "valid_from": "<PERSON><PERSON><PERSON>", "valid_until": "<PERSON><PERSON><PERSON> tot", "title": "Belastingregel toevoegen", "default": "Standaard belastingregel", "save": "Sla", "select_tax_type": "Selecteer belastingtype"}, "edit_tax_rule": {"tax_rate": "Belastingtarief", "tax_type": "Type belasting", "valid_from": "<PERSON><PERSON><PERSON>", "valid_until": "<PERSON><PERSON><PERSON> tot", "title": "Belastingregel bewerken", "default": "Standaard belastingregel", "save": "Update", "select_tax_type": "Selecteer belastingtype"}, "states": {"title": "Staten", "add": "Nieuwe staat toevoegen", "name": "<PERSON><PERSON>", "code": "Code", "collecting": "Belasting innen?", "threshold": "<PERSON><PERSON><PERSON>", "view": "Bekijk", "no_states": "<PERSON><PERSON> zijn geen <PERSON>n"}}, "edit": {"title": "Land bewerken", "country": {"fields": {"name": "<PERSON><PERSON>", "iso_code": "Landcode", "currency": "Valuta", "threshold": "<PERSON><PERSON><PERSON>", "in_eu": "In de EU?", "tax_year": "<PERSON><PERSON> van het belasting<PERSON><PERSON>", "enabled": "Ingeschakeld", "collecting": "Belasting innen", "tax_number": "Belastingnummer", "transaction_threshold": "Dr<PERSON><PERSON> transactie", "threshold_type": "Type drempel", "threshold_types": {"rolling": "Jaarlijks", "calendar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rolling_quarterly": "Rollen per kwart", "rolling_accounting": "Voortschrijdend per boekjaar"}}, "help_info": {"name": "De na<PERSON> van het land", "iso_code": "De ISO-code voor het land", "currency": "De rapporteringsvaluta voor het land", "threshold": "De belastingdrempel voor het land", "in_eu": "Maakt het land deel uit van de EU", "tax_year": "De datum voor het begin van het belastingjaar voor het land", "enabled": "Als het land is ingeschakeld voor aanmeldingen van klanten", "collecting": "Als er altijd belasting geïnd moet worden voor dit land", "tax_number": "Je fiscaal nummer voor dit land.", "transaction_threshold": "Wat de transactiedrempel voor de staat is", "threshold_type": "Hoe de periode voor de drempelberekening wordt bepaald"}}, "update_button": "Update"}}, "tax_type": {"list": {"title": "Soorten belastingen", "create_new": "<PERSON><PERSON><PERSON> maken", "error_message": "Er is een fout opgetreden", "list": {"name": "<PERSON><PERSON>", "make_default": "<PERSON>aard maken", "is_default": "Is standaard", "default": "Standaard", "update": "Update"}, "no_tax_types": "<PERSON><PERSON> zijn momenteel geen belastingsoorten"}, "create": {"title": "Belastingtype aanmaken", "tax_type": {"fields": {"name": "<PERSON><PERSON>", "vat_sense_type": "BTW Zintuigtype"}, "help_info": {"name": "De naam voor de belasting", "vat_sense_type": "Het belastingtype in het systeem van VAT Sense"}}, "create_button": "<PERSON><PERSON>"}, "update": {"title": "Belastingtype bijwerken", "tax_type": {"fields": {"name": "<PERSON><PERSON>", "vat_sense_type": "BTW Zintuigtype"}, "help_info": {"name": "De naam voor de belasting", "vat_sense_type": "Het belastingtype in het systeem van VAT Sense"}}, "update_button": "Update"}}, "finance": {"integration": {"title": "Integraties", "fields": {"integration": "Integratie", "api_key": "API-sleutel", "enabled": "Ingeschakeld"}, "buttons": {"connect": "Verbinding maken via OAuth", "disconnect": "Ontkoppelen", "save": "Sla"}, "settings": {"title": "Instellingen"}, "xero": {"account_id": "Rekeningcode voor betalingen"}, "errors": {"required": "Dit veld is verplicht", "invalid": "Dit veld is ongeldig", "complete_error": "Er is een fout opgetreden tijdens het opslaan van deze instellingen. Probeer het opnieuw."}}, "menu": {"integration": "Integratie"}}, "tax": [], "state": {"view": {"title": "Bekijk staat", "edit": "Bewerk", "fields": {"name": "<PERSON><PERSON>", "code": "Code", "threshold": "<PERSON><PERSON><PERSON>", "collecting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transaction_threshold": "Dr<PERSON><PERSON> transactie", "threshold_type": "Type drempel"}, "tax_rule": {"title": "Belastingregels", "rate": "Belastingtarief", "type": "Type belasting", "default": "Is standaard", "start_date": "Startdatum", "end_date": "Einddatum", "no_tax_rules": "<PERSON><PERSON> belast<PERSON>", "add": "Belastingregel toevoegen", "edit": "Bewerk"}, "add_tax_rule": {"tax_rate": "Belastingtarief", "tax_type": "Type belasting", "valid_from": "<PERSON><PERSON><PERSON>", "valid_until": "<PERSON><PERSON><PERSON> tot", "title": "Belastingregel toevoegen", "default": "Standaard belastingregel", "save": "Sla", "select_tax_type": "Selecteer belastingtype"}, "edit_tax_rule": {"tax_rate": "Belastingtarief", "tax_type": "Type belasting", "valid_from": "<PERSON><PERSON><PERSON>", "valid_until": "<PERSON><PERSON><PERSON> tot", "title": "Belastingregel bewerken", "default": "Standaard belastingregel", "save": "Update", "select_tax_type": "Selecteer belastingtype"}}, "create": {"title": "Nieuwe staat creëren", "state": {"fields": {"name": "<PERSON><PERSON>", "code": "Code", "collecting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "threshold": "<PERSON><PERSON><PERSON>"}, "help_info": {"name": "<PERSON> van de <PERSON>", "code": "De code die vaak wordt gebruikt als steno voor de staat", "collecting": "Als we altijd belasting innen voor de staat", "threshold": "Wat de economische drempel voor de staat is"}}, "create_button": "<PERSON><PERSON>"}, "edit": {"title": "Bewerk staat", "state": {"fields": {"name": "<PERSON><PERSON>", "code": "Code", "collecting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "threshold": "<PERSON><PERSON><PERSON>", "transaction_threshold": "Dr<PERSON><PERSON> transactie", "threshold_type": "Type drempel", "threshold_types": {"rolling": "Jaarlijks", "calendar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rolling_quarterly": "Rollen per kwart", "rolling_accounting": "Voortschrijdend per boekjaar"}}, "help_info": {"name": "<PERSON> van de <PERSON>", "code": "De code die vaak wordt gebruikt als steno voor de staat", "collecting": "Als we altijd belasting innen voor de staat", "threshold": "Wat de economische drempel voor de staat is", "transaction_threshold": "Wat de transactiedrempel voor de staat is", "threshold_type": "Hoe de periode voor de drempelberekening wordt bepaald"}}, "update_button": "Update"}}, "onboarding": {"main": {"bar": {"message": "Stripe moet worden geconfigureerd voordat u BillaBear kunt gebruiken"}, "dialog": {"title": "Inwerken", "has_stripe_key": {"text": "Voer geldige Stripe API-sleutels in", "button": "<PERSON><PERSON> in<PERSON><PERSON>n"}, "has_stripe_imports": {"text": "Gegevens import<PERSON><PERSON>", "button": "Importeren", "dismiss": "Ontsla"}, "has_product": {"text": "Eerste product maken", "button": "Product maken"}, "has_subscription_plan": {"text": "Eerste abonnementsplan maken", "button": "<PERSON><PERSON>"}, "has_customer": {"text": "Eerste klant maken", "button": "<PERSON><PERSON>"}, "has_subscription": {"text": "Eerste abonnement aanmaken", "button": "<PERSON><PERSON>"}}, "error": "Er ging iets mis!"}}, "default_error_message": "Er ging iets mis!", "metric": {"list": {"title": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "no_metrics": "Er zijn nog geen statistieken!", "filter": {"name": "<PERSON><PERSON>"}, "view_btn": "Bekijk"}, "create": {"title": "Metriek aanmaken", "fields": {"name": "<PERSON><PERSON>", "code": "Code", "type": "Type", "aggregation_method": "Aggregatiemethode", "aggregation_property": "Samenvoegingseigenschap", "ingestion": "<PERSON><PERSON>", "filters": "Filters"}, "help_info": {"name": "<PERSON>", "code": "De code die moet worden gebruikt in api-aanroepen. Alleen kleine letters, cijfers en underscore.", "type": "Als de teller voor de klant opnieuw moet worden ingesteld aan het einde van een abonnementsperiode", "aggregation_method": "Hoe de gebeurtenissen die naar BillaBear worden verzonden, moeten worden samengevo<PERSON>d.", "aggregation_property": "Welke eigenschap in de gebeurtenisgegevens moet worden gebruikt voor aggregatie.", "ingestion": "Hoe vaak gebeurtenissen moeten worden verwerkt", "filters": "De filters die moeten worden toegepast op de payload van de gebeurtenis om te worden uitgesloten in aggregatie"}, "aggregation_methods": {"count": "Graaf", "sum": "Som", "latest": "Nieuwste", "unique_count": "<PERSON>iek<PERSON> telling", "max": "Max"}, "ingestion": {"real_time": "<PERSON>chte tijd", "hourly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "daily": "Dagelijks"}, "filter": {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "type": "Type", "no_filters": "Geen filters"}, "filter_type": {"inclusive": "Inclusief", "exclusive": "Exclusief"}, "create_button": "<PERSON><PERSON>"}, "view": {"title": "<PERSON><PERSON><PERSON> metrisch", "main": {"name": "<PERSON><PERSON>", "code": "Code", "type": "Type", "aggregation_method": "Aggregatiemethode", "aggregation_property": "Samenvoegingseigenschap", "event_ingestion": "<PERSON><PERSON>"}, "filters": {"title": "Filters", "name": "<PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "type": "Type", "inclusive": "Inclusief", "exclusive": "Exclusief"}, "update": "Update"}, "update": {"title": "Metriek bijwerken", "update_button": "Sla"}}, "usage_limit": {"create": {"title": "Gebruikslimiet aanmaken", "fields": {"amount": "Bedrag", "action": "<PERSON><PERSON>"}, "help_info": {"amount": "Het bedrag waartoe u de klant wilt beperken voordat er actie wordt ondernomen", "action": "De actie die moet gebeuren als de limiet wordt overschreden."}, "actions": {"warn": "Waar<PERSON>u<PERSON>", "disable": "uitschakelen"}, "submit": "<PERSON><PERSON>"}}, "customer_support": {"integration": {"title": "Integraties voor klantenondersteuning", "fields": {"integration": "Integratie", "api_key": "API-sleutel", "enabled": "Ingeschakeld"}, "buttons": {"connect": "Verbinding maken via OAuth", "disconnect": "Ontkoppelen", "save": "Sla"}, "settings": {"title": "Instellingen"}, "errors": {"required": "Dit veld is verplicht", "invalid": "Dit veld is ongeldig", "complete_error": "Er is een fout opgetreden tijdens het opslaan van deze instellingen. Probeer het opnieuw."}, "zendesk": {"token": "Penning", "subdomain": "Subdomein", "username": "Gebruikersnaam"}, "freshdesk": {"subdomain": "Subdomein", "api_key": "API-sleutel"}}}, "integrations": {"newsletter": {"title": "Integratie nieuwsbrief", "fields": {"marketing_list": "Marketinglijst", "announcement_list": "Bekendmakingslijst"}, "no_lists": "<PERSON><PERSON> lijsten be<PERSON>. Voer eerst de verbindingsgegevens in.", "errors": {"list_required": "Je kunt pas inschakelen als je een lijst hebt geselecteerd. <PERSON><PERSON><PERSON> verbindingsgegevens in en sla op en kies vervolgens een lijst."}, "mailchimp": {"fields": {"server_prefix": "Server voorvoegsel"}}}, "menu": {"main": "Integraties", "accounting": "<PERSON>ek<PERSON><PERSON>", "customer_support": "Klantenservice", "newsletter": "Nieuwsbrief", "notifications": "Meldingen", "crm": "CRM"}, "general": {"fields": {"integration": "Integratie", "api_key": "API-sleutel", "enabled": "Ingeschakeld"}, "buttons": {"connect": "Verbinding maken via OAuth", "disconnect": "Ontkoppelen", "save": "Sla"}, "settings": {"title": "Instellingen"}, "errors": {"required": "Dit veld is verplicht", "invalid": "Dit veld is ongeldig", "complete_error": "Er is een fout opgetreden tijdens het opslaan van deze instellingen. Probeer het opnieuw."}}, "crm": {"title": "CRM-integraties", "fields": {"integration": "Integratie"}, "buttons": {"connect": "Verbinden via Oauth", "disconnect": "Ontkoppelen", "save": "Sla"}}}, "compliance": {"audit": {"all": {"title": "Controlelogboek", "log": "Log", "date": "Datum", "billing_admin": "Ingelogde factureringsadmin", "no_billing_admin": "Dit is niet gedaan door een factureringsadministrateur", "display_name": "<PERSON><PERSON> we<PERSON>", "context": "Logboek Context", "no_logs": "Geen logboeken gevonden"}, "customer": {"title": "Auditlogboek klant - {naam}"}, "billing_admin": {"title": "Auditlogboek factureringsbeheer - {naam}"}}}}, "install": {"title": "Installeer", "submit_button": "Installeer", "user": {"title": "Eerste Admin-gebruiker", "email": "E-mail", "password": "Wachtwoord"}, "settings": {"title": "Systeeminstellingen", "default_brand": "Standaard Merknaam", "from_email": "Standaard e-mailadres van", "timezone": "Tijdzone", "webhook_url": "Basis URL", "currency": "Valuta", "country": "Land"}, "complete_text": "<PERSON><PERSON><PERSON><PERSON> is geïnstalleerd! Je kunt nu inloggen met de gegevens die je hebt opgegeven.", "login_link": "<PERSON><PERSON> hier om in te loggen", "unknown_error": "Onbekende fout.", "stripe": {"no_api_key": "Je moet een Stripe API-sleutel opgeven in de ENV-variabele STRIPE_PRIVATE_API_KEY.", "doc_link": "<PERSON><PERSON> informatie over het instellen van Billa<PERSON>ear.", "invalid_api_key": "De Stripe API-sleutel is ongeldig", "support_link": "Je kunt hier om hulp vragen."}}}