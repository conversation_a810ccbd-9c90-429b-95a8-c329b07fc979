export const WEBHOOK_EVENT_VIEW_TRANSLATION = {
    title: "Event Information",
    main: {
        title: "Event Data",
        type: "Event Type",
        payload: "Payload",
        created_at: "Created At"
    },
    responses: {
        title: "Endpoint Requests",
        list: {
            url: "URL",
            status_code: "Status Code",
            body: "Body",
            error: "Error",
            view: "View",
            created_at: "Created At"
        }
    },
    info: {
        title: "View Request Info",
        error_message: "Error Message",
        status_code: "Status Code",
        body: "Response Body",
        processing_time: "Processing Time",
    }
}