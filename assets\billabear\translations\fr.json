{"global": {"loading": "Chargement", "country": {"AU": "Australie", "BE": "Belgique", "CA": "Canada", "HR": "Croatie", "CZ": "République tchèque", "DK": "Danemark", "EE": "Estonie", "FI": "<PERSON><PERSON>", "FR": "France", "DE": "Allemagne", "GR": "<PERSON><PERSON><PERSON><PERSON>", "HU": "Hong<PERSON>", "IS": "Islande", "LV": "<PERSON><PERSON><PERSON>", "LI": "Liechtenstein", "LT": "Lituanie", "LU": "Luxembourg", "GB": "Royaume-Uni", "US": "États-Unis", "NL": "Pays-Bas", "RO": "Roumanie", "SK": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SI": "Slovénie", "ES": "<PERSON><PERSON><PERSON><PERSON>", "SE": "<PERSON><PERSON><PERSON>", "AF": "Afghanistan", "AL": "Albanie", "DZ": "Algérie", "AD": "<PERSON><PERSON><PERSON>", "AO": "Angola", "AI": "<PERSON><PERSON><PERSON>", "AQ": "Antarctique", "AG": "Antigua et Barbuda", "AR": "Argentine", "AM": "<PERSON><PERSON><PERSON>", "AW": "Aruba", "AT": "<PERSON><PERSON><PERSON>", "AZ": "Azerbaïdjan", "BS": "Bahamas", "BH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BD": "Bangladesh", "BB": "Barbade", "BY": "Bélarus", "BZ": "Belize", "BJ": "<PERSON><PERSON><PERSON>", "BM": "<PERSON><PERSON><PERSON>", "BT": "<PERSON><PERSON><PERSON>", "BO": "<PERSON><PERSON><PERSON>", "BA": "Bosnie et Herzégovine", "BW": "Botswana", "BR": "Brésil", "IO": "Territoire britannique de l'océan Indien", "BN": "Brunei Darussalam", "BG": "Bulgarie", "BF": "Burkina Faso", "BI": "Burundi", "CV": "Cabo Verde", "KH": "<PERSON><PERSON><PERSON>", "CM": "<PERSON><PERSON><PERSON>", "KY": "Îles Caïmans", "CF": "République centrafricaine", "TD": "Tchad", "CL": "<PERSON><PERSON>", "CN": "<PERSON>e", "CX": "L'île de Noël", "CC": "Îles Cocos (Keeling)", "CO": "<PERSON><PERSON><PERSON>", "KM": "Comores", "CG": "Congo", "CD": "Congo, République démocratique du", "CK": "Îles Cook", "CR": "Costa Rica", "CI": "Côte d'Ivoire", "CU": "Cuba", "CY": "Chypre", "DJ": "Djibouti", "DM": "<PERSON>", "DO": "République dominicaine", "EC": "Équateur", "EG": "Égypte", "SV": "El Salvador", "GQ": "<PERSON><PERSON><PERSON>", "ER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SZ": "<PERSON><PERSON><PERSON><PERSON>", "ET": "Éthiopie", "FK": "Îles Malouines (Malvinas)", "FO": "Îles Féroé", "FJ": "<PERSON><PERSON><PERSON>", "GF": "<PERSON>ane française", "PF": "Polynésie française", "GA": "Gabon", "GM": "<PERSON><PERSON><PERSON>", "GE": "Géorgie", "GH": "Ghana", "GI": "Gibraltar", "GL": "Groenland", "GD": "Grenade", "GP": "Guadeloupe", "GT": "Guatemala", "GG": "Guernesey", "GN": "Guinée", "GW": "Guinée-Bissau", "GY": "<PERSON><PERSON>", "HT": "<PERSON><PERSON><PERSON>", "HN": "Honduras", "HK": "Hong Kong", "IN": "Inde", "ID": "Indonésie", "IR": "Iran, République islamique d'", "IQ": "L'Irak", "IE": "<PERSON><PERSON><PERSON>", "IM": "Île de Man", "IL": "<PERSON><PERSON><PERSON>", "IT": "Italie", "JM": "Jamaïque", "JP": "Japon", "JE": "Jersey", "JO": "<PERSON><PERSON>", "KZ": "Kazakhstan", "KE": "Kenya", "KI": "Kiribati", "KP": "<PERSON><PERSON><PERSON>, République populaire démocratique de", "KR": "Corée, République de", "KW": "<PERSON><PERSON><PERSON><PERSON>", "KG": "Kirghizistan", "LA": "République démocratique populaire lao", "LB": "Liban", "LS": "Lesotho", "LR": "Libéria", "LY": "Libye", "MO": "Macao", "MG": "Madagascar", "MW": "Malawi", "MY": "Malaisie", "MV": "Maldives", "ML": "Mali", "MT": "Malte", "MH": "Îles Marshall", "MQ": "Martinique", "MR": "Mauri<PERSON><PERSON>", "MU": "<PERSON>", "YT": "Mayotte", "MX": "Mexique", "FM": "Micronésie, États fédérés de", "MD": "Moldavie, République de", "MC": "Monaco", "MN": "<PERSON><PERSON>", "ME": "Monténégro", "MS": "Montserrat", "MA": "Maroc", "MZ": "Mozambique", "MM": "Myanmar", "NA": "<PERSON><PERSON><PERSON>", "NR": "Nauru", "NP": "Népal", "NC": "Nouvelle-Calédonie", "NZ": "Nouvelle-Zélande", "NI": "Nicaragua", "NE": "Niger", "NG": "Nigéria", "NU": "Niue", "NF": "Île Norfolk", "MK": "Macé<PERSON>ine du Nord", "NO": "Norvège", "OM": "Oman", "PK": "Pakistan", "PW": "<PERSON><PERSON>", "PS": "Palestine, État de", "PA": "Panama", "PG": "Papouasie-Nouvelle-Guinée", "PY": "Paraguay", "PE": "<PERSON><PERSON><PERSON>", "PH": "Philippines", "PN": "Pitcairn", "PL": "Pologne", "PT": "Portugal", "QA": "Qatar", "RE": "Réunion", "RU": "Fédération de Russie", "RW": "Rwanda", "BL": "<PERSON>", "SH": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ascension et Tristan <PERSON>", "KN": "Saint-Kitts-et-Nevis", "LC": "Sainte-<PERSON><PERSON>", "MF": "<PERSON> (partie française)", "PM": "Saint-Pierre et Miquelon", "VC": "Saint-Vincent-et-les-Grenadines", "WS": "Samoa", "SM": "Saint-<PERSON>", "ST": "Sao Tomé et Principe", "SA": "<PERSON><PERSON>", "SN": "Sénégal", "RS": "<PERSON><PERSON>", "SC": "Seychelles", "SL": "Sierra Leone", "SG": "Singapour", "SX": "<PERSON>t Ma<PERSON>n (partie néerlandaise)", "SB": "Îles Salomon", "SO": "<PERSON><PERSON>", "ZA": "Afrique du Sud", "GS": "Géorgie du Sud et îles Sandwich du Sud", "SS": "Sud Soudan", "LK": "Sri Lanka", "SD": "<PERSON><PERSON><PERSON>", "SR": "Suriname", "SJ": "Svalbard et <PERSON>", "CH": "Suisse", "SY": "République arabe syrienne", "TW": "Taïwan, Province de Chine", "TJ": "Tadjikistan", "TZ": "Tanzanie, République unie de", "TH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TL": "Timor-Leste", "TG": "Togo", "TK": "Tokelau", "TO": "Tonga", "TT": "Trinité-et-Tobago", "TN": "<PERSON><PERSON><PERSON>", "TR": "<PERSON><PERSON><PERSON><PERSON>", "TM": "Turkménistan", "TC": "Îles Turks et Caicos", "TV": "Tuvalu", "UG": "<PERSON><PERSON><PERSON>", "UA": "Ukraine", "AE": "Émirats arabes unis", "UM": "Îles mineures éloignées des États-Unis", "UY": "Uruguay", "UZ": "Ouzbékistan", "VU": "Vanuatu", "VE": "Venezuela", "VN": "Viet Nam", "VG": "Îles Vierges britanniques", "VI": "Îles Vierges, États-Unis", "WF": "Wallis et Futuna", "EH": "Sahara occidental", "YE": "<PERSON><PERSON><PERSON>", "ZM": "Zambie", "ZW": "Zimbabwe"}, "select_country": "Sélectionner un pays"}, "public": {"login": {"title": "Connexion", "email": "<PERSON><PERSON><PERSON>", "password": "Mot de passe", "login_button": "Connexion", "remember_me_label": "Souvenez-vous de moi", "forgot_password_link": "Vous avez oublié votre mot de passe ?", "signup_link": "Ouvrir un compte", "logging_in": "Connexion"}, "signup": {"title": "S'inscrire", "email": "<PERSON><PERSON><PERSON>", "email_error": "L'adresse électronique doit être fournie", "email_invalid_error": "Un courriel valide doit être fourni", "password": "Mot de passe", "password_error": "Le mot de passe doit être fourni", "password_confirm": "Confirmer le mot de passe", "password_confirm_error": "Le mot de passe doit correspondre", "signup_button": "S'inscrire", "signing_up": "En cours", "remember_me_label": "Souvenez-vous de moi", "forgot_password_link": "Vous avez oublié votre mot de passe ?", "login_link": "Vous avez déjà un compte ? Connectez-vous maintenant.", "success_message": "Vous vous êtes inscrit avec succès. Veuillez vérifier votre courrier électronique."}, "forgot_password": {"title": "Réinitialiser le mot de passe", "email": "<PERSON><PERSON><PERSON>", "email_error": "Un courriel doit être fourni.", "in_progress": "En cours", "login_link": "Vous vous souvenez de votre mot de passe ? Se connecter", "success_message": "Veuillez vérifier votre courrier électronique", "request_button": "Réinitialiser le mot de passe"}, "forgot_password_confirm": {"title": "Réinitialiser le mot de passe", "password": "Mot de passe", "password_error": "Un mot de passe doit être fourni.", "password_length_error": "Le mot de passe doit comporter au moins 7 caractères", "password_confirm": "Confirmer", "password_confirm_error": "Les mots de passe doivent correspondre", "reset_button": "Réinitialiser le mot de passe", "in_progress": "En cours", "login_link": "Cliquez ici pour vous connecter.", "success_message": "Votre mot de passe a été réinitialisé. Vous pouvez maintenant vous connecter.", "request_button": "Réinitialiser le mot de passe"}, "confirm_email": {"error_message": "Ce lien n'est pas valide", "success_message": "Votre courriel est maintenant confirmé et vous pouvez vous connecter.", "login_link": "Cliquez ici pour vous connecter."}}, "app": {"menu": {"main": {"reports": "Rapports", "subscriptions": "Abonnements", "finance": "Finances", "settings": "Paramètres", "customers": "Clients", "products": "Produits", "invoices": "Factures", "system": "Système", "docs": "Documentation", "workflows": "Flux de travail", "developers": "Développeurs", "home": "Accueil", "customer_list": "Liste des clients", "mobile": {"show": "<PERSON><PERSON><PERSON><PERSON> le menu", "hide": "<PERSON><PERSON> le menu"}, "tax": "<PERSON><PERSON><PERSON><PERSON>", "customer_support_integrations": "Support Intégrations"}}, "team": {"main": {"title": "Paramètres de l'équipe", "add_team_member": "Ajouter un membre de l'équipe"}, "invite": {"title": "Ajouter un membre de l'équipe", "close": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "invite_successfully_sent": "L'invitation a été envoyée avec succès.", "send": "Envoyer l'invitation", "sending": "Envoi", "send_another": "Envoyer un autre"}, "pending_invites": {"title": "Invitations en attente", "none": "Il n'y a pas d'invitation en cours", "email": "<PERSON><PERSON><PERSON>", "invited_at": "In<PERSON><PERSON>", "cancel": "Annuler", "cancelling": "Annulation"}, "members": {"email": "<PERSON><PERSON><PERSON>", "created_at": "S'inscrire à", "disable": "Désactiver", "disabling": "En cours", "active": "Actif", "disabled": "Désactiver"}}, "plan": {"main": {"title": "Plan", "payment_schedule_yearly": "<PERSON><PERSON>", "payment_schedule_monthly": "<PERSON><PERSON><PERSON>", "payment_schedule_label": "<PERSON><PERSON><PERSON> de <PERSON>aiement", "select_plan": "Sélectionner un plan", "selected_plan": "Actuellement actif", "change": "Modification du plan", "payment_settings": "Paramètres de paiement", "cancel_button": "Annuler", "in_progress": "Traitement", "features": "Caractéristiques", "your_current_plan": "Votre plan actuel", "plan_options": "Options du plan"}}, "user": {"settings": {"title": "Paramètres de l'utilisateur", "name": "Nom", "email": "Courrier électronique", "password": "Mot de passe", "locale": "Localité", "save": "Économiser", "error_message": "Il y a eu un problème lors de l'enregistrement des paramètres de l'utilisateur. Veuillez vérifier les erreurs.", "success_message": "La sauvegarde des paramètres a été effectuée avec succès.", "danger_zone": "Zone de danger", "current_password": "Mot de passe actuel", "new_password": "Nouveau mot de passe", "new_password_again": "Confirmer le mot de passe", "change_password": "Modifier le mot de passe", "need_current_password": "Vous devez fournir votre mot de passe actuel", "need_new_password": "Besoin de fournir un nouveau mot de passe", "need_valid_password": "Le mot de passe doit comporter plus de 8 caractères", "need_password_to_match": "Les mots de passe doivent correspondre", "in_progress": "En cours"}, "invite": {"title": "Inviter un utilisateur", "email": "Courrier électronique", "send": "Envoyer", "in_progress": "En cours", "success_message": "Invitation envoyée avec succès !", "need_email": "Besoin d'un email à fournir", "error_message": "Impossible d'envoyer une invitation.", "role": "R<PERSON><PERSON>"}}, "billing": {"details": {"title": "Détails de la facturation", "street_line_one": "Ligne de rue 1", "street_line_two": "Ligne de rue deux", "city": "Ville", "region": "État", "country": "Pays", "postal_code": "Code postal", "submit": "Économiser"}, "main": {"title": "Facturation", "details": "Détails de la facturation", "methods": "Modes de paiement", "invoices": "Factures"}, "card_form": {"name": "Nom", "number": "Numéro de la carte", "exp_month": "Mois d'expiration", "exp_year": "Année d'expiration", "cvc": "Code de sécurité", "add_card": "Ajouter une carte"}, "payment_methods": {"title": "Modes de paiement", "card_number": "Nombre", "card_expiry": "Date d'expiration de la carte", "is_default": "Mode de paiement par défaut", "make_default_btn": "Rendre par défaut", "delete_btn": "<PERSON><PERSON><PERSON><PERSON>", "add_card_btn": "Ajouter une nouvelle carte", "no_saved_payment_methods": "Aucun moyen de paiement n'a été sauvegardé"}}, "customer": {"list": {"title": "Clients", "email": "<PERSON><PERSON><PERSON>", "country": "Pays", "reference": "Référence", "no_customers": "Il n'y a actuellement aucun client existant", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view_btn": "Voir", "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre", "no_filters": "Pas de filtre", "country": "Pays", "company_name": "Nom de l'entreprise"}, "loading": "Chargement des résultats", "error_message": "Une erreur s'est produite", "company_name": "Nom de l'entreprise"}, "create": {"title": "<PERSON><PERSON>er un nouveau client", "email": "<PERSON><PERSON><PERSON>", "street_line_one": "Ligne de rue 1", "street_line_two": "Ligne de rue 2", "city": "Ville", "region": "Région", "country": "Pays", "post_code": "Code postal", "reference": "Référence", "external_reference": "Référence externe", "advance": "avancer", "submit_btn": "<PERSON><PERSON><PERSON> un client", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Création réussie d'un client", "address_title": "<PERSON><PERSON><PERSON>", "locale": "Localité", "billing_type": "Type de facturation", "billing_type_card": "<PERSON><PERSON>", "billing_type_invoice": "Facture", "company_name": "Nom de l'entreprise", "brand": "Marque", "tax_number": "Numéro d'identification fiscale", "standard_tax_rate": "Taux d'imposition normal", "type": "Type de client", "type_business": "Entreprises", "type_individual": "Individuel", "help_info": {"email": "L'adresse électronique du client à laquelle les factures doivent être envoyées", "locale": "La locale à utiliser pour la langue", "company": "Le nom de l'entreprise", "street_line_one": "Première ligne de l'adresse de facturation (rue)", "street_line_two": "Deuxième ligne de l'adresse de facturation (rue)", "city": "La ville de l'adresse de facturation", "region": "Région/état de l'adresse de facturation", "country": "Pays de facturation du client - code pays ISO 3166-1 alpha-2.", "post_code": "Code postal de l'adresse de facturation", "reference": "Votre référence interne pour le client", "billing_type": "Comment le client doit être facturé. Carte signifie que les paiements sont effectués automatiquement au moyen d'une carte enregistrée. Facture signifie que le client reçoit une facture et paie manuellement", "external_reference": "La référence du client utilisée par le prestataire de paiement. Ne rien indiquer, sauf si vous êtes absolument certain d'avoir la bonne référence.", "brand": "La marque à laquelle le client appartiendra.", "tax_number": "Le numéro d'identification fiscale du client", "standard_tax_rate": "Le taux d'imposition à appliquer au client pour tout ce qui n'est pas des services numériques", "type": "Si le client est une entreprise ou un particulier", "invoice_format": "Le format à utiliser pour la création et la livraison d'une facture"}, "failed_message": "Impossible de créer un client", "invoice_format": "Format de la facture", "metadata": {"title": "Métadonnées", "name": "Nom", "value": "<PERSON><PERSON>", "no_values": "Pas de valeurs de métadonnées", "add": "Ajouter des métadonnées"}}, "view": {"title": "Voir les détails du client", "update": "Mise à jour", "disable": "Désactiver", "enable": "Activer", "error": {"not_found": "Aucun client n'a été trouvé", "unknown": "Une erreur inconnue s'est produite"}, "main": {"title": "<PERSON><PERSON><PERSON> p<PERSON>", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence interne", "external_reference": "Référence externe", "status": "Statut", "locale": "Localité", "brand": "Marque", "billing_type": "Type de facturation", "tax_number": "Numéro d'identification fiscale", "standard_tax_rate": "Taux d'imposition normal", "type": "Type", "marketing_opt_in": "Marketing Opt In"}, "address": {"company_name": "Nom de l'entreprise", "title": "<PERSON><PERSON><PERSON>", "street_line_one": "Ligne de rue 1", "street_line_two": "Ligne de rue 2", "city": "Ville", "region": "Région", "post_code": "Code postal", "country": "Pays"}, "credit_notes": {"title": "Notes de crédit", "list": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "created_by": "C<PERSON><PERSON> par", "created_at": "<PERSON><PERSON><PERSON>"}, "no_credit_notes": "Pas de notes de crédit pour ce client"}, "credit": {"title": "Ajustements de crédit", "list": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "created_by": "C<PERSON><PERSON> par", "created_at": "<PERSON><PERSON><PERSON>"}, "no_credit": "Pas de crédit pour ce client", "add_button": "Ajouter"}, "subscriptions": {"title": "Abonnements", "list": {"plan_name": "Plan", "status": "Statut", "schedule": "<PERSON><PERSON><PERSON>", "created_at": "<PERSON><PERSON><PERSON>", "valid_until": "Prochaine facture", "view": "Voir"}, "add_new": "Ajouter un nouvel abonnement", "no_subscriptions": "Pas d'abonnement"}, "subscription_events": {"title": "Événements d'abonnement", "list": {"event": "Événement", "subscription": "Abonnement", "created_at": "<PERSON><PERSON><PERSON>"}, "no_subscription_events": "Pas d'événements d'abonnement"}, "payments": {"title": "Paiements", "list": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "status": "Statut", "created_at": "<PERSON><PERSON><PERSON>"}, "no_payments": "Pas encore de paiements pour ce client"}, "refunds": {"title": "Remboursements", "list": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "created_by": "C<PERSON><PERSON> par", "created_at": "<PERSON><PERSON><PERSON>"}, "no_refunds": "Pas de remboursement pour ce client"}, "payment_details": {"title": "Modalités de paiement", "list": {"brand": "Marque", "last_four": "Les quatre derniers", "default": "Paiement par défaut", "expiry_month": "Mois d'expiration", "expiry_year": "Année d'expiration", "name": "Nom"}, "add_token": "Avec jeton", "add_new": "Ajouter un nouveau", "no_payment_details": "Pas de détails sur le paiement", "delete": "<PERSON><PERSON><PERSON><PERSON>", "make_default": "Rendre par défaut"}, "limits": {"title": "Limites", "list": {"feature": "Fonctionnalité", "limit": "Limite"}, "no_limits": "Pas de limites"}, "features": {"title": "Caractéristiques", "list": {"feature": "Fonctionnalité"}, "no_features": "Aucune caractéristique"}, "invoices": {"title": "Factures", "list": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "status": "Statut", "outstanding": "Remarquable", "overdue": "En retard", "paid": "<PERSON><PERSON>", "created_at": "<PERSON><PERSON><PERSON>", "view_btn": "Voir"}, "no_invoices": "Pas de factures", "next": "Suivant", "prev": "Précédent"}, "invoice_delivery": {"title": "Liv<PERSON>son de la facture", "add_new": "Ajouter un nouveau", "list": {"method": "Méthode", "format": "Format", "detail": "Détail", "view": "Voir"}, "no_delivery_methods": "Aucune méthode de livrai<PERSON>"}, "metric_counters": {"title": "Compteurs métriques", "list": {"name": "Nom", "usage": "Utilisation", "cost": "Coût estimé"}, "no_counters": "Il n'y a pas de compteurs métriques"}, "usage_limits": {"title": "Limites d'utilisation", "add_new": "Ajouter un nouveau", "list": {"amount": "<PERSON><PERSON>", "warn_level": "Action"}, "warn_levels": {"warn": "Avertir", "disable": "Désactiver"}, "no_limits": "Il n'y a pas de limite d'utilisation pour ce client"}, "metadata": {"title": "Métadonnées", "no_metadata": "Pas de métadonnées"}, "audit_log": "Journal d'audit"}, "update": {"title": "Mise à jour du client", "email": "<PERSON><PERSON><PERSON>", "street_line_one": "Ligne de rue 1", "street_line_two": "Ligne de rue 2", "city": "Ville", "region": "Région", "country": "Pays", "post_code": "Code postal", "reference": "Référence", "company_name": "Nom de l'entreprise", "external_reference": "Référence externe", "advance": "avancer", "submit_btn": "Mise à jour", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Mise à jour réussie du client", "address_title": "<PERSON><PERSON><PERSON>", "tax_number": "Numéro d'identification fiscale", "standard_tax_rate": "Taux d'imposition normal", "locale": "Localité", "error": {"not_found": "Aucun client n'a été trouvé", "unknown": "Une erreur inconnue s'est produite"}, "billing_type": "Type de facturation", "billing_type_card": "<PERSON><PERSON>", "billing_type_invoice": "Facture", "type": "Type de client", "type_business": "Entreprises", "type_individual": "Individuel", "help_info": {"email": "L'adresse électronique du client à laquelle les factures doivent être envoyées", "locale": "La locale à utiliser pour la langue", "company_name": "Le nom de l'entreprise", "street_line_one": "Première ligne de l'adresse de facturation (rue)", "street_line_two": "Deuxième ligne de l'adresse de facturation (rue)", "city": "La ville de l'adresse de facturation", "region": "Région/état de l'adresse de facturation", "country": "Pays de facturation du client - code pays ISO 3166-1 alpha-2.", "post_code": "Code postal de l'adresse de facturation", "reference": "Votre référence interne pour le client", "billing_type": "Comment le client doit être facturé. Carte signifie que les paiements sont effectués automatiquement au moyen d'une carte enregistrée. Facture signifie que le client reçoit une facture et paie manuellement", "external_reference": "La référence du client utilisée par le prestataire de paiement. Ne rien indiquer, sauf si vous êtes absolument certain d'avoir la bonne référence.", "tax_number": "Le numéro d'identification fiscale du client", "standard_tax_rate": "Le taux d'imposition à appliquer au client pour tout ce qui n'est pas des services numériques", "type": "Si le client est une entreprise ou un particulier", "invoice_format": "Le format à utiliser pour la création et la livraison d'une facture", "marketing_opt_in": "Si le client a choisi de recevoir des courriels de marketing. Cela concerne les intégrations de newsletters."}, "invoice_format": "Format de la facture", "marketing_opt_in": "Marketing Opt In", "metadata": {"title": "Métadonnées", "name": "Nom", "value": "<PERSON><PERSON>", "no_values": "Pas de valeurs de métadonnées", "add": "Ajouter des métadonnées"}}, "menu": {"title": "Clients", "customers": "Clients"}}, "product": {"list": {"title": "Produits", "name": "Nom", "physical": "Physique", "no_products": "Il n'y a actuellement aucun produit existant", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "filter": {"title": "Filtres", "name": "Nom", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre"}, "error_message": "Une erreur s'est produite"}, "create": {"title": "Créer un nouveau produit", "name": "Nom", "external_reference": "Référence externe", "advance": "avancer", "submit_btn": "Créer un produit", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Produit créé avec succès", "failed_message": "Échec de la création du produit", "tax_rate": "Taux d'imposition", "tax_type": "Type d'impôt", "physical": "Physique", "tax_types": {"digital_services": "Services numériques", "digital_goods": "Biens numériques", "physical": "Biens/services physiques"}, "help_info": {"name": "Le nom du produit", "external_reference": "La référence du produit utilisé par le prestataire de paiement. Ne rien indiquer, sauf si vous êtes absolument certain d'avoir la bonne référence.", "tax_type": "Cela permet de taxer correctement. Les biens et services physiques sont taxés différemment des biens numériques. Dans certains pays, il existe même une taxe sur les services numériques.", "tax_rate": "Le taux de taxe à utiliser pour ce produit. Il est prioritaire sur les autres taux d'imposition.", "physical": "Ce produit est-il physique ?"}}, "view": {"title": "Voir les détails du produit", "update": "Mise à jour", "error": {"not_found": "Aucun produit de ce type n'a été trouvé", "unknown": "Une erreur inconnue s'est produite"}, "main": {"title": "<PERSON><PERSON><PERSON> p<PERSON>", "name": "Nom", "physical": "Physique", "external_reference": "Référence externe", "tax_rate": "Taux d'imposition", "tax_type": "Type d'impôt", "tax_types": {"digital_services": "Services numériques", "digital_goods": "Biens numériques", "physical": "Biens/services physiques"}}, "price": {"title": "Prix", "create": "<PERSON><PERSON>er un nouveau prix", "no_prices": "Il n'y a pas de prix actuellement", "hide": "Rendre le prix privé", "show": "Rendre le prix public", "list": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "recurring": "<PERSON><PERSON><PERSON> r<PERSON>", "schedule": "<PERSON><PERSON><PERSON> de <PERSON>aiement", "including_tax": "Prix TTC", "public": "Prix public", "external_reference": "Référence externe", "usage": "Utilisation"}}, "subscription_plan": {"title": "Plans d'abonnement", "create": "Créer un nouveau plan", "no_subscription_plans": "Il n'y a actuellement aucun plan d'abonnement", "view": "Voir", "list": {"name": "Nom", "external_reference": "Référence externe", "code_name": "Nom du code"}}}, "update": {"title": "Mise à jour du produit", "name": "Nom", "external_reference": "Référence externe", "advance": "avancer", "submit_btn": "Mise à jour", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Mise à jour réussie du produit", "address_title": "<PERSON><PERSON><PERSON>", "error": {"not_found": "Aucun produit de ce type n'a été trouvé", "unknown": "Une erreur inconnue s'est produite"}, "tax_type": "Type d'impôt", "tax_types": {"digital_services": "Services numériques", "digital_goods": "Biens numériques", "physical": "Biens/services physiques"}, "tax_rate": "Taux d'imposition", "help_info": {"name": "Le nom du produit", "external_reference": "La référence du produit utilisé par le prestataire de paiement. Ne rien indiquer, sauf si vous êtes absolument certain d'avoir la bonne référence.", "tax_type": "Cela permet de taxer correctement. Les biens et services physiques sont taxés différemment des biens numériques. Dans certains pays, il existe même une taxe sur les services numériques.", "tax_rate": "Le taux de taxe à utiliser pour ce produit. Il est prioritaire sur les autres taux d'imposition."}}, "menu": {"title": "Produit", "products": "Produits", "features": "Caractéristiques", "vouchers": "<PERSON><PERSON><PERSON>", "products_list": "Liste des produits", "metrics": "Métriques"}}, "price": {"create": {"title": "<PERSON><PERSON>er un nouveau prix", "amount": "<PERSON><PERSON>", "external_reference": "Référence externe", "advance": "avancer", "submit_btn": "Créer un prix", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Prix créé avec succès", "schedule_label": "<PERSON><PERSON><PERSON> de <PERSON>aiement", "currency": "Monnaie", "recurring": "Est-ce que A est récurrent ?", "including_tax": "Le prix inclut-il les taxes ?", "public": "Public", "help_info": {"amount": "Le prix est la monnaie du niveau inférieur. Ainsi, 1,00 USD correspond à 100 et 9,99 à 999.", "display_amount": "Ce prix serait de {amount}.", "external_reference": "La référence du produit utilisé par le prestataire de paiement. Ne rien indiquer, sauf si vous êtes absolument certain d'avoir la bonne référence.", "recurring": "S'il s'agit d'un paiement récurrent ou d'un paiement unique.", "currency": "La devise dans laquelle le client doit être facturé", "schedule": "Combien de fois le client doit-il être facturé ?", "including_tax": "Si vous voulez cacher la taxe dans le prix ou si vous voulez que le client paie la taxe lui-même", "public": "S'il s'agit d'un prix affiché publiquement", "usage": "Si le client est facturé sur la base de son utilisation d'un paramètre ou par siège.", "metric_type": "Si la mesure de l'utilisation est réinitialisée à la fin de l'échéancier de paiement et est utilisée intégralement pour la facturation ou si elle est continue et que la différence entre la dernière facture et la facture suivante est utilisée."}, "schedule": {"week": "Hebdomadaire", "month": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON>"}, "metric": "Métrique", "metric_type": "Type métrique", "create_metric": "<PERSON><PERSON> de<PERSON> créer un indicateur", "metric_types": {"resettable": "Réinitialisable", "continuous": "En continu"}, "type": "Type", "types": {"fixed_price": "Prix fixe", "package": "<PERSON><PERSON>", "per_unit": "Par unité/par siège", "tiered_volume": "Volume par paliers", "tiered_graduated": "<PERSON><PERSON>"}, "usage": "Utilisation", "units": "Unités", "tiers": "Paliers", "tiers_fields": {"first_unit": "Première unité", "last_unit": "Dernière unité", "unit_price": "Prix unitaire", "flat_fee": "<PERSON><PERSON><PERSON> for<PERSON>ita<PERSON>"}}}, "feature": {"list": {"title": "Caractéristiques", "name": "Nom", "code": "Code", "reference": "Référence", "no_features": "Il n'y a actuellement aucune caractéristique existante", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre"}, "error_message": "Une erreur s'est produite", "loading": "Caractéristiques de chargement"}, "create": {"title": "Créer une nouvelle fonctionnalité", "advance": "avancer", "submit_btn": "C<PERSON>er une fonctionnalité", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Fonctionnalité créée avec succès", "address_title": "<PERSON><PERSON><PERSON>", "fields": {"name": "Nom", "code": "Nom de code", "description": "Description"}, "help_info": {"name": "Le nom de l'élément", "code": "Le nom de code de la fonctionnalité. Il est utilisé lors de l'enregistrement d'une utilisation ou lors de la vérification des limites.", "description": "La description de la fonctionnalité"}}}, "subscription_plan": {"create": {"title": "Créer un nouveau plan d'abonnement", "main_section": {"title": "<PERSON><PERSON><PERSON> p<PERSON>", "fields": {"name": "Nom", "code_name": "Nom du code", "user_count": "Nombre d'utilisateurs", "public": "Plan accessible au public", "per_seat": "Par siège", "free": "<PERSON><PERSON><PERSON>"}, "help_info": {"name": "Le nom du plan d'abonnement", "code_name": "Le nom de code du plan à utiliser avec l'API.", "user_count": "Nombre d'utilisateurs autorisés pour ce plan", "public": "Le plan est-il accessible au public ou s'agit-il d'un plan personnalisé ?", "free": "S'agit-il d'un plan gratuit ?", "per_seat": "Le plan est-il facturé par siège ?"}}, "trial_section": {"title": "<PERSON>é<PERSON> du procès", "fields": {"has_trial": "A fait l'objet d'un procès", "is_trial_standalone": "Le procès est-il autonome ?", "trial_length_days": "Du<PERSON>e du procès en jours"}, "help_info": {"has_trial": "Si le plan prévoit une période d'essai par défaut", "trial_length_days": "Du<PERSON>e de l'essai en jours", "is_trial_standalone": "Si un essai est autonome, il n'a pas besoin de prix et l'abonnement s'interrompt à la fin de l'essai"}}, "features_section": {"title": "Caractéristiques", "columns": {"feature": "Fonctionnalité", "description": "Description"}, "create": {"name": "Nom", "code_name": "Nom du code", "description": "Description", "button": "<PERSON><PERSON><PERSON>"}, "add_feature": "Ajouter", "existing": "Caractéristiques existantes", "new": "Créer un nouveau", "no_features": "Pas de caractéristiques"}, "limits_section": {"title": "Limites", "columns": {"limit": "Limite", "feature": "Fonctionnalité", "description": "Description"}, "fields": {"limit": "Limite", "feature": "Fonctionnalité"}, "add_limit": "Ajouter", "no_limits": "Pas de limites"}, "prices_section": {"title": "Prix", "columns": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "schedule": "<PERSON><PERSON><PERSON>"}, "create": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "recurring": "<PERSON><PERSON><PERSON>", "schedule": "<PERSON><PERSON><PERSON>", "including_tax": "Taxe incluse", "public": "Public", "button": "<PERSON><PERSON><PERSON>"}, "add_price": "Ajouter", "existing": "Prix existants", "new": "Créer un nouveau", "no_prices": "Pas de prix"}, "submit_btn": "Créer un plan"}, "view": {"title": "Voir les détails du plan d'abonnement", "update": "Mise à jour", "error": {"not_found": "Aucun plan d'abonnement n'a été trouvé", "unknown": "Une erreur inconnue s'est produite"}, "main": {"title": "<PERSON><PERSON><PERSON> p<PERSON>", "name": "Nom", "code_name": "Nom du code", "per_seat": "Par siège", "free": "<PERSON><PERSON><PERSON>", "user_count": "Nombre d'utilisateurs", "public": "Disponible au public", "has_trial": "A un procès", "trial_length_days": "Du<PERSON><PERSON> du procès", "is_trial_standalone": "Le procès est-il autonome ?"}, "limits": {"title": "Limites", "list": {"feature": "Fonctionnalité", "limit": "Limite", "no_limits": "Aucune limite"}}, "features": {"title": "Caractéristiques", "list": {"feature": "Fonctionnalité", "no_features": "Aucune caractéristique"}}, "price": {"title": "Prix", "list": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "recurring": "<PERSON><PERSON><PERSON> r<PERSON>", "schedule": "<PERSON><PERSON><PERSON> de <PERSON>aiement", "including_tax": "Prix TTC", "public": "Prix public", "external_reference": "Référence externe", "usage": "Utilisation"}}}, "update": {"title": "Mise à jour du plan d'abonnement", "advance": "avancer", "submit_btn": "Mise à jour du plan d'abonnement", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Mise à jour réussie du plan d'abonnement", "address_title": "<PERSON><PERSON><PERSON>", "fields": {"name": "Nom", "code_name": "Nom du code", "user_count": "Nombre d'utilisateurs", "public": "Plan accessible au public", "per_seat": "Par siège", "free": "<PERSON><PERSON><PERSON>", "prices": "Prix", "features": "Caractéristiques", "limits": "Limites", "has_trial": "A un procès", "trial_length_days": "Du<PERSON><PERSON> du procès", "is_trial_standalone": "Le procès est-il autonome ?"}, "help_info": {"name": "Le nom du plan", "code_name": "Le nom de code du plan à utiliser avec l'API.", "user_count": "Nombre d'utilisateurs autorisés pour ce plan", "public": "Le plan est-il accessible au public ou s'agit-il d'un plan personnalisé ?", "free": "S'agit-il d'un plan gratuit ?", "per_seat": "Le plan est-il facturé par siège ?", "has_trial": "Si le plan prévoit une période d'essai par défaut", "trial_length_days": "Du<PERSON>e de l'essai en jours", "is_trial_standalone": "Si un essai est autonome, il n'a pas besoin de prix et l'abonnement s'interrompt à la fin de l'essai"}, "features": {"title": "Caractéristiques", "add_feature": "Ajouter une fonctionnalité"}, "limits": {"title": "Limites", "add_limit": "Ajouter des limites"}, "prices": {"title": "Prix", "add_price": "Ajouter un prix"}}, "menu": {"subscription_plans": "Plans d'abonnement", "products": "Produits", "features": "Caractéristiques"}}, "payment_details": {"add": {"title": "Ajouter les détails du paiement"}, "add_with_token": {"title": "Ajouter des détails de paiement avec un jeton", "field": {"token": "<PERSON><PERSON>"}, "help_info": {"token": "Le jeton fourni par Stripe."}, "submit": "So<PERSON><PERSON><PERSON>"}}, "subscription": {"create": {"title": "Créer un nouvel abonnement", "subscription_plans": "Plans d'abonnement", "payment_details": "Modalités de paiement", "no_eligible_prices": "Il n'y a pas de prix éligibles", "prices": "Prix", "success_message": "L'abonnement a été créé avec succès", "submit_btn": "<PERSON><PERSON><PERSON>", "trial": "<PERSON><PERSON><PERSON> gratuit", "trial_length_days": "Nombre de jours", "unknown_error": "Une erreur inconnue s'est produite lors de la création", "seats": "Nombre de sièges", "help_info": {"eligible_prices": "Lorsqu'un client a déjà un abonnement actif, tout nouvel abonnement doit correspondre à la même période de facturation et à la même devise.", "trial": "Lorsqu'un client a déjà un abonnement actif, il ne peut pas bénéficier d'un nouvel essai gratuit.", "no_trial": "Ce plan n'a pas d'essai gratuit", "seats": "Le nombre de sièges pour lesquels l'abonnement doit être souscrit"}}, "view": {"title": "Voir l'abonnement", "main": {"title": "Données d'abonnement", "status": "Statut", "plan": "Plan", "plan_change": "Plan de changement", "customer": "Client", "main_external_reference": "Référence externe principale", "created_at": "<PERSON><PERSON><PERSON>", "ended_at": "<PERSON><PERSON><PERSON><PERSON> le", "valid_until": "Valable jusqu'au", "seat_number": "Numéro de siège", "change_seat": "<PERSON>r de si<PERSON>"}, "pricing": {"title": "Tarification", "price": "Prix", "recurring": "<PERSON><PERSON><PERSON>", "schedule": "<PERSON><PERSON><PERSON>", "change": "Changer", "no_price": "Pas de prix fixé pour l'abonnement"}, "payments": {"title": "Paiements", "amount": "<PERSON><PERSON>", "created_at": "<PERSON><PERSON><PERSON>", "view": "Voir", "no_payments": "Il n'y a pas encore de paiements"}, "payment_method": {"title": "Mode de paiement", "last_four": "Les quatre derniers", "expiry_month": "Mois d'expiration", "expiry_year": "Année d'expiration", "brand": "Type de carte", "invoiced": "<PERSON><PERSON><PERSON><PERSON>"}, "subscription_events": {"title": "Événements d'abonnement", "list": {"event": "Événement", "subscription": "Abonnement", "created_at": "<PERSON><PERSON><PERSON>"}, "no_subscription_events": "Pas d'événements d'abonnement"}, "buttons": {"cancel": "Annuler", "payment_method": "Mise à jour des détails de paiement", "audit_log": "Journal d'audit"}, "modal": {"seats": {"seats": "Sièges", "seats_help": "Le nombre de sièges pour le plan", "submit": "Économiser"}, "price": {"price": "Nouveau prix", "price_help": "Le nouveau prix à facturer lors de la prochaine facture", "submit": "Mise à jour"}, "plan": {"plan": "Nouveau plan", "plan_help": "Le plan pour lequel vous souhaitez modifier cet abonnement", "price": "Nouveau prix", "price_help": "Le nouveau prix à facturer lors de la prochaine facture", "submit": "Mise à jour", "when": {"title": "Quand", "next_cycle": "A utiliser pour le prochain cycle de facturation", "instantly": "Instantanément", "specific_date": "Date précise"}}, "payment_method": {"payment_method": "Utiliser les détails de paiement", "payment_method_help": "Ces données seront utilisées lors de la prochaine facturation au client.", "update_button": "Mise à jour des détails de paiement", "submit": "Mise à jour"}, "cancel": {"title": "Annuler l'abonnement", "cancel_btn": "Confirmer", "close_btn": "<PERSON><PERSON><PERSON>", "when": {"title": "Quand", "end_of_run": "Fin de la période de facturation en cours", "instantly": "Instantanément", "specific_date": "Date précise"}, "refund_type": {"title": "Type de remboursement", "none": "Aucun", "prorate": "Remboursement au prorata de l'utilisation", "full": "Remboursement intégral"}, "cancelled_message": "Annulation réussie"}}, "usage_estimate": {"title": "Utilisation Estimation du coût", "usage": "Utilisation", "estimate_cost": "Estimation du coût", "metric": "Métrique"}, "metadata": {"title": "Métadonnées", "no_metadata": "Pas de métadonnées"}}, "list": {"title": "Abonnements", "email": "Client", "status": "Statut", "plan": "Plan", "no_subscriptions": "Il n'y a actuellement aucun abonnement", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view": "Voir", "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre"}, "error_message": "Une erreur s'est produite", "filters": {"status": "Statut", "status_choices": {"cancelled": "<PERSON><PERSON><PERSON>", "active": "Actif", "blocked": "<PERSON><PERSON><PERSON><PERSON>", "overdue_payment_open": "Retard de paiement ouvert", "trial_active": "Essai actif", "trial_ended": "<PERSON><PERSON><PERSON> termin<PERSON>"}}, "loading": "Chargement des abonnements..."}, "menu": {"title": "Abonnements", "subscriptions": "Abonnements", "mass_change": "Changement de masse", "subscriptions_list": "Liste d'abonnement"}, "mass_change": {"list": {"title": "Abonnements - Changement de masse", "change_date": "Date de modification", "status": "Statut", "created_at": "<PERSON><PERSON><PERSON>", "no_mass_change": "Il n'y a actuellement aucune modification de masse des abonnements", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view": "Voir", "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre"}, "error_message": "Une erreur s'est produite"}, "create": {"title": "<PERSON><PERSON><PERSON> un changement de masse", "criteria": {"title": "Critères", "plan": "Plan", "price": "Prix", "brand": "Marque", "country": "Pays"}, "new": {"title": "Nouvelles valeurs", "plan": "Nouveau plan", "price": "Nouveau prix"}, "change_date": {"title": "Date de modification", "help_info": "Après la date de changement, tous les renouvellements se feront au nouveau prix. Le plan d'abonnement sera modifié immédiatement."}, "estimate": {"amount": "Il en résultera une modification estimée du {amount} {currency} du {schedule}"}, "submit_button": "Bouton de soumission"}, "view": {"title": "Changement d'abonnement en masse", "criteria": {"title": "Critères", "plan": "Plan", "price": "Prix", "brand": "Marque", "country": "Pays"}, "new_values": {"title": "Nouvelles valeurs", "plan": "Plan", "price": "Prix"}, "change_date": {"title": "Date de modification"}, "estimate": {"amount": "Il en résultera une modification estimée du {amount} {currency} du {schedule}"}, "export_button": "Exporter la liste des clients", "cancel": "Annuler", "uncancel": "Annuler"}}}, "payment": {"list": {"title": "Paiements", "no_payments": "Il n'y a actuellement aucun paiement", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "list": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "customer": "Client", "status": "Statut", "created_at": "<PERSON><PERSON><PERSON>"}, "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre"}, "error_message": "Une erreur s'est produite"}, "view": {"title": "Modalités de paiement", "main": {"title": "<PERSON><PERSON><PERSON> p<PERSON>", "amount": "<PERSON><PERSON>", "currency": "Monnaie", "external_reference": "Référence externe", "status": "Statut", "created_at": "<PERSON><PERSON><PERSON>"}, "customer": {"title": "Client", "email": "<PERSON><PERSON><PERSON>", "more_info": "Plus d'informations", "country": "Pays", "attach": "Jo<PERSON><PERSON> au client"}, "refunds": {"title": "Remboursements", "amount": "<PERSON><PERSON>", "reason": "<PERSON>son", "created_by": "C<PERSON><PERSON> par", "created_at": "<PERSON><PERSON><PERSON>", "none": "Aucun remboursement n'a été trouvé"}, "subscriptions": {"title": "Abonnements", "plan_name": "Nom du régime", "more_info": "Plus d'informations", "none": "Paiement non lié aux abonnements"}, "receipts": {"title": "Recettes", "created_at": "<PERSON><PERSON><PERSON>", "download": "Télécharger", "none": "Le paiement n'est pas accompagné de reçus"}, "buttons": {"refund": "Remboursement de la taxe", "generate_receipt": "Générer un reçu"}, "modal": {"attach": {"title": "Jo<PERSON><PERSON> au client", "button": "<PERSON><PERSON><PERSON>"}, "refund": {"title": "Remboursement", "amount": {"title": "<PERSON><PERSON>", "help_info": "Il s'agit du montant en monnaie mineure. Ainsi, 100 USD correspondent à 1,00 USD."}, "reason": {"title": "<PERSON>son"}, "submit": "Remboursement de la taxe", "success_message": "Remboursement créé avec succès", "error_message": "<PERSON><PERSON><PERSON> chose n'a pas fonctionné"}}}}, "refund": {"list": {"title": "Remboursements", "no_refunds": "Il n'y a pas de remboursement pour le moment", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "list": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "customer": "Client", "status": "Statut", "created_by": "C<PERSON><PERSON> par", "created_at": "<PERSON><PERSON><PERSON>"}, "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre"}, "error_message": "Une erreur s'est produite"}, "view": {"title": "Détails du remboursement", "main": {"title": "<PERSON><PERSON><PERSON> p<PERSON>", "amount": "<PERSON><PERSON>", "currency": "Monnaie", "external_reference": "Référence externe", "status": "Statut", "created_at": "<PERSON><PERSON><PERSON>"}, "buttons": {"refund": "Remboursement de la taxe"}, "modal": {"refund": {"title": "Remboursement", "amount": {"title": "<PERSON><PERSON>", "help_info": "Il s'agit du montant en monnaie mineure. Ainsi, 100 USD correspondent à 1,00 USD."}, "reason": {"title": "<PERSON>son"}, "submit": "Remboursement de la taxe"}}}}, "transactions": {"menu": {"title": "Transactions", "payments": "Paiements", "refunds": "Remboursements", "charge_backs": "Dossiers de charge", "invoices": "Factures", "unpaid_invoices": "Factures impayées", "checkout": "Sortie de caisse", "countries": "Pays", "tax_types": "Types d'impôts"}}, "settings": {"menu": {"title": "Paramètres", "user_settings": "Paramètres de l'utilisateur", "invite": "Invitation", "pdf_templates": "<PERSON>d<PERSON><PERSON> PDF", "email_templates": "Modèles d'e-mails", "tax_settings": "Paramètres fiscaux", "brand_settings": "Réglages de la marque", "notification_settings": "Paramètres de notification", "system_settings": "Paramètres du système", "users": "Utilisateurs", "stripe": "Ray<PERSON>", "api_keys": "Clés API", "exchange_rates": "Taux de <PERSON>", "integrations": "Intégrations", "vat_sense": "VatSense", "audit_log": "Journal d'audit"}, "pdf_template": {"list": {"title": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nom", "locale": "Localité", "brand": "Marque", "create_btn": "<PERSON><PERSON><PERSON>", "edit_btn": "Editer", "no_templates": "<PERSON><PERSON> <PERSON> mod<PERSON>", "error_message": "Une erreur s'est produite", "generator": "Mise à jour des paramètres du générateur"}, "update": {"title": "Mise à jour du modèle - {name}", "content": "Contenu", "save": "Économiser", "download": "Télécharger le test PDF", "template": "<PERSON><PERSON><PERSON><PERSON>", "help_info": {"template": "Utiliser le langage de modélisation Twig", "variable_docs": "Consultez la documentation pour connaître les variables disponibles"}}, "generator_settings": {"title": "Paramètres du générateur PDF", "generator": "Générateur", "tmp_dir": "Le répertoire temporaire", "api_key": "Clé <PERSON>", "bin": "Emplacement de la corbeille", "submit": "Économiser", "help_info": {"generator": "Le générateur à utiliser. En cas de doute, utiliser mpdf", "tmp_dir": "Le répertoire temporaire à utiliser. En cas de doute, utiliser /tmp", "api_key": "La clé API à utiliser", "bin": "L'emplacement de wkhtmltopdf"}}, "create": {"title": "<PERSON><PERSON><PERSON> un modèle", "content": "Contenu", "save": "Économiser", "download": "Télécharger le test PDF", "template": "<PERSON><PERSON><PERSON><PERSON>", "locale": "Localité", "type": "Type", "brand": "Marque", "help_info": {"locale": "La région à laquelle le modèle PDF est destiné", "brand": "La marque pour laquelle le modèle PDF a été créé", "type": "Le type de PDF pour lequel le modèle est utilisé", "template": "Utiliser le langage de modélisation Twig", "variable_docs": "Consultez la documentation pour connaître les variables disponibles"}}}, "brand_settings": {"list": {"title": "Réglages de la marque", "name": "Nom", "edit_btn": "Editer", "no_brands": "Aucune marque n'existe", "create_new": "<PERSON><PERSON><PERSON>", "error_message": "Une erreur s'est produite"}, "update": {"title": "Mise à jour des paramètres de la marque - {name}", "fields": {"name": "Nom", "email": "Adresse électronique", "company_name": "Nom de l'entreprise", "street_line_one": "Ligne de rue 1", "street_line_two": "Ligne de rue 2", "city": "Ville", "region": "Région", "country": "Pays", "postcode": "Code postal", "code": "Code", "tax_number": "Numéro d'identification fiscale", "tax_rate": "Taux d'imposition", "digital_services_tax_rate": "Taux de la taxe sur les services numériques", "support_email": "<PERSON><PERSON><PERSON> d'assistance", "support_phone_number": "Numéro de téléphone de l'assistance"}, "help_info": {"name": "Le nom de la marque", "code": "Le code à utiliser pour identifier la marque dans les appels API. Ce code ne peut pas être mis à jour.", "email": "L'email à utiliser lors de l'envoi d'emails au client de la marque", "company_name": "Le nom de l'entreprise à des fins de facturation", "street_line_one": "Première ligne de l'adresse de facturation (rue)", "street_line_two": "Deuxième ligne de l'adresse de facturation (rue)", "city": "La ville de l'adresse de facturation", "region": "Région/état de l'adresse de facturation", "country": "Pays de facturation du client - code pays ISO 3166-1 alpha-2.", "postcode": "Code postal de l'adresse de facturation", "tax_number": "Le numéro d'identification fiscale de l'entreprise/de la marque", "tax_rate": "Le taux d'imposition à utiliser pour votre pays d'origine ou lorsqu'aucun autre taux d'imposition ne peut être trouvé", "digital_services_tax_rate": "Le taux d'imposition à utiliser pour votre pays d'origine ou lorsqu'aucun autre taux d'imposition ne peut être trouvé pour les services numériques", "support_email": "L'adresse électronique du contact d'assistance", "support_phone_number": "Le numéro de téléphone du contact d'assistance"}, "general": "Paramètres généraux", "notifications": "Notifications", "address_title": "Adresse de facturation", "success_message": "Mise à jour", "submit_btn": "Mise à jour", "notification": {"subscription_creation": "Création d'un abonnement", "subscription_cancellation": "Annulation de l'abonnement", "expiring_card_warning": "Avertissement concernant l'expiration de la carte", "expiring_card_warning_day_before": "Avertissement concernant l'expiration de la carte - la veille", "invoice_created": "Facture créée", "invoice_overdue": "Facture en souffrance", "quote_created": "Citation Créée", "trial_ending_warning": "Avertissement de fin de procès", "before_charge_warning": "Avertissement avant la charge", "before_charge_warning_options": {"none": "Aucun", "all": "Tous", "yearly": "<PERSON><PERSON>"}, "payment_failure": "Défaut de paiement"}, "support": "Coordonnées du service d'assistance"}, "create": {"title": "Créer des paramètres de marque", "fields": {"name": "Nom", "email": "Adresse électronique", "company_name": "Nom de l'entreprise", "street_line_one": "Ligne de rue 1", "street_line_two": "Ligne de rue 2", "city": "Ville", "region": "Région", "country": "Pays", "post_code": "Code postal", "code": "Code", "tax_number": "Numéro d'identification fiscale", "tax_rate": "Taux d'imposition", "digital_services_tax_rate": "Taux de la taxe sur les services numériques", "support_email": "<PERSON><PERSON><PERSON> d'assistance", "support_phone_number": "Numéro de téléphone de l'assistance"}, "help_info": {"name": "Le nom de la marque", "code": "Le code à utiliser pour identifier la marque dans les appels API. Il ne peut pas être mis à jour. Il s'agit le plus souvent d'un code alphanumérique minuscule avec des traits de soulignement uniquement.", "tax_number": "Le numéro d'identification fiscale de la marque/entreprise", "email": "L'email à utiliser lors de l'envoi d'emails au client de la marque", "company_name": "Le nom de l'entreprise à des fins de facturation", "street_line_one": "Première ligne de l'adresse de facturation (rue)", "street_line_two": "Deuxième ligne de l'adresse de facturation (rue)", "city": "La ville de l'adresse de facturation", "region": "Région/état de l'adresse de facturation", "country": "Pays de facturation du client - code pays ISO 3166-1 alpha-2.", "postcode": "Code postal de l'adresse de facturation", "tax_rate": "Le taux d'imposition à utiliser pour votre pays d'origine ou lorsqu'aucun autre taux d'imposition ne peut être trouvé", "digital_services_tax_rate": "Le taux d'imposition à utiliser pour votre pays d'origine ou lorsqu'aucun autre taux d'imposition ne peut être trouvé pour les services numériques", "support_email": "L'adresse électronique du contact d'assistance", "support_phone_number": "Le numéro de téléphone du contact d'assistance"}, "address_title": "Adresse de facturation", "success_message": "Mise à jour", "submit_btn": "<PERSON><PERSON><PERSON>", "support": "Coordonnées du service d'assistance"}}, "email_template": {"list": {"title": "Modèles de courrier électronique", "email": "<PERSON><PERSON><PERSON>", "country": "Pays", "reference": "Référence", "brand": "Marque", "no_customers": "Il n'existe actuellement aucun modèle de courrier électronique", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "locale": "Localité", "view_btn": "Voir", "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre"}, "error_message": "Une erreur s'est produite"}, "create": {"title": "<PERSON><PERSON><PERSON> un modèle d'e-mail", "fields": {"name": "Nom", "locale": "Localité", "use_emsp_template": "Utiliser le modèle EMSP", "subject": "Sujet", "template_body": "Corps du modèle", "template_id": "ID du modèle", "brand": "Marque"}, "help_info": {"name": "Pour quel courriel ce modèle est-il destiné ?", "locale": "Pour quelle locale ce modèle est-il destiné.", "use_emsp_template": "Si le système de modèles du fournisseur de services de messagerie que vous utilisez doit être utilisé. Si vous n'êtes pas sûr, ne cochez pas la case", "subject": "Le message à mettre dans l'objet", "template_body": "Le modèle TWIG qui doit être utilisé pour générer le code html de l'e-mail.", "template_id": "L'ID du modèle qui vous a été donné par votre fournisseur de services de messagerie où vous avez créé le modèle. En cas de doute, décochez la case Utiliser le modèle emsp.", "brand": "La marque à laquelle s'adresse le modèle d'e-mail.", "variable_docs": "Consultez la documentation pour connaître les variables disponibles"}, "submit_btn": "<PERSON><PERSON><PERSON>", "success_message": "Création réussie d'un modèle d'e-mail"}, "update": {"title": "Mise à jour du modèle de courrier électronique", "fields": {"name": "Nom", "locale": "Localité", "use_emsp_template": "Utiliser le modèle EMSP", "subject": "Sujet", "template_body": "Corps du modèle", "template_id": "ID du modèle"}, "help_info": {"name": "Pour quel courriel ce modèle est-il destiné ?", "locale": "Pour quelle locale ce modèle est-il destiné.", "use_emsp_template": "Si le système de modèles du fournisseur de services de messagerie que vous utilisez doit être utilisé. Si vous n'êtes pas sûr, ne cochez pas la case", "subject": "Le message à mettre dans l'objet", "template_body": "Le modèle TWIG qui doit être utilisé pour générer le code html de l'e-mail.", "template_id": "L'ID du modèle qui vous a été donné par votre fournisseur de services de messagerie où vous avez créé le modèle. En cas de doute, décochez la case Utiliser le modèle emsp.", "variable_docs": "Consultez la documentation pour connaître les variables disponibles"}, "submit_btn": "Mise à jour", "success_message": "Mise à jour réussie du modèle d'e-mail", "test_email": "Envoyer un courriel de test"}}, "notification_settings": {"update": {"title": "Paramètres de notification", "submit_btn": "Mise à jour", "success_message": "Mise à jour des paramètres de notification", "fields": {"send_customer_notifications": "Envoyer des notifications aux clients", "emsp": "Fournisseur de services de courrier électronique", "emsp_api_key": "Fournisseur de services de messagerie - Clé API", "emsp_api_url": "Fournisseur de services de courrier électronique - URL de l'API", "emsp_domain": "Fournisseur de services de courrier électronique - Domaine", "default_outgoing_email": "<PERSON><PERSON><PERSON> sortant par défaut"}, "help_info": {"emsp": "Le fournisseur d'accès à la messagerie que vous souhaitez utiliser. Si vous n'êtes pas sûr, utilisez le système.", "emsp_api_key": "La clé API fournie par le fournisseur de services de messagerie.", "emsp_api_url": "L'URL de l'API fournie par le fournisseur de services de messagerie.", "emsp_domain": "Le domaine du fournisseur de services de messagerie.", "send_customer_notifications": "Si vous souhaitez que BillaBear envoie des notifications aux clients telles que la création d'un abonnement, une interruption, la réception d'un paiement, etc.", "default_outgoing_email": "L'adresse électronique par défaut à utiliser pour l'envoi de notifications lorsqu'il n'existe pas de paramètres de marque"}}}, "system_settings": {"update": {"title": "Paramètres du système", "submit_btn": "Mise à jour", "success_message": "Mise à jour des paramètres du système", "fields": {"system_url": "URL du système", "timezone": "<PERSON><PERSON> ho<PERSON>", "invoice_number_generation": "Génération de numéros de factures", "subsequential_number": "<PERSON><PERSON><PERSON><PERSON>", "default_invoice_due_time": "D<PERSON>lai de facturation par défaut", "format": "Format", "invoice_generation": "Génération de factures"}, "help_info": {"system_url": "L'url de base de BillaBear se trouve à l'adresse suivante.", "timezone": "Le fuseau horaire par défaut du système", "invoice_number_generation": "Comment le numéro de facture est généré. Random est une chaîne aléatoire et subsequent signifie qu'il s'agit d'un nombre qui s'incrémente", "subsequential_number": "Le dernier numéro de facture utilisé. Le numéro de facture suivant sera supérieur d'un chiffre", "default_invoice_due_time": "Combien de temps s'écoule entre la création de la facture et la date d'échéance ?", "format": "Le format à utiliser pour la génération du numéro de facture. %S correspond au numéro séquentiel et %R à 8 caractères aléatoires.", "invoice_generation": "Lorsque de nouvelles factures pour des abonnements doivent être générées"}, "invoice_number_generation": {"random": "Nombre aléatoire", "subsequential": "Subséquente", "format": "Format"}, "default_invoice_due_time": {"30_days": "30 jours", "60_days": "60 jours", "90_days": "90 jours", "120_days": "120 jours"}, "invoice_generation_types": {"periodically": "Périodiquement", "end_of_month": "<PERSON> du mois"}}}, "user": {"list": {"title": "Utilisa<PERSON>ur", "email": "<PERSON><PERSON><PERSON>", "roles": "<PERSON><PERSON><PERSON>", "reference": "Référence", "no_customers": "Il n'y a actuellement aucun client existant", "create_new": "<PERSON><PERSON><PERSON>", "invite": "Inviter un nouvel utilisateur", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view_btn": "Voir", "list": {"email": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>"}, "invite_title": "Invitations", "invite_list": {"email": "<PERSON><PERSON><PERSON>", "sent_at": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "copy_link": "Copier le lien", "copied_link": "<PERSON><PERSON><PERSON>"}, "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre"}, "error_message": "Une erreur s'est produite", "audit_log": "Journal d'audit"}, "update": {"title": "Mise à jour de l'utilisateur", "fields": {"email": "<PERSON><PERSON><PERSON>", "roles": "<PERSON><PERSON><PERSON>"}, "help_info": {"email": "L'adresse électronique que l'utilisateur doit utiliser pour se connecter et recevoir des notifications.", "roles": "À quoi l'utilisateur doit-il avoir accès ?"}, "submit_btn": "Mise à jour", "success_message": "Mise à jour réussie de l'utilisateur"}}, "stripe": {"main": {"title": "Importation de bandes", "edit_config": "Modifier la configuration", "hide_config": "Cacher la configuration", "start_button": "Bouton de démarrage de l'importation", "already_in_progress": "Importation déjà en cours", "list": {"state": "État", "last_id": "<PERSON><PERSON> numéro d'identification traité", "created_at": "<PERSON><PERSON><PERSON>", "updated_at": "Mise à jour à", "no_results": "Jusqu'à présent, il n'y a pas eu d'importations de produits à rayures.", "view": "Voir"}, "danger_zone": {"title": "Zone de danger", "use_stripe_billing": "Utilisez Stripe Billing pour facturer les clients.", "disable_billing": "Désactiver la facturation Stripe", "enable_billing": "Activer la facturation Stripe"}, "disable_billing_modal": {"title": "Désactiver la facturation Stripe", "disable_all_subscriptions": "En désactivant la facturation Stripe, vous indiquez que vous ne voulez plus que Stripe gère la facturation des clients mais que BillaBear s'en charge. Cela vous permettra d'économiser de l'argent.", "warning": "Une fois désactivé, si vous souhaitez revenir à l'utilisation de Stripe Billing, vous devrez réinscrire manuellement tout le monde.", "cancel": "Annuler", "confirm": "Confirmer"}, "webhook": {"title": "Crochet Web", "url": "URL du webhook", "register_webhook": "Enregistrer un Webhook", "deregister_webhook": "Désinscription du Webhook", "help_info": {"url": "URL https publiquement disponible pour les appels webhook."}}, "stripe_config": {"title": "Clés API Stripe", "description": "Pour utiliser Stripe, vous devez configurer les clés API.", "stripe_private_key": "Clé privée", "help_info": {"stripe_private_key": "La clé API à utiliser pour authentifier les requêtes du backend", "stripe_public_key": "La clé API à utiliser pour authentifier les requêtes du frontend."}, "stripe_public_key": "Clé publique", "submit_button": "So<PERSON><PERSON><PERSON>", "error": "Impossible de confirmer les clés API Stripe."}}, "view_import": {"title": "Importation de bandes", "progress": "Progrès", "error": "<PERSON><PERSON><PERSON>", "last_updated_at": "<PERSON><PERSON><PERSON> mise à jour le", "last_id_processed": "Dernière identification traitée", "process": {"started": "<PERSON><PERSON><PERSON><PERSON>", "customers": "Clients", "products": "Produits", "prices": "Prix", "subscriptions": "Abonnements", "payments": "Paiements", "refunds": "Remboursements", "charge_backs": "Dossiers de charge", "completed": "<PERSON><PERSON><PERSON><PERSON>"}}}, "api_keys": {"main": {"title": "Clés API", "add_new_button": "<PERSON><PERSON>er une nouvelle clé API", "info": {"api_base_url": "URL de base de l'API"}, "list": {"name": "Nom", "key": "Clé", "expires_at": "Expires At", "created_at": "<PERSON><PERSON><PERSON>", "no_api_keys": "Il n'y a actuellement aucune clé API", "disable_button": "Désactiver"}, "create": {"title": "<PERSON><PERSON>er une nouvelle clé", "name": "Nom", "expires": "Expiration", "close": "<PERSON><PERSON><PERSON>", "create_button": "<PERSON><PERSON><PERSON>"}}}, "exchange_rates": {"title": "Taux de <PERSON>", "list": {"currency_code": "Monnaie", "rate": "<PERSON><PERSON>", "no_rates": "<PERSON>s de taux"}}, "tax_settings": {"update": {"title": "Paramètres fiscaux", "submit_btn": "So<PERSON><PERSON><PERSON>", "success_message": "Mise à jour des paramètres fiscaux", "fields": {"tax_customers_with_tax_number": "Clients fiscaux avec numéro d'identification fiscale", "eu_business_tax_rules": "<PERSON><PERSON>rer les règles de l'UE en matière de fiscalité des entreprises", "eu_one_stop_shop_rule": "Règle du guichet unique de l'UE", "vat_sense_enabled": "Activation de la détection de la TVA", "vat_sense_api_key": "Clé API VAT Sense", "validate_vat_ids": "Valider les identifiants de TVA"}, "help_info": {"tax_customers_with_tax_number": "Si la case n'est pas cochée, les clients qui ont fourni un numéro d'identification fiscale ne sont pas soumis à la taxe", "eu_business_tax_rules": "Si cette option est activée, les entreprises clientes qui ont fourni un numéro de TVA seront traitées différemment des clients normaux", "eu_one_stop_shop_rule": "Appliquer la règle du guichet unique de l'UE. Les pays de l'UE sont taxés quel que soit le seuil d'imposition.", "vat_sense_enabled": "Si vous souhaitez synchroniser quotidiennement vos règles fiscales avec la base de données VAT Sense", "vat_sense_api_key": "Votre clé API VAT Sense. <a href=\"https://vatsense.com/signup?referral=BILLABEAR\" target='_blank'>Obtenez-en un gratuitement ici</a>", "validate_vat_ids": "Si vous souhaitez valider les identifiants fiscaux par rapport à l'API VAT Sense."}}, "vatsense": {"title": "VatSense", "fields": {"vat_sense_enabled": "Activation de la détection de la TVA", "vat_sense_api_key": "Clé API VAT Sense", "validate_vat_ids": "Valider les identifiants de TVA"}, "help_info": {"vat_sense_enabled": "Si vous souhaitez synchroniser quotidiennement vos règles fiscales avec la base de données VAT Sense", "vat_sense_api_key": "Votre clé API VAT Sense. <a href=\"https://vatsense.com/signup?referral=BILLABEAR\" target='_blank'>Obtenez-en un gratuitement ici</a>", "validate_vat_ids": "Si vous souhaitez valider les identifiants fiscaux par rapport à l'API VAT Sense."}, "description": "Grâce à l'intégration de VAT Sense, vos règles fiscales sont automatiquement mises à jour lorsque des modifications sont apportées à la législation fiscale dans le monde entier. Vous pouvez également faire valider les identifiants de TVA par VAT Sense afin de vous assurer que les clients européens ont des identifiants de TVA valides.", "create_account": "Vous pouvez créer un compte gratuit.", "create_account_link": "<PERSON><PERSON><PERSON> un compte"}}}, "charge_backs": {"list": {"title": "Dossiers de charge", "no_charge_backs": "Il n'y a actuellement aucune rétrofacturation", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view_payment": "Voir le paiement", "list": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "customer": "Client", "status": "Statut", "created_at": "<PERSON><PERSON><PERSON>"}, "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre"}}}, "reports": {"dashboard": {"title": "Tableau de bord", "subscription_count": {"title": "Abonnements actifs"}, "subscription_creation": {"title": "Nouveaux abonnements"}, "subscription_cancellation": {"title": "Abonnements renouvelés"}, "payment_amount": {"title": "Recettes obtenues"}, "refund_amount": {"title": "<PERSON><PERSON>"}, "charge_back_amount": {"title": "<PERSON><PERSON> contesté"}, "estimated_mrr": "Estimation du TRM", "estimated_arr": "Estimation de l'ARR", "header": {"active_subscriptions": "Abonnements actifs", "active_customers": "Clients actifs", "unpaid_invoices": "Factures impayées"}, "buttons": {"daily": "Quotidiennement", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>", "subscriptions": "Abonnements", "payments": "Paiements"}, "links": {"customers": "Clients", "subscriptions": "Abonnements", "invoices": "Factures"}, "latest_customers": {"title": "Derniers clients", "list": {"email": "<PERSON><PERSON><PERSON>", "creation_date": "Date de création"}}, "latest_events": {"title": "Derniers événements", "list": {"event_type": "Type d'événement", "customer": "Client", "creation_date": "Date de création"}}, "latest_payments": {"title": "Derniers paiements", "list": {"amount": "<PERSON><PERSON>", "customer": "Client", "creation_date": "Date de création"}}, "payments": {"title": "Totaux des paiements"}, "loading_chart": "Chargement des données du graphique..."}, "expiring_cards": {"main": {"title": "Cartes arrivant à expiration", "list": {"customer_email": "<PERSON><PERSON><PERSON>", "card_number": "Numéro de la carte", "no_expiring_cards": "Pas de cartes sur le point d'expirer", "loading": "chargement", "view": "Voir"}}}, "menu": {"title": "Rapports", "dashboard": "Tableau de bord", "expiring_cards": "Cartes arrivant à expiration", "subscriptions": "Abonnements", "tax": "<PERSON><PERSON><PERSON><PERSON>", "churn": "Désabonnement", "lifetime": "Durée de vie"}, "subscriptions": {"overview": {"title": "Abonnements", "plans": {"title": "Ventilation des plans"}, "schedules": {"title": "Répartition du calendrier"}}, "churn": {"title": "Désabonnement", "buttons": {"daily": "Quotidiennement", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>"}}}, "vat": {"overview": {"title": "TVA", "list": {"amount": "<PERSON><PERSON>", "currency": "Monnaie", "country": "Pays"}}}, "financial": {"lifetime": {"title": "Valeur à vie", "lifespan": "Durée de vie", "lifespan_value": "{lifespan} ans", "lifetime": "Valeur à vie", "customer_count": "Nombre de clients", "filters": {"country": "Pays", "payment_schedule": "<PERSON><PERSON><PERSON> de <PERSON>aiement", "subscription_plan": "Plan d'abonnement", "brand": "Marque"}, "help_info": {"country": "Pour connaître la valeur de la durée de vie des utilisateurs de ce pays", "payment_schedule": "Pour connaître la valeur de la durée de vie des utilisateurs qui paient selon un calendrier de paiement", "subscription_plan": "Pour connaître la valeur de la durée de vie des utilisateurs d'une formule d'abonnement", "brand": "Pour connaître la valeur à vie des utilisateurs d'une marque"}, "schedules": {"week": "Hebdomadaire", "month": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON>"}, "chart": {"lifetime_values": "Valeur à vie", "customer_counts": "Nombre de clients"}, "submit": "Filtre"}}, "tax": {"title": "Rapport fiscal", "map": {"title": "Taxe perçue pour"}, "countries": {"title": "Seuils par pays", "transacted_amount": "<strong>Transacté:</strong> {currency}{transacted_amount}", "collected_amount": "<strong>Collecté:</strong> {currency}{collected_amount}", "threshold_status": "<strong><PERSON><PERSON>:</strong> {status}", "threshold_reached": "<PERSON><PERSON><PERSON>", "threshold_not_reached": "Non atteint"}, "transactions": {"title": "Exemple d'exportation", "download": "Télécharger l'exportation"}}}, "credit": {"create": {"title": "<PERSON><PERSON><PERSON> un crédit", "amount": "<PERSON><PERSON>", "currency": "Monnaie", "reason": "<PERSON>son", "type": "Type", "credit": "Crédit", "debit": "Débit", "help_info": {"type": "Type d'ajustement, crédit ou débit", "amount": "Le prix est la monnaie du niveau inférieur. Ainsi, 1,00 USD correspond à 100 et 9,99 à 999.", "display_amount": "Ce prix serait de {amount}.", "currency": "La devise dans laquelle le client doit être facturé", "reason": "Une raison facultative qui peut être utile ultérieurement."}, "success_message": "<PERSON><PERSON><PERSON> c<PERSON> avec succès", "submit_btn": "<PERSON><PERSON><PERSON>"}}, "invoices": {"list": {"title": "Factures", "unpaid_title": "Factures impayées", "email": "<PERSON><PERSON><PERSON>", "total": "Total", "currency": "Monnaie", "created_at": "<PERSON><PERSON><PERSON>", "download": "Télécharger", "charge": "Tentative de paiement", "no_invoices": "Il n'y a pas de factures ici", "next": "Suivant", "prev": "Précédent", "view_btn": "Voir la facture", "status": "Statut", "paid": "<PERSON><PERSON>", "outstanding": "Remarquable", "filter": {"title": "Filtres", "button": "Filtres", "email": "<PERSON><PERSON><PERSON>", "number": "Numéro de la facture"}, "mark_as_paid": "Marquer comme payé"}, "menu": {"title": "Factures", "invoices": "<PERSON><PERSON> lister", "unpaid_invoices": "Liste des impayés", "create": "<PERSON><PERSON>er une facture", "quotes": "Citations", "settings": "Paramètres", "invoices_list": "Liste des factures"}, "create": {"title": "<PERSON><PERSON>er une facture", "create_invoice": "<PERSON><PERSON>er une facture", "success_message": "Facture créée", "errors": {"no_customer": "Un client est nécessaire", "nothing_to_invoice": "<PERSON><PERSON> de<PERSON> ajouter un abonnement ou un article unique.", "same_currency_and_schedule": "La même devise et le même calendrier doivent être utilisés pour les abonnements", "currency": "Une devise est nécessaire", "need_description": "Besoin d'une description", "need_amount": "Montant nécessaire", "need_tax_type": "Besoin d'un type de taxe"}, "customer": {"create_customer": "<PERSON><PERSON><PERSON> un client", "fields": {"customer": "Client", "currency": "Monnaie", "due_date": "Date d'échéance"}, "help_info": {"customer": "Le client pour lequel le devis est établi", "currency": "La devise à utiliser pour la facture", "due_date": "La date d'échéance de la facture. Si aucune date n'est indiquée, la valeur par défaut du système est utilisée."}}, "subscriptions": {"title": "Abonnements", "add_new": "Ajouter un abonnement", "list": {"subscription_plan": "Plan d'abonnement", "price": "Prix", "seat_number": "Numéro de siège"}, "no_subscriptions": "Pas d'abonnement", "add_subscription": "Ajouter un abonnement"}, "items": {"title": "Éléments non récurrents", "add_item": "Ajouter un élément ponctuel", "no_items": "Pas d'éléments ponctuels", "list": {"description": "Description", "amount": "<PERSON><PERSON>", "tax_included": "Taxe incluse", "digital_product": "Produit numérique", "tax_type": "Type d'impôt"}, "tax_types": {"digital_services": "Services numériques", "digital_goods": "Biens numériques", "physical": "Biens/services physiques"}}}, "view": {"title": "Voir la facture", "main": {"title": "Informations sur la facture", "created_at": "<PERSON><PERSON><PERSON>", "pay_link": "Lien salarial", "due_date": "Date d'échéance"}, "customer": {"title": "Client", "email": "<PERSON><PERSON><PERSON>", "more_info": "Plus d'informations", "address": {"company_name": "Nom de l'entreprise", "street_line_one": "Ligne de rue 1", "street_line_two": "Ligne de rue 2", "city": "Ville", "region": "Région", "post_code": "Code postal", "country": "Pays"}}, "biller": {"title": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "more_info": "Plus d'informations", "address": {"company_name": "Nom de l'entreprise", "street_line_one": "Ligne de rue 1", "street_line_two": "Ligne de rue 2", "city": "Ville", "region": "Région", "post_code": "Code postal", "country": "Pays"}}, "lines": {"title": "Articles", "description": "Description", "tax_rate": "Taux d'imposition", "amount": "<PERSON><PERSON>", "tax_exempt": "Exemption fiscale"}, "total": {"title": "Total", "total": "Total", "sub_total": "Sous-total", "tax_total": "Taxe Total"}, "status": {"paid": "Facture payée avec succès à {date}", "outstanding": "La facture doit encore être payée."}, "actions": {"charge_card": "Carte de paiement", "mark_as_paid": "Marquer comme payé"}, "payment_failed": {"message": "Impossible d'effectuer le paiement avec succès"}, "payment_succeeded": {"message": "Paiement effectué avec succès."}, "download": "Télécharger la facture", "invoice_delivery": {"title": "Livraisons de factures", "method": "Méthode", "detail": "Détail", "status": "Statut", "created_at": "<PERSON><PERSON><PERSON>", "no_invoice_deliveries": "Pas de livraison de factures"}}, "settings": {"title": "Paramètres de facture", "update": "Mise à jour"}, "delivery": {"create": {"title": "<PERSON><PERSON>er une nouvelle facture Livraison", "fields": {"method": "Méthode", "format": "Format", "sftp": {"port": "Port", "hostname": "Nom d'hôte", "directory": "Annuaire", "username": "Nom d'utilisateur", "password": "Mot de passe"}, "webhook": {"method": "Méthode", "url": "URL"}, "email": {"email": "<PERSON><PERSON><PERSON>", "help_info": "Si aucun courriel n'est fourni, le courriel du client sera utilisé par défaut."}}, "save": "Économiser"}, "update": {"title": "Mise à jour de la livraison de la facture", "fields": {"method": "Méthode", "format": "Format", "sftp": {"port": "Port", "hostname": "Nom d'hôte", "directory": "Annuaire", "username": "Nom d'utilisateur", "password": "Mot de passe"}, "webhook": {"method": "Méthode", "url": "URL"}, "email": {"email": "<PERSON><PERSON><PERSON>", "help_info": "Si aucun courriel n'est fourni, le courriel du client sera utilisé par défaut."}}, "save": "Économiser"}, "format": {"pdf": "PDF (EN ANGLAIS)", "zugferd_v1": "ZUGFeRD V1", "zugferd_v2": "ZUGFeRD V2 - XRechnung"}}, "download": {"loading_message": "Chargement...", "format": "Choisir le format de téléchargement", "download": "Télécharger"}}, "home": {"stripe_import": {"text": "Vous n'avez pas importé vos données de stripe.", "link": "Cliquez ici pour importer maintenant", "dismiss": "<PERSON><PERSON><PERSON>"}, "update_available": {"text": "Une mise à jour est disponible", "link": "Détails de la publication", "dismiss": "<PERSON><PERSON><PERSON>"}, "default_tax": {"text": "Votre pays n'est pas pris en charge pour les taux d'imposition par défaut. Vous devez définir un taux de taxe pour votre marque par défaut !", "link": "Nouvelles marques"}}, "vouchers": {"create": {"title": "<PERSON><PERSON><PERSON> un bon", "submit": "So<PERSON><PERSON><PERSON>", "success_message": "<PERSON><PERSON> su<PERSON>, création d'un bon d'échange", "fields": {"name": "Nom", "type": "Type", "type_percentage": "Pourcentage", "type_fixed_credit": "Cré<PERSON> fixe", "percentage": "Pourcentage", "entry_type": "Type d'entrée", "entry_type_manual": "<PERSON>", "entry_type_automatic": "Automatique", "amount": "Montant - {amount}", "code": "Code", "entry_event": "Événement", "event_expired_card_added": "Ajout d'une nouvelle carte de paiement lors de l'avertissement de carte expirée"}, "help_info": {"name": "Le nom du bon", "type": "Le pourcentage est un pourcentage sur une facture et le crédit fixe donne un crédit fixe", "entry_type": "<PERSON> signifie que l'utilisateur saisit un code, automatique signifie qu'il est déclenché par un événement", "percentage": "Le pourcentage de réduction", "amount": "Le montant en {amount} que le bon fournit", "code": "Le code que le client devra fournir pour que le bon soit activé", "entry_event": "L'événement qui doit se produire pour que le bon soit activé"}}, "list": {"title": "<PERSON><PERSON><PERSON>", "no_vouchers": "Il n'y a pas de bons actuellement", "create_new": "<PERSON><PERSON>er un nouveau bon", "list": {"name": "Nom", "type": "Type", "entry_type": "Type d'entrée"}, "view_btn": "Voir", "loading": "Bons de chargement"}, "view": {"title": "<PERSON>", "main": {"name": "Nom", "type": "Type", "disabled": "Handicapés", "entry_type": "Type d'entrée", "percentage": "Pourcentage", "amount": "Montant pour {amount}", "code": "Code", "automatic_event": "Événement automatique"}, "disable": "Désactiver", "enable": "Activer"}}, "quotes": {"create": {"title": "<PERSON><PERSON>er un devis", "create_quote": "<PERSON><PERSON>er un devis", "success_message": "Citation créée", "errors": {"no_customer": "Un client est nécessaire", "nothing_to_invoice": "<PERSON><PERSON> de<PERSON> ajouter un abonnement ou un article unique.", "same_currency_and_schedule": "La même devise et le même calendrier doivent être utilisés pour les abonnements", "currency": "Une devise est nécessaire", "need_description": "Besoin d'une description", "need_amount": "Montant nécessaire", "need_tax_type": "Besoin d'un type de taxe"}, "customer": {"create_customer": "<PERSON><PERSON><PERSON> un client", "fields": {"customer": "Client", "currency": "Monnaie", "expires_at": "Expires At"}, "help_info": {"customer": "Le client pour lequel le devis est établi", "currency": "La devise à utiliser pour le devis", "expires_at": "Lorsque le devis expire et ne peut être payé"}}, "subscriptions": {"title": "Abonnements", "add_new": "Ajouter un abonnement", "list": {"subscription_plan": "Plan d'abonnement", "price": "Prix", "per_seat": "Par siège"}, "no_subscriptions": "Pas d'abonnement", "add_subscription": "Ajouter un abonnement"}, "items": {"title": "Éléments non récurrents", "add_item": "Ajouter un élément ponctuel", "no_items": "Pas d'éléments ponctuels", "list": {"description": "Description", "amount": "<PERSON><PERSON>", "tax_included": "Taxe incluse", "digital_product": "Produit numérique", "tax_type": "Type d'impôt"}, "tax_types": {"digital_services": "Services numériques", "digital_goods": "Biens numériques", "physical": "Biens/services physiques"}}}, "list": {"title": "Citations", "email": "<PERSON><PERSON><PERSON>", "total": "Total", "currency": "Monnaie", "created_at": "<PERSON><PERSON><PERSON>", "no_quotes": "Il n'y a pas de citations ici", "next": "Suivant", "prev": "Précédent", "view_btn": "Voir", "filter": {"title": "Filtres", "button": "Filtres", "email": "<PERSON><PERSON><PERSON>", "number": "Numéro de la facture"}}, "view": {"title": "Voir le devis", "quote": {"title": "Informations sur les citations", "created_by": "C<PERSON><PERSON> par", "created_at": "<PERSON><PERSON><PERSON>", "expires_at": "Expires At", "pay_link": "Lien salarial"}, "status": {"paid": "Devis payé avec succès à {date}"}, "customer": {"title": "Client", "email": "<PERSON><PERSON><PERSON>", "more_info": "Plus d'informations", "address": {"company_name": "Nom de l'entreprise", "street_line_one": "Ligne de rue 1", "street_line_two": "Ligne de rue 2", "city": "Ville", "region": "Région", "post_code": "Code postal", "country": "Pays"}}, "lines": {"title": "Articles", "description": "Description", "schedule": "<PERSON><PERSON><PERSON> de <PERSON>aiement", "tax_rate": "Taux d'imposition", "amount": "<PERSON><PERSON>", "one_off": "Unique", "tax_exempt": "Exemption fiscale"}, "total": {"title": "Total", "total": "Total", "sub_total": "Sous-total", "tax_total": "Taxe Total"}}}, "system": {"webhooks": {"webhook_endpoint": {"list": {"title": "Points de terminaison des webhooks", "add": "Ajouter un point final", "view": "Voir", "list": {"name": "Nom", "url": "URL", "status": "Statut"}, "no_endpoints": "Il n'y a actuellement aucun point d'arrivée pour les webhooks"}, "create": {"title": "Créer un point de terminaison Webhook", "fields": {"name": "Nom", "url": "URL"}, "help_info": {"name": "Le nom du point de terminaison du webhook pour faciliter son identification ultérieure", "url": "L'URL où les charges utiles doivent être envoyées"}, "create_button": "<PERSON><PERSON><PERSON>"}, "view": {"title": "Voir le point final", "main": {"title": "Info", "name": "Nom", "url": "URL"}}}, "main": {"title": "Crochets Web", "manage_endpoints": "<PERSON><PERSON><PERSON> les points d'accès", "list": {"type": "Type", "created_at": "<PERSON><PERSON><PERSON>", "view_btn": "Voir les données de l'événement", "loading": "Chargement des événements Webhook", "no_events": "Aucun événement webhook ne s'est produit"}}, "event": {"view": {"title": "Informations sur l'événement", "main": {"title": "Données sur les événements", "type": "Type d'événement", "payload": "Charge utile", "created_at": "<PERSON><PERSON><PERSON>"}, "responses": {"title": "Demandes de points d'accès", "list": {"url": "URL", "status_code": "Code de statut", "body": "Corps", "error": "<PERSON><PERSON><PERSON>", "view": "Voir", "created_at": "<PERSON><PERSON><PERSON>"}}, "info": {"title": "Voir Demande d'information", "error_message": "Message d'erreur", "status_code": "Code de statut", "body": "Organe de réponse", "processing_time": "<PERSON><PERSON><PERSON>"}}}}, "integrations": {"list": {"title": "Intégrations", "list": {"name": "Intégration"}, "slack": {"name": "<PERSON><PERSON>ck", "button": "Configurer"}}, "slack": {"webhooks": {"list": {"title": "Slack Webhooks", "name": "Nom", "webhook": "Crochet Web", "disable_btn": "Désactiver", "enable_btn": "Activer", "no_webhooks": "Il n'y a pas encore de webhooks slack", "next": "Suivant", "prev": "Précédent", "error_message": "Impossible de récupérer les webhooks de Slack", "create_new": "<PERSON><PERSON><PERSON>"}, "create": {"title": "<PERSON><PERSON><PERSON> un Webhook Slack", "fields": {"name": "Nom", "webhook": "<PERSON><PERSON>ok"}, "help_info": {"name": "Le nom utilisé pour identifier ce webhook dans BillaBear", "webhook": "L'URL fournie par Slack à utiliser en tant que webhook"}, "save_btn": "Économiser"}}, "notifications": {"list": {"title": "Notification Slack", "event": "Événement", "webhook": "Crochet Web", "disable_btn": "Désactiver", "template": "<PERSON><PERSON><PERSON><PERSON>", "enable_btn": "Activer", "no_notifications": "Il n'y a pas encore de notifications Slack", "next": "Suivant", "prev": "Précédent", "error_message": "Impossible de récupérer les notifications de Slack", "create_new": "<PERSON><PERSON><PERSON>"}, "create": {"title": "<PERSON><PERSON><PERSON> une notification Slack", "fields": {"webhook": "Crochet Web", "event": "Événement", "template": "<PERSON><PERSON><PERSON><PERSON>"}, "help_info": {"event": "L'événement qui doit déclencher la notification", "webhook": "Le webhook slack à utiliser pour la notification", "template": "Le modèle à utiliser pour l'envoi de la notification. <a href=\"https://docs.billabear.com/user/integration/slack\" target=\"_blank\">Les variables peuvent être trouvées ici</a>"}, "save_btn": "Économiser"}}, "menu": {"title": "<PERSON><PERSON>ck", "webhooks": "Crochets Web", "notification": "Notifications"}}}, "menu": {"title": "<PERSON>ils système", "webhooks": "Crochets Web", "integrations": "Intégrations"}}, "checkout": {"create": {"title": "<PERSON><PERSON>er une caisse", "create_quote": "<PERSON><PERSON>er une caisse", "success_message": "Caisse c<PERSON>", "errors": {"no_customer": "Un client est nécessaire", "nothing_to_invoice": "<PERSON><PERSON> de<PERSON> ajouter un abonnement ou un article unique.", "same_currency_and_schedule": "La même devise et le même calendrier doivent être utilisés pour les abonnements", "currency": "Une devise est nécessaire", "need_description": "Besoin d'une description", "need_amount": "Montant nécessaire", "need_tax_type": "Besoin d'un type de taxe"}, "customer": {"create_customer": "<PERSON><PERSON><PERSON> un client", "fields": {"name": "Nom", "permanent": "Permanent", "customer": "Client", "currency": "Monnaie", "slug": "<PERSON><PERSON>", "expires_at": "Expires At", "brand": "Marque"}, "help_info": {"permanent": "S'il s'agit d'une caisse permanente ou d'une caisse unique", "name": "Nom d'identification de la caisse", "customer": "Le client à qui s'adresse la caisse", "currency": "La devise à utiliser pour la caisse", "expires_at": "Lorsque le devis expire et ne peut être payé", "slug": "Le nom de l'URL. Si vous voulez que la caisse ait une jolie URL, utilisez ceci.", "brand": "La marque à laquelle appartient la caisse"}}, "subscriptions": {"title": "Abonnements", "add_new": "Ajouter un abonnement", "list": {"subscription_plan": "Plan d'abonnement", "price": "Prix", "per_seat": "Par siège"}, "no_subscriptions": "Pas d'abonnement", "add_subscription": "Ajouter un abonnement"}, "items": {"title": "Éléments non récurrents", "add_item": "Ajouter un élément ponctuel", "no_items": "Pas d'éléments ponctuels", "list": {"description": "Description", "amount": "<PERSON><PERSON>", "tax_included": "Taxe incluse", "digital_product": "Produit numérique", "tax_type": "Type d'impôt"}, "tax_types": {"digital_services": "Services numériques", "digital_goods": "Biens numériques", "physical": "Biens/services physiques"}}}, "view": {"title": "Voir la caisse", "checkout": {"title": "Informations sur le paiement", "created_by": "C<PERSON><PERSON> par", "created_at": "<PERSON><PERSON><PERSON>", "expires_at": "Expires At", "pay_link": "Lien salarial", "name": "Nom"}, "status": {"paid": "Devis payé avec succès à {date}"}, "customer": {"title": "Client", "email": "<PERSON><PERSON><PERSON>", "more_info": "Plus d'informations", "address": {"company_name": "Nom de l'entreprise", "street_line_one": "Ligne de rue 1", "street_line_two": "Ligne de rue 2", "city": "Ville", "region": "Région", "post_code": "Code postal", "country": "Pays"}}, "lines": {"title": "Articles", "description": "Description", "schedule": "<PERSON><PERSON><PERSON> de <PERSON>aiement", "tax_rate": "Taux d'imposition", "amount": "<PERSON><PERSON>", "one_off": "Unique", "tax_exempt": "Exemption fiscale"}, "total": {"title": "Total", "total": "Total", "sub_total": "Sous-total", "tax_total": "Taxe Total"}}, "list": {"title": "Caisses", "email": "<PERSON><PERSON><PERSON>", "country": "Pays", "reference": "Référence", "no_checkouts": "Il n'y a actuellement aucune caisse existante", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view_btn": "Voir", "list": {"name": "Nom", "created_at": "<PERSON><PERSON><PERSON>", "view": "Voir"}, "filter": {"title": "Filtres", "name": "Nom", "button": "Filtres", "search": "Filtre"}, "loading": "Chargement des résultats", "error_message": "Une erreur s'est produite"}}, "layout": {"topbar": {"menu": {"settings": "Paramètres", "signout": "S'inscrire"}}}, "workflows": {"cancellation_request": {"list": {"title": "Demandes d'annulation", "email": "Client", "status": "Statut", "plan": "Plan", "no_cancellation_requests": "Il n'y a actuellement aucune demande d'annulation", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view": "Voir", "edit_button": "Editer", "bulk_button": "Retraitement en vrac", "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre", "has_error": "A une erreur"}, "error_message": "Une erreur s'est produite"}, "view": {"title": "<PERSON><PERSON><PERSON> de la demande d'annulation", "subscription": {"title": "Détails de l'abonnement", "name": "Nom du régime", "customer": "Client", "original_cancellation_date": "Date d'annulation initiale"}, "details": {"title": "Détails de l'annulation", "state": "État", "when": "Quand", "refund_type": "Type de remboursement", "specific_date": "Date d'annulation"}, "error": {"title": "<PERSON><PERSON><PERSON>"}, "buttons": {"process": "Nouvelle tentative"}}, "edit": {"title": "Modifier les demandes d'annulation", "add_place": "Ajouter un lieu", "add_place_modal": {"title": "Ajouter un lieu", "from_place": "<PERSON> lieu", "to_place": "Placer", "name": "Nom", "event_handler": "Gestionnaire d'événements", "handler_options": "Options du gestionnaire", "add": "Ajouter", "required": "Est nécessaire"}, "edit_place_modal": {"title": "<PERSON><PERSON> le lieu", "delete_button": "Supprimer le lieu", "enable_button": "Activer", "disable_button": "Désactiver"}}}, "menu": {"title": "Outils de flux de travail", "cancellation_requests": "Demandes d'annulation", "subscription_creation": "Création d'un abonnement", "payment_creation": "Création de paiements", "refund_created_process": "Processus de création de remboursement", "payment_failure_process": "Processus d'échec de paiement", "charge_back_creation": "Création d'une rétrofacturation"}, "subscription_creation": {"list": {"title": "Création d'un abonnement", "email": "Client", "status": "Statut", "plan": "Plan", "no_cancellation_requests": "Il n'y a actuellement aucune création d'abonnement", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view": "Voir", "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre", "has_error": "A une erreur"}, "error_message": "Une erreur s'est produite", "edit_button": "Editer", "bulk_button": "Retraitement en vrac"}, "view": {"title": "Détails de la création de l'abonnement", "subscription": {"title": "Détails de l'abonnement", "name": "Nom du régime", "customer": "Client", "view": "Voir l'abonnement"}, "details": {"title": "Détails de la création", "state": "État"}, "error": {"title": "<PERSON><PERSON><PERSON>"}, "buttons": {"process": "Nouvelle tentative"}}, "edit": {"title": "Modifier la création d'un abonnement", "add_place": "Ajouter un lieu", "add_place_modal": {"title": "Ajouter un lieu", "from_place": "<PERSON> lieu", "to_place": "Placer", "name": "Nom", "event_handler": "Gestionnaire d'événements", "handler_options": "Options du gestionnaire", "add": "Ajouter", "required": "Est nécessaire"}}}, "payment_creation": {"list": {"title": "Création de paiements", "email": "Client", "status": "Statut", "plan": "Plan", "no_results": "Il n'y a actuellement aucun résultat", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view": "Voir", "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre", "has_error": "A une erreur"}, "bulk_button": "Retraitement en vrac", "error_message": "Une erreur s'est produite", "edit_button": "Editer"}, "view": {"title": "Détails de la création du paiement", "payment": {"title": "Modalités de paiement", "name": "Nom du régime", "customer": "Client", "view": "Voir le paiement"}, "details": {"title": "Détails de la création", "state": "État"}, "error": {"title": "<PERSON><PERSON><PERSON>"}, "buttons": {"process": "Nouvelle tentative"}}, "edit": {"title": "Modifier la création de paiements", "add_place": "Ajouter un lieu", "add_place_modal": {"title": "Ajouter un lieu", "from_place": "<PERSON> lieu", "to_place": "Placer", "name": "Nom", "event_handler": "Gestionnaire d'événements", "handler_options": "Options du gestionnaire", "add": "Ajouter", "required": "Est nécessaire"}}}, "refund_created_process": {"list": {"title": "Remboursement créé", "email": "Client", "status": "Statut", "plan": "Plan", "no_results": "Il n'y a actuellement aucun résultat", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view": "Voir", "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre", "has_error": "A une erreur"}, "error_message": "Une erreur s'est produite", "edit_button": "Editer", "bulk_button": "Retraitement en vrac"}, "view": {"title": "Détails du processus de création de remboursement", "refund": {"title": "Détails du remboursement", "name": "Nom du régime", "customer": "Client", "view": "Voir le remboursement"}, "details": {"title": "Détails de la création", "state": "État"}, "error": {"title": "<PERSON><PERSON><PERSON>"}, "buttons": {"process": "Nouvelle tentative"}}, "edit": {"title": "Modifier le processus de remboursement créé", "add_place": "Ajouter un lieu", "add_place_modal": {"title": "Ajouter un lieu", "from_place": "<PERSON> lieu", "to_place": "Placer", "name": "Nom", "event_handler": "Gestionnaire d'événements", "handler_options": "Options du gestionnaire", "add": "Ajouter", "required": "Est nécessaire"}, "edit_place_modal": {"title": "<PERSON><PERSON> le lieu", "disable_button": "Désactiver", "enable_button": "Activer"}}}, "payment_failure_process": {"list": {"title": "Défaut de paiement", "email": "Client", "status": "Statut", "plan": "Plan", "no_results": "Il n'y a actuellement aucun résultat", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view": "Voir", "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre", "has_error": "A une erreur"}, "error_message": "Une erreur s'est produite"}, "view": {"title": "Détails de la procédure d'échec de paiement", "payment": {"title": "Détails de la tentative de paiement", "amount": "<PERSON><PERSON>", "customer": "Client", "view": "Voir la facture"}, "details": {"title": "Détails de la création", "state": "État"}, "error": {"title": "<PERSON><PERSON><PERSON>"}, "buttons": {"process": "Nouvelle tentative"}}}, "charge_back_creation": {"list": {"title": "Création d'une rétrofacturation", "email": "Client", "status": "Statut", "plan": "Plan", "no_results": "Il n'y a actuellement aucun résultat", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "view": "Voir", "filter": {"title": "Filtres", "email": "<PERSON><PERSON><PERSON>", "reference": "Référence", "external_reference": "Référence externe", "button": "Filtres", "search": "Filtre", "has_error": "A une erreur"}, "error_message": "Une erreur s'est produite", "edit_button": "Editer", "bulk_button": "Retraitement en vrac"}, "view": {"title": "Détails de la création de la rétrofacturation", "payment": {"title": "Modalités de paiement", "name": "Nom du régime", "customer": "Client", "view": "Voir le paiement"}, "details": {"title": "Détails de la création", "state": "État"}, "error": {"title": "<PERSON><PERSON><PERSON>"}, "buttons": {"process": "Nouvelle tentative"}}, "edit": {"title": "Modifier la création d'une rétrofacturation", "add_place": "Ajouter un lieu", "add_place_modal": {"title": "Ajouter un lieu", "from_place": "<PERSON> lieu", "to_place": "Placer", "name": "Nom", "event_handler": "Gestionnaire d'événements", "handler_options": "Options du gestionnaire", "add": "Ajouter", "required": "Est nécessaire"}}}}, "country": {"list": {"title": "Pays", "no_countries": "Il n'y a actuellement aucun pays", "create_new": "<PERSON><PERSON><PERSON>", "next": "<PERSON> suivante", "prev": "<PERSON> p<PERSON>", "list": {"name": "Nom", "iso_code": "Code", "tax_threshold": "<PERSON>uil d'imposition", "collecting": "Perception de la taxe"}, "view": "Voir", "filter": {"title": "Filtres", "name": "Nom", "code": "Code", "button": "Filtres", "search": "Filtre", "collecting": "Perception de la taxe"}, "error_message": "Une erreur s'est produite"}, "create": {"title": "Créer un nouveau pays", "country": {"fields": {"name": "Nom", "iso_code": "Code pays", "currency": "Monnaie", "threshold": "<PERSON><PERSON>", "in_eu": "Dans l'UE ?", "tax_year": "Début de l'année fiscale", "collecting": "Percevoir l'impôt", "tax_number": "Numéro d'identification fiscale"}, "help_info": {"name": "Le nom du pays", "iso_code": "Code ISO du pays", "currency": "La devise de déclaration du pays", "threshold": "Le seuil d'imposition du pays", "in_eu": "Le pays fait-il partie de l'UE ?", "tax_year": "Date du début de l'année fiscale pour le pays", "collecting": "Si l'impôt doit toujours être perçu pour ce pays", "tax_number": "Votre numéro d'identification fiscale pour ce pays."}}, "create_button": "<PERSON><PERSON><PERSON>"}, "view": {"title": "Voir le pays", "fields": {"name": "Nom", "iso_code": "Code pays", "threshold": "<PERSON><PERSON>", "currency": "Monnaie", "in_eu": "Dans l'UE", "start_of_tax_year": "Début de l'année fiscale", "enabled": "Activé", "collecting": "Perception de la taxe", "tax_number": "Numéro d'identification fiscale", "transaction_threshold": "Seuil de transaction", "threshold_type": "Type de seuil"}, "edit_button": "Editer", "tax_rule": {"title": "Règles fiscales", "rate": "Taux d'imposition", "type": "Type d'impôt", "default": "Est par défaut", "start_date": "Date de début", "end_date": "Date de fin", "no_tax_rules": "Pas de règles fiscales", "add": "Ajouter une règle fiscale", "edit": "Editer"}, "add_tax_rule": {"tax_rate": "Taux d'imposition", "tax_type": "Type d'impôt", "valid_from": "Valable à partir de", "valid_until": "Valable jusqu'au", "title": "Ajouter une règle fiscale", "default": "Règle fiscale par défaut", "save": "Économiser", "select_tax_type": "Sélectionner le type de taxe"}, "edit_tax_rule": {"tax_rate": "Taux d'imposition", "tax_type": "Type d'impôt", "valid_from": "Valable à partir de", "valid_until": "Valable jusqu'au", "title": "Modifier la règle fiscale", "default": "Règle fiscale par défaut", "save": "Mise à jour", "select_tax_type": "Sélectionner le type de taxe"}, "states": {"title": "États", "add": "Ajouter un nouvel État", "name": "Nom", "code": "Code", "collecting": "Percevoir des taxes ?", "threshold": "<PERSON><PERSON>", "view": "Voir", "no_states": "Il n'y a pas d'État"}}, "edit": {"title": "Modifier le pays", "country": {"fields": {"name": "Nom", "iso_code": "Code pays", "currency": "Monnaie", "threshold": "<PERSON><PERSON>", "in_eu": "Dans l'UE ?", "tax_year": "Début de l'année fiscale", "enabled": "Activé", "collecting": "Percevoir l'impôt", "tax_number": "Numéro d'identification fiscale", "transaction_threshold": "Seuil de transaction", "threshold_type": "Type de seuil", "threshold_types": {"rolling": "<PERSON><PERSON><PERSON>", "calendar": "Année civile", "rolling_quarterly": "Roulage par quartiers", "rolling_accounting": "Roulage par année comptable"}}, "help_info": {"name": "Le nom du pays", "iso_code": "Code ISO du pays", "currency": "La devise de déclaration du pays", "threshold": "Le seuil d'imposition du pays", "in_eu": "Le pays fait-il partie de l'UE ?", "tax_year": "Date du début de l'année fiscale pour le pays", "enabled": "Si le pays est activé pour l'inscription des clients", "collecting": "Si l'impôt doit toujours être perçu pour ce pays", "tax_number": "Votre numéro d'identification fiscale pour ce pays.", "transaction_threshold": "Quel est le seuil de transaction pour l'État ?", "threshold_type": "Détermination de la période de calcul du seuil"}}, "update_button": "Mise à jour"}}, "tax_type": {"list": {"title": "Types d'impôts", "create_new": "Créer un nouveau", "error_message": "Une erreur s'est produite", "list": {"name": "Nom", "make_default": "Rendre par défaut", "is_default": "Est par défaut", "default": "Défaut", "update": "Mise à jour"}, "no_tax_types": "Il n'y a actuellement aucun type d'impôt"}, "create": {"title": "Créer un type de taxe", "tax_type": {"fields": {"name": "Nom", "vat_sense_type": "TVA Type de détection"}, "help_info": {"name": "Le nom de la taxe", "vat_sense_type": "Le type de taxe dans le système de VAT Sense"}}, "create_button": "<PERSON><PERSON><PERSON>"}, "update": {"title": "Mise à jour du type d'impôt", "tax_type": {"fields": {"name": "Nom", "vat_sense_type": "TVA Type de détection"}, "help_info": {"name": "Le nom de la taxe", "vat_sense_type": "Le type de taxe dans le système de VAT Sense"}}, "update_button": "Mise à jour"}}, "finance": {"integration": {"title": "Intégrations", "fields": {"integration": "Intégration", "api_key": "Clé API", "enabled": "Activé"}, "buttons": {"connect": "Connexion via OAuth", "disconnect": "Déconnexion", "save": "Économiser"}, "settings": {"title": "Paramètres"}, "xero": {"account_id": "Code de compte pour les paiements"}, "errors": {"required": "Ce champ est obligatoire", "invalid": "Ce champ n'est pas valide", "complete_error": "Une erreur s'est produite lors de l'enregistrement de ces paramètres. Veuillez réessayer."}}, "menu": {"integration": "Intégration"}}, "tax": [], "state": {"view": {"title": "Voir l'État", "edit": "Editer", "fields": {"name": "Nom", "code": "Code", "threshold": "<PERSON><PERSON>", "collecting": "<PERSON><PERSON><PERSON>", "transaction_threshold": "Seuil de transaction", "threshold_type": "Type de seuil"}, "tax_rule": {"title": "Règles fiscales", "rate": "Taux d'imposition", "type": "Type d'impôt", "default": "Est par défaut", "start_date": "Date de début", "end_date": "Date de fin", "no_tax_rules": "Pas de règles fiscales", "add": "Ajouter une règle fiscale", "edit": "Editer"}, "add_tax_rule": {"tax_rate": "Taux d'imposition", "tax_type": "Type d'impôt", "valid_from": "Valable à partir de", "valid_until": "Valable jusqu'au", "title": "Ajouter une règle fiscale", "default": "Règle fiscale par défaut", "save": "Économiser", "select_tax_type": "Sélectionner le type de taxe"}, "edit_tax_rule": {"tax_rate": "Taux d'imposition", "tax_type": "Type d'impôt", "valid_from": "Valable à partir de", "valid_until": "Valable jusqu'au", "title": "Modifier la règle fiscale", "default": "Règle fiscale par défaut", "save": "Mise à jour", "select_tax_type": "Sélectionner le type de taxe"}}, "create": {"title": "Créer un nouvel État", "state": {"fields": {"name": "Nom", "code": "Code", "collecting": "<PERSON><PERSON><PERSON>", "threshold": "<PERSON><PERSON>"}, "help_info": {"name": "Le nom de l'Etat", "code": "Le code qui est souvent utilisé comme raccourci pour l'état", "collecting": "Si nous collectons toujours des impôts pour l'État", "threshold": "Quel est le seuil économique pour l'État ?"}}, "create_button": "<PERSON><PERSON><PERSON>"}, "edit": {"title": "Editer l'État", "state": {"fields": {"name": "Nom", "code": "Code", "collecting": "<PERSON><PERSON><PERSON>", "threshold": "<PERSON><PERSON>", "transaction_threshold": "Seuil de transaction", "threshold_type": "Type de seuil", "threshold_types": {"rolling": "<PERSON><PERSON><PERSON>", "calendar": "Année civile", "rolling_quarterly": "Roulage par quartiers", "rolling_accounting": "Roulage par année comptable"}}, "help_info": {"name": "Le nom de l'Etat", "code": "Le code qui est souvent utilisé comme raccourci pour l'état", "collecting": "Si nous collectons toujours des impôts pour l'État", "threshold": "Quel est le seuil économique pour l'État ?", "transaction_threshold": "Quel est le seuil de transaction pour l'État ?", "threshold_type": "Comment est déterminée la période de temps pour le calcul du seuil ?"}}, "update_button": "Mise à jour"}}, "onboarding": {"main": {"bar": {"message": "Stripe doit être configuré avant de pouvoir utiliser BillaBear"}, "dialog": {"title": "Embarquement", "has_stripe_key": {"text": "Saisir les clés API Stripe valides", "button": "<PERSON><PERSON>r ici"}, "has_stripe_imports": {"text": "Importer des données de Stripe", "button": "Importation", "dismiss": "<PERSON><PERSON><PERSON>"}, "has_product": {"text": "<PERSON><PERSON><PERSON> le premier produit", "button": "Créer un produit"}, "has_subscription_plan": {"text": "<PERSON><PERSON><PERSON> le premier plan d'abonnement", "button": "<PERSON><PERSON><PERSON>"}, "has_customer": {"text": "<PERSON><PERSON><PERSON> un premier client", "button": "<PERSON><PERSON><PERSON>"}, "has_subscription": {"text": "Créer le premier abonnement", "button": "<PERSON><PERSON><PERSON>"}}, "error": "<PERSON><PERSON><PERSON> chose n'a pas fonctionné !"}}, "default_error_message": "<PERSON><PERSON><PERSON> chose n'a pas fonctionné !", "metric": {"list": {"title": "Métrique", "create": "<PERSON><PERSON><PERSON>", "name": "Nom", "no_metrics": "Il n'y a pas encore de métriques !", "filter": {"name": "Nom"}, "view_btn": "Voir"}, "create": {"title": "<PERSON><PERSON><PERSON> une mesure", "fields": {"name": "Nom", "code": "Code", "type": "Type", "aggregation_method": "Méthode d'agrégation", "aggregation_property": "Propriété d'agrégation", "ingestion": "Ingestion", "filters": "Filtres"}, "help_info": {"name": "Le nom de la métrique", "code": "Le code qui doit être utilisé dans les appels à l'API. Lettres minuscules, chiffres et traits de soulignement uniquement.", "type": "Si le compteur du client doit être remis à zéro à la fin d'une période d'abonnement", "aggregation_method": "Comment les événements envoyés à BillaBear doivent être regroupés.", "aggregation_property": "Quelle propriété des données de l'événement doit être utilisée pour l'agrégation.", "ingestion": "Fréquence de traitement des événements", "filters": "Les filtres qui doivent être appliqués à la charge utile de l'événement à exclure dans l'agrégation."}, "aggregation_methods": {"count": "<PERSON><PERSON>er", "sum": "Somme", "latest": "Dernières nouvelles", "unique_count": "Nombre unique", "max": "Max"}, "ingestion": {"real_time": "<PERSON><PERSON> réel", "hourly": "<PERSON><PERSON><PERSON>", "daily": "Quotidiennement"}, "filter": {"name": "Nom", "value": "<PERSON><PERSON>", "type": "Type", "no_filters": "Pas de filtre"}, "filter_type": {"inclusive": "Inclusif", "exclusive": "Exclusif"}, "create_button": "<PERSON><PERSON><PERSON>"}, "view": {"title": "Voir métrique", "main": {"name": "Nom", "code": "Code", "type": "Type", "aggregation_method": "Méthode d'agrégation", "aggregation_property": "Propriété d'agrégation", "event_ingestion": "Ingestion"}, "filters": {"title": "Filtres", "name": "Nom", "value": "<PERSON><PERSON>", "type": "Type", "inclusive": "Inclusif", "exclusive": "Exclusif"}, "update": "Mise à jour"}, "update": {"title": "Mise à jour de la métrique", "update_button": "Économiser"}}, "usage_limit": {"create": {"title": "C<PERSON>er une limite d'utilisation", "fields": {"amount": "<PERSON><PERSON>", "action": "Action"}, "help_info": {"amount": "<PERSON>ant auquel vous souhaitez limiter le client avant qu'une action ne soit entreprise.", "action": "L'action qui doit se produire lorsque la limite est dépassée."}, "actions": {"warn": "Avertir", "disable": "Désactiver"}, "submit": "<PERSON><PERSON><PERSON>"}}, "customer_support": {"integration": {"title": "Intégrations du support client", "fields": {"integration": "Intégration", "api_key": "Clé API", "enabled": "Activé"}, "buttons": {"connect": "Connexion via OAuth", "disconnect": "Déconnexion", "save": "Économiser"}, "settings": {"title": "Paramètres"}, "errors": {"required": "Ce champ est obligatoire", "invalid": "Ce champ n'est pas valide", "complete_error": "Une erreur s'est produite lors de l'enregistrement de ces paramètres. Veuillez réessayer."}, "zendesk": {"token": "<PERSON><PERSON>", "subdomain": "Sous-domaine", "username": "Nom d'utilisateur"}, "freshdesk": {"subdomain": "Sous-domaine", "api_key": "Clé API"}}}, "integrations": {"newsletter": {"title": "Intégration du bulletin d'information", "fields": {"marketing_list": "Liste de marketing", "announcement_list": "Liste des annonces"}, "no_lists": "Aucune liste n'est disponible. Entrez d'abord les détails de la connexion.", "errors": {"list_required": "Vous ne pouvez pas activer tant que vous n'avez pas sélectionné une liste. Entrez les détails de la connexion et sauvegardez, puis choisissez une liste."}, "mailchimp": {"fields": {"server_prefix": "Pré<PERSON><PERSON> du serveur"}}}, "menu": {"main": "Intégrations", "accounting": "Comptabilité", "customer_support": "Soutien à la clientèle", "newsletter": "Bulletin d'information", "notifications": "Notifications", "crm": "CRM"}, "general": {"fields": {"integration": "Intégration", "api_key": "Clé API", "enabled": "Activé"}, "buttons": {"connect": "Connexion via OAuth", "disconnect": "Déconnexion", "save": "Économiser"}, "settings": {"title": "Paramètres"}, "errors": {"required": "Ce champ est obligatoire", "invalid": "Ce champ n'est pas valide", "complete_error": "Une erreur s'est produite lors de l'enregistrement de ces paramètres. Veuillez réessayer."}}, "crm": {"title": "Intégrations CRM", "fields": {"integration": "Intégration"}, "buttons": {"connect": "Connexion via Oauth", "disconnect": "Déconnexion", "save": "Économiser"}}}, "compliance": {"audit": {"all": {"title": "Journal d'audit", "log": "Journal", "date": "Date", "billing_admin": "Connexion à l'administration de la facturation", "no_billing_admin": "Cela n'a pas été fait par un administrateur chargé de la facturation", "display_name": "Nom d'affichage", "context": "Contexte du journal", "no_logs": "Aucun journal trouvé"}, "customer": {"title": "Journal d'audit du client - {nom}"}, "billing_admin": {"title": "Journal d'audit de l'administrateur de la facturation - {nom}"}}}}, "install": {"title": "Installer", "submit_button": "Installer", "user": {"title": "Premier utilisate<PERSON> Admin", "email": "<PERSON><PERSON><PERSON>", "password": "Mot de passe"}, "settings": {"title": "Paramètres du système", "default_brand": "Marque par dé<PERSON>ut", "from_email": "Adresse électronique de l'expéditeur par défaut", "timezone": "<PERSON><PERSON> ho<PERSON>", "webhook_url": "Url de base", "currency": "Monnaie", "country": "Pays"}, "complete_text": "BillaBear a été installé ! Vous pouvez maintenant vous connecter en utilisant les détails que vous avez fournis.", "login_link": "Cliquez ici pour vous connecter", "unknown_error": "<PERSON><PERSON><PERSON> inconnue.", "stripe": {"no_api_key": "Vous devez fournir une clé API Stripe dans la variable ENV STRIPE_PRIVATE_API_KEY.", "doc_link": "Plus d'informations sur l'installation de BillaBear.", "invalid_api_key": "La clé API Stripe n'est pas valide", "support_link": "<PERSON><PERSON> pouvez demander de l'aide ici."}}}