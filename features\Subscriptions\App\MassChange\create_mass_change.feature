Feature: Create Subscription Mass Change

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
      | Product Two | prod_jf9j542       |
    And the follow brands exist:
      | Name    | Code    | Email               |
      | Example | example | <EMAIL> |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 3400   | USD      | true      | month    | true   |
      | Product One | 3300   | USD      | true      | month    | true   |
      | Product One | 3400   | GBP      | true      | month    | true   |
      | Product One | 3500   | GBP      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Two  |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Third Plan |
      | Public     | True       |
      | Per Seat   | False      |
      | User Count | 10         |
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   |
      | <EMAIL>   | UK      | cust_dfugfdu       | Customer Two   |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Three |
      | <EMAIL>  | UK      | cust_dfudsdu       | Customer Four  |
      | <EMAIL>  | UK      | cust_dfugjfdu      | Customer Five  |
      | <EMAIL>   | UK      | cust_dfugmnenf     | Customer Six   |
      | <EMAIL> | UK      | cust_dfurjg        | Customer Seven |
      | <EMAIL> | UK      | cust_drngu         | Customer Eight |
      | <EMAIL>  | UK      | cust_drmrdu        | Customer Nine  |
      | <EMAIL>   | UK      | cust_dloluesu      | Customer Ten   |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
      | Test Two          | 3000         | USD            | month          | <EMAIL> |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
      | Test Two          | 3000         | USD            | month          | <EMAIL> |
      | Test Plan         | 3300         | USD            | month          | <EMAIL> |
      | Test Plan         | 3300         | USD            | month          | <EMAIL> |
      | Test Two          | 3300         | USD            | month          | <EMAIL> |
      | Test Plan         | 3300         | USD            | month          | <EMAIL> |
      | Test Plan         | 3400         | USD            | month          | <EMAIL> |
      | Test Two          | 3400         | GBP            | month          | <EMAIL> |

  Scenario: Create Mass Change change
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a mass subscription change:
      | Target Subscription Plan | Test Plan   |
      | New Subscription Plan    | Third Plan  |
      | Date                     | +3 days     |
    Then there should be a mass subscription change that contains:
      | Target Subscription Plan | Test Plan   |
      | New Subscription Plan    | Third Plan  |
      | Date                     | +3 days     |

  Scenario: Create Mass Change change failed invalid new subscription plan
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a mass subscription change:
      | Target Subscription Plan | Test Plan   |
      | New Subscription Plan    | invalid        |
      | Date                     | +3 days     |
    Then there should not be a mass subscription change

  Scenario: Create Mass Change change failed invalid target subscription plan
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a mass subscription change:
      | Target Subscription Plan | invalid     |
      | New Subscription Plan    | Test Plan   |
      | Date                     | +3 days     |
    Then there should not be a mass subscription change

  Scenario: Create Mass Change change failed no target group
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a mass subscription change:
      | New Subscription Plan    | Test Plan   |
      | Date                     | +3 days     |
    Then there should not be a mass subscription change

  Scenario: Create Mass Change change - new price
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a mass subscription change:
      | Target Subscription Plan | Test Plan   |
      | New Price Amount         | 3400        |
      | New Price Currency       | USD         |
      | New Price Schedule       | month       |
      | Date                     | +3 days     |
    Then there should be a mass subscription change that contains:
      | Target Subscription Plan | Test Plan   |
      | New Price Amount         | 3400        |
      | New Price Currency       | USD         |
      | New Price Schedule       | month       |
      | Date                     | +3 days     |

  Scenario: Create Mass Change change - target price
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a mass subscription change:
      | Target Subscription Plan | Test Plan   |
      | Target Price Amount      | 3400        |
      | Target Price Currency    | USD         |
      | Target Price Schedule    | month       |
      | New Price Amount         | 3400        |
      | New Price Currency       | USD         |
      | New Price Schedule       | month       |
      | Date                     | +3 days     |
    Then there should be a mass subscription change that contains:
      | Target Subscription Plan | Test Plan   |
      | Target Price Amount      | 3400        |
      | Target Price Currency    | USD         |
      | Target Price Schedule    | month       |
      | New Price Amount         | 3400        |
      | New Price Currency       | USD         |
      | New Price Schedule       | month       |
      | Date                     | +3 days     |


  Scenario: Create Mass Change change - target brand
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a mass subscription change:
      | Target Subscription Plan | Test Plan   |
      | Target Price Amount      | 3400        |
      | Target Price Currency    | USD         |
      | Target Price Schedule    | month       |
      | Target Brand             | Example     |
      | New Price Amount         | 3400        |
      | New Price Currency       | USD         |
      | New Price Schedule       | month       |
      | Date                     | +3 days     |
    Then there should be a mass subscription change that contains:
      | Target Subscription Plan | Test Plan   |
      | Target Price Amount      | 3400        |
      | Target Price Currency    | USD         |
      | Target Price Schedule    | month       |
      | Target Brand             | Example     |
      | New Price Amount         | 3400        |
      | New Price Currency       | USD         |
      | New Price Schedule       | month       |
      | Date                     | +3 days     |

  Scenario: Create Mass Change change - target country
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a mass subscription change:
      | Target Subscription Plan | Test Plan   |
      | Target Price Amount      | 3400        |
      | Target Price Currency    | USD         |
      | Target Price Schedule    | month       |
      | Target Country           | US          |
      | New Price Amount         | 3400        |
      | New Price Currency       | USD         |
      | New Price Schedule       | month       |
      | Date                     | +3 days     |
    Then there should be a mass subscription change that contains:
      | Target Subscription Plan | Test Plan   |
      | Target Price Amount      | 3400        |
      | Target Price Currency    | USD         |
      | Target Price Schedule    | month       |
      | Target Country           | US          |
      | New Price Amount         | 3400        |
      | New Price Currency       | USD         |
      | New Price Schedule       | month       |
      | Date                     | +3 days     |