Feature: Manage Customer Endpoint

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      | Billing Type |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   | invoice      |
      | <EMAIL>   | UK      | cust_dfugfdu       | Customer Two   | card         |

  Scenario: Valid token
    Given there is a manage customer session for "<EMAIL>" that expires in "+5 minutes"
    When I view the manage customer endpoint for "<EMAIL>"
    Then I will see the customer portal information

  Scenario: Expired token
    Given there is a manage customer session for "<EMAIL>" that expires in "-5 minutes"
    When I view the manage customer endpoint for "<EMAIL>"
    Then I will not see the customer portal information