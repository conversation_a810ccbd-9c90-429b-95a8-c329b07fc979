Feature:

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | DE      | cust_jf9j54d       | Customer Two |

  Scenario:
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the following invoice delivery setups exist:
      | Customer                 | Type    | SFTP User | SFTP Password | SFTP Host   | SFTP Port | SFTP Dir | Webhook URL         | Webhook Method | Format |
      | <EMAIL> | Email   |           |               |             |           |          |                     |                | PDF    |
      | <EMAIL> | SFTP    | user      | password      | example.org | 2222      | .        |                     |                | PDF    |
      | <EMAIL> | Webhook |           |               |             |           |          | https://example.net | POST           | PDF    |
      | <EMAIL> | Webhook |           |               |             |           |          | https://example.org | POST           | PDF    |
    When I edit the delivery methods for "<EMAIL>" for "Webhook" with:
      | Type           | Webhook             |
      | Format         | PDF                 |
      | Webhook URL    | https://example.com |
      | Webhook Method | POST                |
    Then there should be an invoice delivery for "<EMAIL>" for type "Webhook" and url "https://example.com"

