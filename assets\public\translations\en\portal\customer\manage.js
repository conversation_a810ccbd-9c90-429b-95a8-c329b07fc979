export const PORTAL_CUSTOMER_MANAGE_TRANSLATIONS = {
    title: "Manage Customer",
    error_message: "An error occurred, please go back and try again.",
    subscriptions: {
        title: "Subscriptions",
        plan_name: "Plan",
        status: "Status",
        next_billing_cycle: "Next Billing Cycle",
        cancel: "Cancel"
    },
    payment_methods: {
        title: "Payment Methods",
        invoiced_customer: "To change your payment methods you need to contact customer support",
        make_default: "Make Default"
    },
    invoices: {
        title: "Invoices",
        download: "Download",
        number: "Invoice Number",
        status: "Status",
        paid: "Paid",
        outstanding: "Outstanding",
        paid_at: "Paid at",
        pay_now: "Pay Now",
        total: "Total"
    },
    modal: {
        cancel: {
            title: "Cancel Subscription",
            warning_message: "Are you sure you want to cancel the subscription for {plan_name}",
            button: "Confirm Cancellation"
        },
        error: {
            title: "Payment Failure",
            error_message: "Failed to process the payment with the reason \"{message}\""
        }
    }
}