Feature: Customer List
  In order to keep track of customers
  As an APP user
  I need to be see what customers are

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |

  Scenario: Raw list
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    When I use the site to list customers
    Then I should see in the site response the customer "<EMAIL>"
    And I should see in the site response the customer "<EMAIL>"

  Scenario: Raw list
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    When I use the site to list customers with parameter "email" with value "one"
    Then I should see in the site response the customer "<EMAIL>"
    Then I should not see in the site response the customer "<EMAIL>"

  Scenario: No Results
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    When I use the site to list customers with parameter "email" with value "fifty"
    Then the site response data field should be empty
    Then I should not see in the site response the customer "<EMAIL>"
    Then I should not see in the site response the customer "<EMAIL>"

  Scenario: Country filter
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    When I use the site to list customers with parameter "country" with value "UK"
    Then I should see in the site response the customer "<EMAIL>"
    Then I should not see in the site response the customer "<EMAIL>"

  Scenario: Limited to one
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    When I use the site to list customers with parameter "per_page" with value "1"
    Then I should see in the site response with only 1 result in the data set
    And the I should see in the site response there are more results

  Scenario: Pagination limited to one
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And I use the site to list customers with parameter "per_page" with value "1"
    When I use the site to list customers with the last_key from the last response
    Then I should see in the site response with only 1 result in the data set
    And the I should not see in the site response there are more results

  Scenario: Reference filter
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    When I use the site to list customers with parameter "reference" with value "One"
    Then I should see in the site response with only 1 result in the data set
    Then I should see in the site response the customer "<EMAIL>"
    Then I should not see in the site response the customer "<EMAIL>"

  Scenario: External Reference filter
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    When I use the site to list customers with parameter "external_reference" with value "dfugfdu"
    Then I should see in the site response with only 1 result in the data set
    Then I should see in the site response the customer "<EMAIL>"
    Then I should not see in the site response the customer "<EMAIL>"
