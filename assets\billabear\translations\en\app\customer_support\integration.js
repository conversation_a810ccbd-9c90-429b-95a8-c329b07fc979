export const CUSTOMER_SUPPORT_INTEGRATION_TRANSLATIONS = {
    title: "Customer Support Integrations",
    fields: {
        integration: "Integration",
        api_key: "API Key",
        enabled: "Enabled",
    },
    buttons: {
        connect: "Connect via OAuth",
        disconnect: "Disconnect",
        save: "Save",
    },
    settings: {
        title: "Settings",
    },
    errors: {
        required: "This field is required",
        invalid: "This field is invalid",
        complete_error: "An error occurred while trying to save these settings. Please try again.",
    },
    zendesk: {
        token: "Token",
        subdomain: "Subdomain",
        username: "Username",
    },
    freshdesk: {
        subdomain: "Subdomain",
        api_key: "API Key",
    }
}
