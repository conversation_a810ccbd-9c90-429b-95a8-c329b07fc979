Feature: Customer Creation
  In order to keep track of customers
  As an APP user
  I need to be register a customer

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |

  Scenario: Successfully create customer
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a customer via the app with the following info
      | Email   | <EMAIL> |
      | Country | DE                   |
    Then there should be a customer for "<EMAIL>"

  Scenario: No email
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a customer via the app with the following info
      | Email   |    |
      | Country | DE |
    Then there should be an error for "email"
    And there should not be an error for "country"

  Scenario: Invalid email
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a customer via the app with the following info
      | Email   | a-word   |
      | Country | DE |
    Then there should be an error for "email"
    And there should not be an error for "country"

  Scenario: Stripe not configured
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And there are no stripe api keys configured
    When I create a customer via the app with the following info
      | Email   | a-word   |
      | Country | DE |
    Then there should be an error for "stripe"

  Scenario: Successfully create customer with references
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a customer via the app with the following info
      | Email              | <EMAIL> |
      | Country            | DE                   |
      | External Reference | cust_4945959         |
      | Reference          | Test Customer        |
      | Post Code          | 20043                |
    Then there should be a customer for "<EMAIL>"
    And the customer "<EMAIL>" should have the external reference "cust_4945959"
    And the customer "<EMAIL>" should have the reference "Test Customer"
    And the customer "<EMAIL>" should have the post code "20043"


  Scenario: Customer already exists
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
    When I create a customer via the app with the following info
      | Email   | <EMAIL> |
      | Country | DE                   |
    Then I should be told there is a conflict

  Scenario: Successfully create customer with references and billing type
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a customer via the app with the following info
      | Email              | <EMAIL> |
      | Country            | DE                   |
      | External Reference | cust_4945959         |
      | Reference          | Test Customer        |
      | Billing Type       | invoice              |
    Then there should be a customer for "<EMAIL>"
    And the customer "<EMAIL>" should have the external reference "cust_4945959"
    And the customer "<EMAIL>" should have the reference "Test Customer"
    And the customer "<EMAIL>" should have the billing type "invoice"

  Scenario: Successfully create customer with Brand
    Given the follow brands exist:
      | Name    | Code    | Email               |
      | Example | example | <EMAIL> |
    And I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a customer via the app with the following info
      | Email              | <EMAIL> |
      | Country            | DE                   |
      | External Reference | cust_4945959         |
      | Reference          | Test Customer        |
      | Billing Type       | invoice              |
      | Brand              | example              |
      | Locale             | en                   |
    Then there should be a customer for "<EMAIL>"
    And the customer "<EMAIL>" should have the brand "example"
    And the customer "<EMAIL>" should have the locale "en"

  Scenario: Successfully create customer with Brand and tax number
    Given the follow brands exist:
      | Name    | Code    | Email               |
      | Example | example | <EMAIL> |
    And I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a customer via the app with the following info
      | Email              | <EMAIL> |
      | Country            | DE                   |
      | External Reference | cust_4945959         |
      | Reference          | Test Customer        |
      | Billing Type       | invoice              |
      | Brand              | example              |
      | Locale             | en                   |
      | Tax Number         | GB2494944            |
    Then there should be a customer for "<EMAIL>"
    And the customer "<EMAIL>" should have the tax number "GB2494944"

  Scenario: Successfully create customer as business customer
    Given the follow brands exist:
      | Name    | Code    | Email               |
      | Example | example | <EMAIL> |
    And I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a customer via the app with the following info
      | Email              | <EMAIL> |
      | Country            | DE                   |
      | External Reference | cust_4945959         |
      | Reference          | Test Customer        |
      | Billing Type       | invoice              |
      | Brand              | example              |
      | Locale             | en                   |
      | Tax Number         | GB2494944            |
      | Type               | Business             |
    Then there should be a customer for "<EMAIL>"
    And the customer "<EMAIL>" should be a business customer

  Scenario: Successfully create customer as Individual customer
    Given the follow brands exist:
      | Name    | Code    | Email               |
      | Example | example | <EMAIL> |
    And I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a customer via the app with the following info
      | Email              | <EMAIL> |
      | Country            | DE                   |
      | External Reference | cust_4945959         |
      | Reference          | Test Customer        |
      | Billing Type       | invoice              |
      | Brand              | example              |
      | Locale             | en                   |
      | Tax Number         | GB2494944            |
      | Type               | Individual           |
    Then there should be a customer for "<EMAIL>"
    And the customer "<EMAIL>" should be a individual customer

  Scenario: Error when country is not enabled
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And that the following countries exist:
      | Name           | ISO Code | Threshold | Currency | Enabled |
      | United Kingdom | GB       | 1770      | GBP      | true    |
      | United States  | US       | 0         | USD      | true    |
      | Germany        | DE       | 0         | EUR      | false   |
    When I create a customer via the app with the following info
      | Email   | <EMAIL> |
      | Country | DE                   |
    Then there should be an error for "address.country"

  Scenario: Create email pdf invoice delivery
    Given the follow brands exist:
      | Name    | Code    | Email               |
      | Example | example | <EMAIL> |
    And I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I create a customer via the app with the following info
      | Email              | <EMAIL> |
      | Country            | DE                   |
      | External Reference | cust_4945959         |
      | Reference          | Test Customer        |
      | Billing Type       | invoice              |
      | Brand              | example              |
      | Locale             | en                   |
      | Tax Number         | GB2494944            |
      | Type               | Individual           |
    Then there should be an invoice delivery for "<EMAIL>" for type "Email" and format "pdf"
