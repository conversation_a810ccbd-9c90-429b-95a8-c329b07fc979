Feature: Customer Subscription Update Price

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And there are the following tax types:
      | Name             | Physical |
      | Digital Goods    | False    |
      | Digital Services | False    |
      | Physical         | True     |
    And that the following countries exist:
      | Name           | ISO Code | Threshold | Currency |
      | United Kingdom | GB       | 1770      | GBP      |
    And the following country tax rules exist:
      | Country        | Tax Type       | Tax Rate | Valid From |
      | United Kingdom | Digital Goods  | 20       | -10 days   |
    And the follow products exist:
      | Name        | External Reference | Tax Type      |
      | Product One | prod_jf9j545       | Digital Goods |
      | Product Two | prod_jf9j542       | Digital Goods |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 3500   | USD      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |


  Scenario: Update price
    Given I have authenticated to the API
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year |
      | <EMAIL> | 0444      | 03           | 25          |
    When I update via the api the subscription "Test Plan" for "<EMAIL>" to use the 30000 in "USD" per "year" price
    Then the subscription "Test Plan" for "<EMAIL>" will be for 30000 in "USD" per "year"

  Scenario: Update price
    Given I have authenticated to the API
    And stripe billing is disabled
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 | Started Current Period |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> | -7 days                |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year |
      | <EMAIL> | 0444      | 03           | 25          |
    When I update via the api the subscription "Test Plan" for "<EMAIL>" to use the 30000 in "USD" per "year" price
    Then the subscription "Test Plan" for "<EMAIL>" will be for 30000 in "USD" per "year"
    And the subscription "Test Plan" for "<EMAIL>" will expire in a year
    And there will be an invoice for a partial amount of 30000 for "Test Plan" for "<EMAIL>"

  Scenario: Update price
    Given I have authenticated to the API
    And stripe billing is disabled
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 | Started Current Period | Next Charge |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> | -7 days                | +10 minutes |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year |
      | <EMAIL> | 0444      | 03           | 25          |
    When I update via the api the subscription "Test Plan" for "<EMAIL>" to use the 30000 in "USD" per "year" price
    Then the subscription "Test Plan" for "<EMAIL>" will be for 30000 in "USD" per "year"
    And the subscription "Test Plan" for "<EMAIL>" will expire in a year
    And there will be an invoice for the amount of 30000 for "Test Plan" for "<EMAIL>"

  Scenario: Update price - Stripe Billing
    Given I have authenticated to the API
    And stripe billing is disabled
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year |
      | <EMAIL> | 0444      | 03           | 25          |
    When I update via the api the subscription "Test Plan" for "<EMAIL>" to use the 30000 in "USD" per "year" price
    Then the subscription "Test Plan" for "<EMAIL>" will be for 30000 in "USD" per "year"
