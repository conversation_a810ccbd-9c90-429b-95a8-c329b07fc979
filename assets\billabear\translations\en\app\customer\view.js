export const CUSTOMER_VIEW_TRANSLATIONS = {
    title: 'View Customer Details',
    update: 'Update',
    disable: "Disable",
    enable: "Enable",
    audit_log: "Audit Log",
    error: {
        not_found: "No such customer found",
        unknown: "An unknown error has occurred"
    },
    main: {
        title: "Main Details",
        email: "Email",
        reference: "Internal Reference",
        external_reference: "External Reference",
        status: "Status",
        locale: "Locale",
        brand: "Brand",
        billing_type: "Billing Type",
        tax_number: "Tax Number",
        standard_tax_rate: "Standard Tax Rate",
        type: "Type",
        marketing_opt_in: "Marketing Opt In",
    },
    metadata: {
        title: "Metadata",
        no_metadata: "No metadata",
    },
    address: {
        company_name: "Company Name",
        title: "Address",
        street_line_one: "Street Line 1",
        street_line_two: "Street Line 2",
        city: "City",
        region: "Region",
        post_code: "Post Code",
        country: "Country",
    },
    credit_notes: {
        title: "Credit Notes",
        list: {
            amount: "Amount",
            currency: "Currency",
            created_by: "Created By",
            created_at: "Created At"
        },
        no_credit_notes: "No credit notes for this customer",
    },
    credit: {
        title: "Credit Adjustments",
        list: {
            amount: "Amount",
            currency: "Currency",
            created_by: "Created By",
            created_at: "Created At"
        },
        no_credit: "No credit for this customer",
        add_button: "Add"
    },
    subscriptions: {
        title: "Subscriptions",
        list: {
            plan_name: "Plan",
            status: "Status",
            schedule: "Schedule",
            created_at: "Created At",
            valid_until: "Next Billed",
            view: "View",
        },
        add_new: "Add New Subscription",
        no_subscriptions: "No Subscriptions"
    },
    subscription_events: {
        title: "Subscription Events",
        list: {
            event: "Event",
            subscription: "Subscription",
            created_at: "Created At"
        },
        no_subscription_events: "No Subscription Events"
    },
    payments: {
        title: "Payments",
        list: {
            amount: "Amount",
            currency: "Currency",
            status: "Status",
            created_at: "Created At"
        },
        no_payments: "No payments for this customer yet"
    },
    refunds: {
        title: "Refunds",
        list: {
            amount: "Amount",
            currency: "Currency",
            created_by: "Created By",
            created_at: "Created At"
        },
        no_refunds: "No refunds for this customer"
    },
    payment_details: {
        title: "Payment Details",
        list: {
            brand: "Brand",
            last_four: "Last Four",
            default: "Default Payment",
            expiry_month: "Expiry Month",
            expiry_year: "Expiry Year",
            name: "Name"
        },
        add_token: "With Token",
        add_new: "Add new",
        no_payment_details: "No payment details",
        delete: "Delete",
        make_default: "Make Default"
    },
    limits: {
        title: "Limits",
        list: {
            feature: "Feature",
            limit: "Limit"
        },
        no_limits: "No Limits"
    },
    features: {
        title: "Features",
        list: {
            feature: "Feature"
        },
        no_features: "No features"
    },
    invoices: {
        title: "Invoices",
        list:{
            amount: "Amount",
            currency: "Currency",
            status: "Status",
            outstanding: "Outstanding",
            overdue: "Overdue",
            paid: "Paid",
            created_at: "Created At",
            view_btn: "View"
        },
        no_invoices: "No invoices",
        next: "Next",
        prev: "Previous"
    },
    invoice_delivery: {
        title: "Invoice Delivery",
        add_new: "Add New",
        list: {
            method: "Method",
            format: "Format",
            detail: "Detail",
            view: "View",
        },
        no_delivery_methods: "No delivery methods",
    },
    metric_counters: {
        title: "Metric Counters",
        list: {
            name: "Name",
            usage: "Usage",
            cost: "Estimated Cost"
        },
        no_counters: "There are no metric counters"
    },
    usage_limits: {
        title: "Usage Limits",
        add_new: "Add New",
        list: {
            amount: "Amount",
            warn_level: "Action"
        },
        warn_levels: {
            warn: "Warn",
            disable: "Disable"
        },
        no_limits: "There are no usage limits for this customer"
    }
}
