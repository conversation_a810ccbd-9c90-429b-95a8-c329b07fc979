export const PRODUCT_CREATE_TRANSLATIONS = {
    title: "Create new product",
    name: "Name",
    external_reference: "External Reference",
    advance: "advance",
    submit_btn: "Create Product",
    show_advanced: "Advanced",
    success_message: "Successfully created product",
    failed_message: "Failed to create product",
    tax_rate: "Tax Rate",
    tax_type: "Tax Type",
    physical: "Physical",
    tax_types: {
        digital_services: "Digital Services",
        digital_goods: "Digital Goods",
        physical: "Physical Goods/Services"
    },
    help_info: {
        name: "The name of the product",
        external_reference: "The reference for the product that is used by the payment provider. Leave empty unless you're extremely confident you have the correct reference.",
        tax_type: "This is to help with taxing correctly. Physical goods and services are taxed differently from digital goods. And in some countries they have a digital services tax.",
        tax_rate: "The tax rate that is to be used for this product. It will override other tax rates.",
        physical: "Is this product physical?"
    }
}