Feature: Customer Subscription Update Price
  In order to manage a customer's subscriptions payment method
  As an APP user
  I need to be able to update the price a customer is charged

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
      | Product Two | prod_jf9j542       |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 3500   | USD      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |


  Scenario: Update price
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year |
      | <EMAIL> | 0444      | 03           | 25          |
    When I update the subscription "Test Plan" for "<EMAIL>" to use the 3500 in "USD" per "month" price
    Then the subscription "Test Plan" for "<EMAIL>" will be for 3500 in "USD" per "month"

  Scenario: Update price - Stripe Billing
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And stripe billing is disabled
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                 |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year |
      | <EMAIL> | 0444      | 03           | 25          |
    When I update the subscription "Test Plan" for "<EMAIL>" to use the 3500 in "USD" per "month" price
    Then the subscription "Test Plan" for "<EMAIL>" will be for 3500 in "USD" per "month"
