Feature: Customer Read APP
  In order to manage a customer
  As an APP user
  I need to be see what the customer info

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |

  Scenario: Get customer info
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    When I view the customer info via the site for "<EMAIL>"
    Then I will see the "customer" data with the "email" value "<EMAIL>"
    Then I will see the "customer" data with the "billing_type" value "card"
