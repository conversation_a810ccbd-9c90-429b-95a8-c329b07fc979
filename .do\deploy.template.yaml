spec:
  name: billabe<PERSON><PERSON>
  envs:
    - key: APP_ENV
      value: "prod"
    - key: STRIPE_PUBLIC_API_KEY
      scope: RUN_TIME
    - key: STRIPE_PRIVATE_API_KEY
      scope: RUN_TIME
    - key: DATABASE_URL
      scope: RUN_TIME
      value: ${billabeardb.DATABASE_URL}
  services:
    - environment_slug: php
      git:
        branch: main
        repo_clone_url: https://github.com/billabear/billabear.git
      name: billabearweb
      build_command: yarn build
      run_command: heroku-php-apache2 public/
      instance_count: 1
  workers:
    - environment_slug: php
      git:
        branch: main
        repo_clone_url: https://github.com/billabear/billabear.git
      name: scheduler
      run_command: bin/console messenger:consume scheduler_main main
      instance_count: 1
  databases:
    - name: billabeardb
