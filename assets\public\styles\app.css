@tailwind base;
@tailwind components;

@tailwind utilities;

@layer base {
    h1 {
        @apply text-2xl;
    }
    h2 {
        @apply text-xl;
    }
    h3 {
        @apply text-lg;
    }
    a {
        @apply text-blue-600 underline;
    }
}


.StripeElement {
    box-sizing: border-box;
    height: 40px;
    padding: 10px 12px;
    border: 1px solid transparent;
    border-radius: 4px;
    background-color: white;
    box-shadow: 0 1px 3px 0 #d1defc;
    -webkit-transition: box-shadow 150ms ease;
    transition: box-shadow 150ms ease;
}

.StripeElement--focus {
    box-shadow: 0 1px 3px 0 #cfd7df;
}

.StripeElement--invalid {
    border-color: #fa755a;
}

.StripeElement--webkit-autofill {
    background-color: #fefde5 !important;
}

.btn--danger {
    @apply rounded-lg bg-red-500 shadow text-white p-3 font-bold hover:bg-red-400;
}

.btn--main {
    @apply rounded-lg bg-black shadow text-white p-3 font-bold hover:bg-gray-500;
}

.btn-secondary {

    @apply rounded-lg border border-black bg-white shadow text-black p-3 font-bold hover:bg-gray-50;
}