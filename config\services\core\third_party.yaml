services:

  Rollbar\RollbarLogger:
    class: <PERSON><PERSON>\RollbarLogger
    factory: [ '@BillaBear\Logger\Error\RollbarFactory', 'create' ]
    lazy: true # Lazy to avoid errors when it's not enabled

  BillaBear\Logger\Error\RollbarHandler: ~

  Billa<PERSON>ear\Tax\VatSense\VatSenseClient:
    class: BillaBear\Tax\VatSense\VatSenseClient
    factory: [ '@BillaBear\Tax\VatSense\ClientFactory', 'build' ]

  Monolog\Processor\UidProcessor:
    tags:
      - { name: monolog.processor }

  Elastic\Elasticsearch\ClientInterface:
    class: Elastic\Elasticsearch\Client
    factory: ['@BillaBear\Elasticsearch\Factory', 'build']