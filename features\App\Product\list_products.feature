Feature: List Product
  In order to manage products
  As an APP user
  I need to be see all the products

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |

  Scenario: List Products
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow products exist:
      | Name        |
      | Product One |
      | Product Two |
    When I use the APP to list product
    Then I should see in the API response the product "Product Two"
    And I should see in the API response the product "Product One"
