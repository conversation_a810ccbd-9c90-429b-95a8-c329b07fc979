Feature: Customer Enable APP
  In order to fix potential false disable issues
  As an API user
  I need to be able to enable customers

  Scenario: Get customer info
    Given I have authenticated to the API
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
    And customer "<EMAIL>" is disabled
    When I enable the customer info via the API for "<EMAIL>"
    Then the customer "<EMAIL>" is enabled
