{"global": {"loading": "Cargando", "country": {"AU": "Australia", "BE": "Bélgica", "CA": "Canadá", "HR": "Croacia", "CZ": "República Checa", "DK": "Dinamarca", "EE": "Estonia", "FI": "Finlandia", "FR": "Francia", "DE": "Alemania", "GR": "Grecia", "HU": "Hungr<PERSON>", "IS": "Islandia", "LV": "Letonia", "LI": "Liechtenstein", "LT": "Lituania", "LU": "Luxemburgo", "GB": "Reino Unido", "US": "Estados Unidos", "NL": "País<PERSON>", "RO": "Rumanía", "SK": "<PERSON><PERSON><PERSON>aq<PERSON><PERSON>", "SI": "Eslovenia", "ES": "España", "SE": "<PERSON><PERSON>", "AF": "Afganistán", "AL": "Albania", "DZ": "Argelia", "AD": "Andorra", "AO": "Angola", "AI": "<PERSON><PERSON><PERSON>", "AQ": "An<PERSON><PERSON><PERSON><PERSON>", "AG": "Antigua y Barbuda", "AR": "Argentina", "AM": "Armenia", "AW": "Aruba", "AT": "Austria", "AZ": "Azerbaiyán", "BS": "Bahamas", "BH": "<PERSON><PERSON><PERSON><PERSON>", "BD": "Bangladesh", "BB": "Barbados", "BY": "Bielorrusia", "BZ": "Belice", "BJ": "<PERSON><PERSON>", "BM": "Bermudas", "BT": "<PERSON><PERSON>", "BO": "Bolivia", "BA": "Bosnia y Herzegovina", "BW": "<PERSON><PERSON><PERSON>", "BR": "Brasil", "IO": "Territorio Británico del Océano Índico", "BN": "Brunei Darussalam", "BG": "Bulgaria", "BF": "Burkina Faso", "BI": "Burundi", "CV": "Cabo Verde", "KH": "Camboya", "CM": "<PERSON><PERSON><PERSON>", "KY": "<PERSON><PERSON>", "CF": "República Centroafricana", "TD": "Chad", "CL": "Chile", "CN": "China", "CX": "<PERSON><PERSON>", "CC": "<PERSON><PERSON> (Keeling)", "CO": "Colombia", "KM": "Comoras", "CG": "Congo", "CD": "Congo, República Democrática del", "CK": "<PERSON><PERSON>", "CR": "Costa Rica", "CI": "Costa de Marfil", "CU": "Cuba", "CY": "<PERSON><PERSON>", "DJ": "<PERSON><PERSON><PERSON>", "DM": "Dominica", "DO": "República Dominicana", "EC": "Ecuador", "EG": "Egipto", "SV": "El Salvador", "GQ": "Guinea Ecuatorial", "ER": "Eritrea", "SZ": "<PERSON><PERSON><PERSON><PERSON>", "ET": "Etiopía", "FK": "<PERSON><PERSON> (Falkland Islands)", "FO": "<PERSON><PERSON>", "FJ": "<PERSON><PERSON>", "GF": "Guayana Francesa", "PF": "Polinesia <PERSON>", "GA": "Gabón", "GM": "Gambia", "GE": "Georgia", "GH": "Ghana", "GI": "Gibraltar", "GL": "Groenlandia", "GD": "Granada", "GP": "Guadalupe", "GT": "Guatemala", "GG": "Guernsey", "GN": "Guinea", "GW": "Guinea-Bissau", "GY": "Guyana", "HT": "Haití", "HN": "Honduras", "HK": "Hong Kong", "IN": "India", "ID": "Indonesia", "IR": "<PERSON><PERSON><PERSON>, República Islámica del", "IQ": "Iraq", "IE": "Irlanda", "IM": "<PERSON><PERSON>", "IL": "Israel", "IT": "Italia", "JM": "Jamaica", "JP": "Japón", "JE": "Jersey", "JO": "Jordan", "KZ": "Kazajstán", "KE": "Ken<PERSON>", "KI": "Kiribati", "KP": "Corea, República Popular Democrática de", "KR": "Corea, República de", "KW": "Kuwait", "KG": "Kirguistán", "LA": "República Democrática Popular Lao", "LB": "Líbano", "LS": "Lesotho", "LR": "Liberia", "LY": "Libia", "MO": "Macao", "MG": "Madagascar", "MW": "Malawi", "MY": "Malasia", "MV": "<PERSON><PERSON><PERSON>", "ML": "Mali", "MT": "Malta", "MH": "<PERSON><PERSON>", "MQ": "Martinica", "MR": "Mauritania", "MU": "<PERSON><PERSON><PERSON>", "YT": "Mayotte", "MX": "México", "FM": "Micronesia, Estados Federados de", "MD": "Moldavia, República de", "MC": "Mónaco", "MN": "Mongolia", "ME": "Montenegro", "MS": "Montserrat", "MA": "Marrue<PERSON>", "MZ": "Mozambique", "MM": "Myanmar", "NA": "Namibia", "NR": "Nauru", "NP": "Nepal", "NC": "Nueva Caledonia", "NZ": "Nueva Zelanda", "NI": "Nicaragua", "NE": "<PERSON><PERSON><PERSON>", "NG": "Nigeria", "NU": "Niue", "NF": "<PERSON><PERSON> de <PERSON>", "MK": "Macedonia del Norte", "NO": "<PERSON><PERSON><PERSON>", "OM": "Omán", "PK": "Pakistán", "PW": "<PERSON><PERSON>", "PS": "Palestina, Estado de", "PA": "Panamá", "PG": "Papúa Nueva Guinea", "PY": "Paraguay", "PE": "Perú", "PH": "Filipinas", "PN": "Pitcairn", "PL": "Polonia", "PT": "Portugal", "QA": "Qatar", "RE": "Reunión", "RU": "Federación de Rusia", "RW": "<PERSON><PERSON><PERSON>", "BL": "San Bartolomé", "SH": "Santa Elena, Ascensión y Tristán da Cunha", "KN": "San Cristóbal y Nieves", "LC": "Santa Lucía", "MF": "<PERSON> (parte francesa)", "PM": "San Pedro y Miquelón", "VC": "San Vicente y las Granadinas", "WS": "Samoa", "SM": "San Marino", "ST": "Santo <PERSON> y Príncipe", "SA": "Arabia Saudí", "SN": "Senegal", "RS": "Serbia", "SC": "Seychelles", "SL": "Sierra Leona", "SG": "Singapur", "SX": "<PERSON> (parte neerlandesa)", "SB": "<PERSON><PERSON>", "SO": "Somalia", "ZA": "Sudáfrica", "GS": "Georgia del Sur y las islas Sandwich del Sur", "SS": "Sudán del Sur", "LK": "Sri Lanka", "SD": "Sudán", "SR": "Surinam", "SJ": "Svalbard <PERSON> <PERSON>", "CH": "<PERSON><PERSON>", "SY": "República Árabe Siria", "TW": "<PERSON><PERSON><PERSON>, provincia de China", "TJ": "Tayikistán", "TZ": "Tanzania, República Unida de", "TH": "Tailandia", "TL": "Timor Oriental", "TG": "Togo", "TK": "Tokelau", "TO": "Tonga", "TT": "Trinidad y Tobago", "TN": "<PERSON><PERSON><PERSON>", "TR": "Turquía", "TM": "Turkmenistán", "TC": "Islas Turcas y Caicos", "TV": "Tuvalu", "UG": "Uganda", "UA": "Ucrania", "AE": "Emiratos Árabes Unidos", "UM": "Islas periféricas menores de Estados Unidos", "UY": "Uruguay", "UZ": "Uzbekistán", "VU": "Vanuatu", "VE": "Venezuela", "VN": "Vietnam", "VG": "<PERSON><PERSON>", "VI": "<PERSON><PERSON>, EE.UU.", "WF": "<PERSON> y <PERSON>", "EH": "Sáhara Occidental", "YE": "Yemen", "ZM": "Zambia", "ZW": "Zimbabue"}, "select_country": "Seleccionar país"}, "public": {"login": {"title": "Inicio de sesión", "email": "Correo electrónico", "password": "Contraseña", "login_button": "Inicio de sesión", "remember_me_label": "Acuérdate de m<PERSON>", "forgot_password_link": "¿Ha olvidado su contraseña?", "signup_link": "Regístrese para obtener una cuenta", "logging_in": "Inicio de sesión"}, "signup": {"title": "Inscribirse", "email": "Correo electrónico", "email_error": "Debe facilitarse la dirección de correo electrónico", "email_invalid_error": "Debe facilitarse una dirección de correo electrónico válida", "password": "Contraseña", "password_error": "Contraseña obligatoria", "password_confirm": "Confirmar con<PERSON>", "password_confirm_error": "La contraseña debe coincidir", "signup_button": "Inscribirse", "signing_up": "En curso", "remember_me_label": "Acuérdate de m<PERSON>", "forgot_password_link": "¿Ha olvidado su contraseña?", "login_link": "¿Ya tienes una cuenta? Conéctese ahora.", "success_message": "Se ha registrado correctamente. Compruebe su correo electrónico."}, "forgot_password": {"title": "Restablecer contraseña", "email": "Correo electrónico", "email_error": "Debe facilitarse una dirección de correo electrónico.", "in_progress": "En curso", "login_link": "¿Recuerdas tu contraseña? Conectarse", "success_message": "Compruebe su correo electrónico", "request_button": "Restablecer contraseña"}, "forgot_password_confirm": {"title": "Restablecer contraseña", "password": "Contraseña", "password_error": "Debe facilitarse una contraseña.", "password_length_error": "La contraseña debe tener al menos 7 caracteres", "password_confirm": "Confirme", "password_confirm_error": "Las contraseñas deben coincidir", "reset_button": "Restablecer contraseña", "in_progress": "En curso", "login_link": "Haga clic aquí para iniciar sesión.", "success_message": "Su contraseña ha sido restablecida. Ya puede iniciar sesión.", "request_button": "Restablecer contraseña"}, "confirm_email": {"error_message": "Este enlace no es válido", "success_message": "Tu correo electrónico está confirmado y puedes iniciar sesión.", "login_link": "Haga clic aquí para iniciar sesión."}}, "app": {"menu": {"main": {"reports": "Informes", "subscriptions": "Suscripciones", "finance": "Finanzas", "settings": "<PERSON><PERSON><PERSON><PERSON>", "customers": "Clientes", "products": "Productos", "invoices": "Facturas", "system": "Sistema", "docs": "Documentación", "workflows": "Flujos de trabajo", "developers": "Desarrolladores", "home": "<PERSON><PERSON>o", "customer_list": "Lista de clientes", "mobile": {"show": "Mostrar menú", "hide": "Ocultar menú"}, "tax": "Impuesto", "customer_support_integrations": "Apoyo a las integraciones"}}, "team": {"main": {"title": "Configuración del equipo", "add_team_member": "<PERSON>ñadir <PERSON> del equipo"}, "invite": {"title": "<PERSON>ñadir <PERSON> del equipo", "close": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "invite_successfully_sent": "La invitación se ha enviado correctamente.", "send": "Enviar invitación", "sending": "Envío de", "send_another": "Enviar otro"}, "pending_invites": {"title": "Invitaciones pendientes", "none": "No hay invitaciones pendientes", "email": "Correo electrónico", "invited_at": "Invitado a", "cancel": "<PERSON><PERSON><PERSON>", "cancelling": "Cancelación"}, "members": {"email": "Correo electrónico", "created_at": "Inscrito en", "disable": "Desactivar", "disabling": "En curso", "active": "Activo", "disabled": "Desactivar"}}, "plan": {"main": {"title": "Plan", "payment_schedule_yearly": "Anualmente", "payment_schedule_monthly": "Mensualmente", "payment_schedule_label": "Calendario de pagos", "select_plan": "Seleccionar plan", "selected_plan": "Actualmente activo", "change": "Modificación de este plan", "payment_settings": "Opciones de pago", "cancel_button": "<PERSON><PERSON><PERSON>", "in_progress": "Tratamiento", "features": "Características", "your_current_plan": "Su plan actual", "plan_options": "Opciones del plan"}}, "user": {"settings": {"title": "Configuración del usuario", "name": "Nombre", "email": "Correo electrónico", "password": "Contraseña", "locale": "Local", "save": "Guardar", "error_message": "Se ha producido un problema al guardar la configuración del usuario. Por favor, compruebe los errores.", "success_message": "Se ha guardado correctamente la configuración.", "danger_zone": "Zona de peligro", "current_password": "Contraseña actual", "new_password": "Nueva contraseña", "new_password_again": "Confirmar con<PERSON>", "change_password": "Cambiar contraseña", "need_current_password": "Debe proporcionar su contraseña actual", "need_new_password": "Necesidad de proporcionar una nueva contraseña", "need_valid_password": "La contraseña debe tener más de 8 caracteres", "need_password_to_match": "Las contraseñas deben coincidir", "in_progress": "En curso"}, "invite": {"title": "Invitar usuario", "email": "Correo electrónico", "send": "Enviar", "in_progress": "En curso", "success_message": "Invitación enviada con éxito", "need_email": "Es necesario proporcionar un correo electrónico", "error_message": "No se puede enviar la invitación.", "role": "Papel"}}, "billing": {"details": {"title": "Datos de facturación", "street_line_one": "Calle Línea Uno", "street_line_two": "Calle Línea Dos", "city": "Ciudad", "region": "Estado", "country": "<PERSON><PERSON>", "postal_code": "Código postal", "submit": "Guardar"}, "main": {"title": "Facturación", "details": "Datos de facturación", "methods": "Formas de pago", "invoices": "Facturas"}, "card_form": {"name": "Nombre", "number": "Número de tarjeta", "exp_month": "Mes de expiración", "exp_year": "Año de expiración", "cvc": "Código de seguridad", "add_card": "<PERSON><PERSON><PERSON>"}, "payment_methods": {"title": "Formas de pago", "card_number": "Número", "card_expiry": "Fecha de caducidad de la tarjeta", "is_default": "Forma de pago por defecto", "make_default_btn": "Por defecto", "delete_btn": "Bo<PERSON>r", "add_card_btn": "<PERSON><PERSON>dir nueva tarjeta", "no_saved_payment_methods": "No guardar métodos de pago"}}, "customer": {"list": {"title": "Clientes", "email": "Correo electrónico", "country": "<PERSON><PERSON>", "reference": "Referencia", "no_customers": "Actualmente no hay clientes", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view_btn": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "no_filters": "Sin filtros", "country": "<PERSON><PERSON>", "company_name": "Nombre de la empresa"}, "loading": "Carga de resultados", "error_message": "Se ha producido un error", "company_name": "Nombre de la empresa"}, "create": {"title": "Crear un nuevo cliente", "email": "Correo electrónico", "street_line_one": "Calle Línea 1", "street_line_two": "Calle Línea 2", "city": "Ciudad", "region": "Región", "country": "<PERSON><PERSON>", "post_code": "Código postal", "reference": "Referencia", "external_reference": "Referencia externa", "advance": "avance", "submit_btn": "Crear cliente", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Cliente creado con éxito", "address_title": "Dirección", "locale": "Local", "billing_type": "Tipo de facturación", "billing_type_card": "Tarjeta", "billing_type_invoice": "Factura", "company_name": "Nombre de la empresa", "brand": "<PERSON><PERSON>", "tax_number": "Número de identificación fiscal", "standard_tax_rate": "Tipo impositivo normal", "type": "Tipo de cliente", "type_business": "Empresas", "type_individual": "Individual", "help_info": {"email": "El correo electrónico del cliente al que deben enviarse las facturas", "locale": "La configuración regional que se utilizará para el idioma", "company": "El nombre de la empresa", "street_line_one": "La primera línea de la dirección de facturación", "street_line_two": "La segunda línea de la dirección de facturación", "city": "Ciudad de la dirección de facturación", "region": "Región/estado de la dirección de facturación", "country": "El país de facturación del cliente - código de país ISO 3166-1 alfa-2.", "post_code": "El código postal de la dirección de facturación", "reference": "Su referencia interna para el cliente", "billing_type": "Cómo se facturará al cliente. Tarjeta significa que los pagos serán automáticos a través de una tarjeta registrada. Factura significa que reciben una factura y pagan manualmente", "external_reference": "La referencia del cliente que utiliza el proveedor de pagos. Déjelo vacío a menos que esté extremadamente seguro de que tiene la referencia correcta.", "brand": "La marca a la que pertenecerá el cliente.", "tax_number": "El número de identificación fiscal del cliente", "standard_tax_rate": "El tipo impositivo que debe aplicarse al cliente para todo excepto los servicios digitales", "type": "Si el cliente es una empresa o un particular", "invoice_format": "El formato que debe utilizarse al crear y entregar una factura"}, "failed_message": "No se ha podido crear el cliente", "invoice_format": "Formato de factura", "metadata": {"title": "Metadatos", "name": "Nombre", "value": "Valor", "no_values": "Sin valores de metadatos", "add": "<PERSON><PERSON><PERSON>"}}, "view": {"title": "Ver detalles del cliente", "update": "Actualización", "disable": "Desactivar", "enable": "Activar", "error": {"not_found": "No se ha encontrado ningún cliente", "unknown": "Se ha producido un error desconocido"}, "main": {"title": "Detalles principales", "email": "Correo electrónico", "reference": "Referencia interna", "external_reference": "Referencia externa", "status": "Estado", "locale": "Local", "brand": "<PERSON><PERSON>", "billing_type": "Tipo de facturación", "tax_number": "Número de identificación fiscal", "standard_tax_rate": "Tipo impositivo normal", "type": "Tipo", "marketing_opt_in": "Marketing Opt In"}, "address": {"company_name": "Nombre de la empresa", "title": "Dirección", "street_line_one": "Calle Línea 1", "street_line_two": "Calle Línea 2", "city": "Ciudad", "region": "Región", "post_code": "Código postal", "country": "<PERSON><PERSON>"}, "credit_notes": {"title": "Notas de crédito", "list": {"amount": "Importe", "currency": "Moneda", "created_by": "<PERSON><PERSON>o por", "created_at": "Creado en"}, "no_credit_notes": "No hay notas de crédito para este cliente"}, "credit": {"title": "Ajustes de crédito", "list": {"amount": "Importe", "currency": "Moneda", "created_by": "<PERSON><PERSON>o por", "created_at": "Creado en"}, "no_credit": "No hay crédito para este cliente", "add_button": "<PERSON><PERSON><PERSON>"}, "subscriptions": {"title": "Suscripciones", "list": {"plan_name": "Plan", "status": "Estado", "schedule": "<PERSON><PERSON><PERSON>", "created_at": "Creado en", "valid_until": "Siguiente facturado", "view": "<PERSON>er"}, "add_new": "Añadir nueva suscripción", "no_subscriptions": "Sin suscripciones"}, "subscription_events": {"title": "Eventos de suscripción", "list": {"event": "Evento", "subscription": "Suscripción", "created_at": "Creado en"}, "no_subscription_events": "No hay eventos de suscripción"}, "payments": {"title": "Pagos", "list": {"amount": "Importe", "currency": "Moneda", "status": "Estado", "created_at": "Creado en"}, "no_payments": "Aún no hay pagos para este cliente"}, "refunds": {"title": "Reembolsos", "list": {"amount": "Importe", "currency": "Moneda", "created_by": "<PERSON><PERSON>o por", "created_at": "Creado en"}, "no_refunds": "No hay devoluciones para este cliente"}, "payment_details": {"title": "Datos de pago", "list": {"brand": "<PERSON><PERSON>", "last_four": "Cuatro últimos", "default": "Pago por defecto", "expiry_month": "Mes de expiración", "expiry_year": "Año de expiración", "name": "Nombre"}, "add_token": "<PERSON>", "add_new": "<PERSON><PERSON><PERSON>ue<PERSON>", "no_payment_details": "No hay datos de pago", "delete": "Bo<PERSON>r", "make_default": "Por defecto"}, "limits": {"title": "Límites", "list": {"feature": "Característica", "limit": "Límite"}, "no_limits": "Sin límites"}, "features": {"title": "Características", "list": {"feature": "Característica"}, "no_features": "Sin funciones"}, "invoices": {"title": "Facturas", "list": {"amount": "Importe", "currency": "Moneda", "status": "Estado", "outstanding": "Destacado", "overdue": "<PERSON><PERSON><PERSON>", "paid": "<PERSON><PERSON>", "created_at": "Creado en", "view_btn": "<PERSON>er"}, "no_invoices": "Sin facturas", "next": "Siguient<PERSON>", "prev": "Anterior"}, "invoice_delivery": {"title": "Entrega de facturas", "add_new": "<PERSON><PERSON><PERSON>ue<PERSON>", "list": {"method": "<PERSON><PERSON><PERSON><PERSON>", "format": "Formato", "detail": "Detalle", "view": "<PERSON>er"}, "no_delivery_methods": "Sin métodos de entrega"}, "metric_counters": {"title": "Contadores métricos", "list": {"name": "Nombre", "usage": "Utilización", "cost": "Coste estimado"}, "no_counters": "No hay contadores métricos"}, "usage_limits": {"title": "Límites de uso", "add_new": "<PERSON><PERSON><PERSON>ue<PERSON>", "list": {"amount": "Importe", "warn_level": "Acción"}, "warn_levels": {"warn": "<PERSON><PERSON>", "disable": "Desactivar"}, "no_limits": "No hay límites de uso para este cliente"}, "metadata": {"title": "Metadatos", "no_metadata": "Sin metadatos"}, "audit_log": "Registro de auditoría"}, "update": {"title": "Actualizar cliente", "email": "Correo electrónico", "street_line_one": "Calle Línea 1", "street_line_two": "Calle Línea 2", "city": "Ciudad", "region": "Región", "country": "<PERSON><PERSON>", "post_code": "Código postal", "reference": "Referencia", "company_name": "Nombre de la empresa", "external_reference": "Referencia externa", "advance": "avance", "submit_btn": "Actualizado", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Actualizar correctamente al cliente", "address_title": "Dirección", "tax_number": "Número de identificación fiscal", "standard_tax_rate": "Tipo impositivo normal", "locale": "Local", "error": {"not_found": "No se ha encontrado ningún cliente", "unknown": "Se ha producido un error desconocido"}, "billing_type": "Tipo de facturación", "billing_type_card": "Tarjeta", "billing_type_invoice": "Factura", "type": "Tipo de cliente", "type_business": "Empresas", "type_individual": "Individual", "help_info": {"email": "El correo electrónico del cliente al que deben enviarse las facturas", "locale": "La configuración regional que se utilizará para el idioma", "company_name": "El nombre de la empresa", "street_line_one": "La primera línea de la dirección de facturación", "street_line_two": "La segunda línea de la dirección de facturación", "city": "Ciudad de la dirección de facturación", "region": "Región/estado de la dirección de facturación", "country": "El país de facturación del cliente - código de país ISO 3166-1 alfa-2.", "post_code": "El código postal de la dirección de facturación", "reference": "Su referencia interna para el cliente", "billing_type": "Cómo se facturará al cliente. Tarjeta significa que los pagos serán automáticos a través de una tarjeta registrada. Factura significa que reciben una factura y pagan manualmente", "external_reference": "La referencia del cliente que utiliza el proveedor de pagos. Déjelo vacío a menos que esté extremadamente seguro de que tiene la referencia correcta.", "tax_number": "El número de identificación fiscal del cliente", "standard_tax_rate": "El tipo impositivo que debe aplicarse al cliente para todo excepto los servicios digitales", "type": "Si el cliente es una empresa o un particular", "invoice_format": "El formato que debe utilizarse al crear y entregar una factura", "marketing_opt_in": "Si el cliente ha optado por recibir correos electrónicos de marketing. Esto afecta a las integraciones de boletines."}, "invoice_format": "Formato de factura", "marketing_opt_in": "Marketing Opt In", "metadata": {"title": "Metadatos", "name": "Nombre", "value": "Valor", "no_values": "Sin valores de metadatos", "add": "<PERSON><PERSON><PERSON>"}}, "menu": {"title": "Clientes", "customers": "Clientes"}}, "product": {"list": {"title": "Productos", "name": "Nombre", "physical": "Físico", "no_products": "Actualmente no existen productos", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "filter": {"title": "<PERSON><PERSON><PERSON>", "name": "Nombre", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Se ha producido un error"}, "create": {"title": "Crear un nuevo producto", "name": "Nombre", "external_reference": "Referencia externa", "advance": "avance", "submit_btn": "Crear producto", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Producto creado con éxito", "failed_message": "No se ha podido crear el producto", "tax_rate": "Tipo impositivo", "tax_type": "Tipo de impuesto", "physical": "Físico", "tax_types": {"digital_services": "Servicios digitales", "digital_goods": "Productos digitales", "physical": "Bienes y servicios físicos"}, "help_info": {"name": "El nombre del producto", "external_reference": "La referencia del producto que utiliza el proveedor de pagos. Déjelo vacío a menos que esté extremadamente seguro de que tiene la referencia correcta.", "tax_type": "Esto es para ayudar a gravar correctamente. Los bienes y servicios físicos se gravan de forma diferente a los digitales. Y en algunos países existe un impuesto sobre los servicios digitales.", "tax_rate": "El tipo impositivo que se utilizará para este producto. Anulará otros tipos impositivos.", "physical": "¿Este producto es físico?"}}, "view": {"title": "Ver detalles del producto", "update": "Actualización", "error": {"not_found": "No se ha encontrado el producto", "unknown": "Se ha producido un error desconocido"}, "main": {"title": "Detalles principales", "name": "Nombre", "physical": "Físico", "external_reference": "Referencia externa", "tax_rate": "Tipo impositivo", "tax_type": "Tipo de impuesto", "tax_types": {"digital_services": "Servicios digitales", "digital_goods": "Productos digitales", "physical": "Bienes y servicios físicos"}}, "price": {"title": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON>r nuevo precio", "no_prices": "Actualmente no hay precios", "hide": "Hacer privado el precio", "show": "<PERSON>cer p<PERSON> el precio", "list": {"amount": "Importe", "currency": "Moneda", "recurring": "<PERSON><PERSON> perió<PERSON>", "schedule": "Calendario de pagos", "including_tax": "El precio incluye impuestos", "public": "<PERSON><PERSON>", "external_reference": "Referencia externa", "usage": "Utilización"}}, "subscription_plan": {"title": "Planes de suscripción", "create": "Crear un nuevo Plan", "no_subscription_plans": "Actualmente no hay planes de suscripción", "view": "<PERSON>er", "list": {"name": "Nombre", "external_reference": "Referencia externa", "code_name": "Código"}}}, "update": {"title": "Actualizar producto", "name": "Nombre", "external_reference": "Referencia externa", "advance": "avance", "submit_btn": "Actualizado", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Actualizar correctamente el producto", "address_title": "Dirección", "error": {"not_found": "No se ha encontrado el producto", "unknown": "Se ha producido un error desconocido"}, "tax_type": "Tipo de impuesto", "tax_types": {"digital_services": "Servicios digitales", "digital_goods": "Productos digitales", "physical": "Bienes y servicios físicos"}, "tax_rate": "Tipo impositivo", "help_info": {"name": "El nombre del producto", "external_reference": "La referencia del producto que utiliza el proveedor de pagos. Déjelo vacío a menos que esté extremadamente seguro de que tiene la referencia correcta.", "tax_type": "Esto es para ayudar a gravar correctamente. Los bienes y servicios físicos se gravan de forma diferente a los digitales. Y en algunos países existe un impuesto sobre los servicios digitales.", "tax_rate": "El tipo impositivo que se utilizará para este producto. Anulará otros tipos impositivos."}}, "menu": {"title": "Producto", "products": "Productos", "features": "Características", "vouchers": "Vales", "products_list": "Lista de productos", "metrics": "Métricas"}}, "price": {"create": {"title": "<PERSON><PERSON>r nuevo precio", "amount": "Importe", "external_reference": "Referencia externa", "advance": "avance", "submit_btn": "<PERSON><PERSON><PERSON> precio", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "<PERSON>cio creado con éxito", "schedule_label": "Calendario de pagos", "currency": "Moneda", "recurring": "¿Es recurrente?", "including_tax": "¿El precio incluye impuestos?", "public": "Público", "help_info": {"amount": "El precio es la moneda de nivel menor. Así, 1,00 USD sería 100 y 9,99 sería 999.", "display_amount": "Este precio sería de {amount}.", "external_reference": "La referencia del producto que utiliza el proveedor de pagos. Déjelo vacío a menos que esté extremadamente seguro de que tiene la referencia correcta.", "recurring": "Si se trata de un pago periódico o puntual.", "currency": "La divisa en la que se cobrará al cliente", "schedule": "Con qué frecuencia se debe cobrar al cliente", "including_tax": "Si desea ocultar el impuesto en el precio o si desea que el cliente pague el impuesto por sí mismo", "public": "Si se trata de un precio público", "usage": "Si se factura al cliente en función de su uso de una métrica o por asiento.", "metric_type": "Si la métrica de uso se restablece al final del calendario de pagos y se utiliza en su totalidad para la facturación o de forma continua y se utiliza la diferencia entre la última factura y la siguiente."}, "schedule": {"week": "<PERSON><PERSON><PERSON>", "month": "Mensualmente", "year": "Anualmente"}, "metric": "M<PERSON><PERSON><PERSON>", "metric_type": "<PERSON><PERSON><PERSON> m<PERSON>", "create_metric": "Debe crear una Métrica", "metric_types": {"resettable": "Reiniciable", "continuous": "Continuo"}, "type": "Tipo", "types": {"fixed_price": "Precio fijo", "package": "<PERSON><PERSON><PERSON>", "per_unit": "Por unidad/por asiento", "tiered_volume": "Volumen escalonado", "tiered_graduated": "Graduado por niveles"}, "usage": "Utilización", "units": "Unidades", "tiers": "<PERSON><PERSON><PERSON>", "tiers_fields": {"first_unit": "Primera unidad", "last_unit": "Última unidad", "unit_price": "Precio unitario", "flat_fee": "Ta<PERSON>fa plana"}}}, "feature": {"list": {"title": "Características", "name": "Nombre", "code": "Código", "reference": "Referencia", "no_features": "Actualmente no existen funciones", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Se ha producido un error", "loading": "Características de carga"}, "create": {"title": "Crear nueva función", "advance": "avance", "submit_btn": "<PERSON><PERSON>r fun<PERSON>", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Función creada con éxito", "address_title": "Dirección", "fields": {"name": "Nombre", "code": "Nombre en clave", "description": "Descripción"}, "help_info": {"name": "Nombre de la función", "code": "El nombre en clave de la función. Se utiliza al registrar un uso o al comprobar los límites.", "description": "Descripción de la función"}}}, "subscription_plan": {"create": {"title": "Crear un nuevo plan de suscripción", "main_section": {"title": "Detalles principales", "fields": {"name": "Nombre", "code_name": "Código", "user_count": "Recuento de usuarios", "public": "Plan públicamente disponible", "per_seat": "Por asiento", "free": "<PERSON><PERSON><PERSON>"}, "help_info": {"name": "Nombre del plan de suscripción", "code_name": "Nombre en clave del plan que se utilizará con la API.", "user_count": "El número de usuarios permitidos para este plan", "public": "¿El plan está a disposición del público o es un plan personalizado?", "free": "¿Es un plan gratuito?", "per_seat": "¿El plan se cobra por asiento?"}}, "trial_section": {"title": "Detalles del juicio", "fields": {"has_trial": "Tiene juicio", "is_trial_standalone": "¿Es el juicio independiente?", "trial_length_days": "Duración del juicio en días"}, "help_info": {"has_trial": "Si el plan tiene un periodo de prueba por defecto", "trial_length_days": "Duración de la prueba en días", "is_trial_standalone": "Si una prueba es independiente, no necesita un precio y la suscripción se interrumpe al final de la prueba"}}, "features_section": {"title": "Características", "columns": {"feature": "Característica", "description": "Descripción"}, "create": {"name": "Nombre", "code_name": "Código", "description": "Descripción", "button": "<PERSON><PERSON>"}, "add_feature": "<PERSON><PERSON><PERSON>", "existing": "Características existentes", "new": "<PERSON><PERSON><PERSON> nuevo", "no_features": "Sin funciones"}, "limits_section": {"title": "Límites", "columns": {"limit": "Límite", "feature": "Característica", "description": "Descripción"}, "fields": {"limit": "Límite", "feature": "Característica"}, "add_limit": "<PERSON><PERSON><PERSON>", "no_limits": "Sin límites"}, "prices_section": {"title": "<PERSON><PERSON><PERSON>", "columns": {"amount": "Importe", "currency": "Moneda", "schedule": "<PERSON><PERSON><PERSON>"}, "create": {"amount": "Importe", "currency": "Moneda", "recurring": "<PERSON><PERSON><PERSON>", "schedule": "<PERSON><PERSON><PERSON>", "including_tax": "Impuestos incluidos", "public": "Público", "button": "<PERSON><PERSON>"}, "add_price": "<PERSON><PERSON><PERSON>", "existing": "Precios existentes", "new": "<PERSON><PERSON><PERSON> nuevo", "no_prices": "Sin precios"}, "submit_btn": "Crear plan"}, "view": {"title": "Ver detalles del plan de suscripción", "update": "Actualización", "error": {"not_found": "No se ha encontrado ningún plan de suscripción", "unknown": "Se ha producido un error desconocido"}, "main": {"title": "Detalles principales", "name": "Nombre", "code_name": "Código", "per_seat": "Por asiento", "free": "<PERSON><PERSON><PERSON>", "user_count": "Recuento de usuarios", "public": "Disponible públicamente", "has_trial": "Tiene Juicio", "trial_length_days": "Duración del juicio", "is_trial_standalone": "¿El juicio es independiente?"}, "limits": {"title": "Límites", "list": {"feature": "Característica", "limit": "Límite", "no_limits": "Sin límites"}}, "features": {"title": "Características", "list": {"feature": "Característica", "no_features": "Sin funciones"}}, "price": {"title": "<PERSON><PERSON><PERSON>", "list": {"amount": "Importe", "currency": "Moneda", "recurring": "<PERSON><PERSON> perió<PERSON>", "schedule": "Calendario de pagos", "including_tax": "El precio incluye impuestos", "public": "<PERSON><PERSON>", "external_reference": "Referencia externa", "usage": "Utilización"}}}, "update": {"title": "Actualizar el plan de suscripción", "advance": "avance", "submit_btn": "Actualizar el plan de suscripción", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Plan de suscripción actualizado con éxito", "address_title": "Dirección", "fields": {"name": "Nombre", "code_name": "Código", "user_count": "Recuento de usuarios", "public": "Plan públicamente disponible", "per_seat": "Por asiento", "free": "<PERSON><PERSON><PERSON>", "prices": "<PERSON><PERSON><PERSON>", "features": "Características", "limits": "Límites", "has_trial": "Tiene Juicio", "trial_length_days": "Duración del juicio", "is_trial_standalone": "¿El juicio es independiente?"}, "help_info": {"name": "El nombre del plan", "code_name": "Nombre en clave del plan que se utilizará con la API.", "user_count": "El número de usuarios permitidos para este plan", "public": "¿El plan está a disposición del público o es un plan personalizado?", "free": "¿Es un plan gratuito?", "per_seat": "¿El plan se cobra por asiento?", "has_trial": "Si el plan tiene un periodo de prueba por defecto", "trial_length_days": "Duración de la prueba en días", "is_trial_standalone": "Si una prueba es independiente, no necesita un precio y la suscripción se interrumpe al final de la prueba"}, "features": {"title": "Características", "add_feature": "Añadir función"}, "limits": {"title": "Límites", "add_limit": "<PERSON><PERSON><PERSON>"}, "prices": {"title": "<PERSON><PERSON><PERSON>", "add_price": "<PERSON><PERSON><PERSON>"}}, "menu": {"subscription_plans": "Planes de suscripción", "products": "Productos", "features": "Características"}}, "payment_details": {"add": {"title": "<PERSON><PERSON><PERSON> de pago"}, "add_with_token": {"title": "<PERSON><PERSON><PERSON> de<PERSON> de pago con token", "field": {"token": "<PERSON><PERSON>"}, "help_info": {"token": "El token proporcionado por Stripe."}, "submit": "Enviar"}}, "subscription": {"create": {"title": "Crear nueva suscripción", "subscription_plans": "Planes de suscripción", "payment_details": "Datos de pago", "no_eligible_prices": "No hay precios subvencionables", "prices": "<PERSON><PERSON><PERSON>", "success_message": "Suscripción creada con éxito", "submit_btn": "<PERSON><PERSON>", "trial": "<PERSON><PERSON><PERSON> gratuita", "trial_length_days": "Número de días", "unknown_error": "Se ha producido un error desconocido durante la creación", "seats": "Número de plazas", "help_info": {"eligible_prices": "Cuando un cliente ya tiene una suscripción activa, las nuevas suscripciones deben ser para el mismo periodo de facturación y moneda.", "trial": "Cuando un cliente ya tiene una suscripción activa, no puede optar a otra prueba gratuita.", "no_trial": "Este plan no tiene prueba gratuita", "seats": "El número de plazas para las que debe abonarse"}}, "view": {"title": "Ver suscripción", "main": {"title": "Datos de suscripción", "status": "Estado", "plan": "Plan", "plan_change": "Plan de cambio", "customer": "Cliente", "main_external_reference": "Referencia externa principal", "created_at": "Creado en", "ended_at": "Terminado en", "valid_until": "<PERSON><PERSON><PERSON><PERSON> hasta", "seat_number": "Número de asiento", "change_seat": "Cambiar de asiento"}, "pricing": {"title": "<PERSON><PERSON><PERSON>", "price": "Precio", "recurring": "<PERSON><PERSON><PERSON>", "schedule": "<PERSON><PERSON><PERSON>", "change": "Cambia", "no_price": "No hay precio establecido para la suscripción"}, "payments": {"title": "Pagos", "amount": "Importe", "created_at": "Creado en", "view": "<PERSON>er", "no_payments": "Aún no hay pagos"}, "payment_method": {"title": "Forma de pago", "last_four": "Cuatro últimos", "expiry_month": "Mes de expiración", "expiry_year": "Año de expiración", "brand": "Tipo de tarjeta", "invoiced": "Facturado"}, "subscription_events": {"title": "Eventos de suscripción", "list": {"event": "Evento", "subscription": "Suscripción", "created_at": "Creado en"}, "no_subscription_events": "No hay eventos de suscripción"}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "payment_method": "Actualizar datos de pago", "audit_log": "Registro de auditoría"}, "modal": {"seats": {"seats": "Asientos", "seats_help": "El número de plazas del plan", "submit": "Guardar"}, "price": {"price": "Precio nuevo", "price_help": "El nuevo precio que se cobrará en la próxima factura", "submit": "Actualización"}, "plan": {"plan": "Nuevo plan", "plan_help": "El plan al que desea cambiar esta suscripción", "price": "Precio nuevo", "price_help": "El nuevo precio que se cobrará en la próxima factura", "submit": "Actualización", "when": {"title": "En", "next_cycle": "Utilizar para el próximo ciclo de facturación", "instantly": "Instantáneamente", "specific_date": "Fecha específica"}}, "payment_method": {"payment_method": "Utilizar da<PERSON> de pago", "payment_method_help": "Estos datos se utilizarán la próxima vez que cobremos al cliente.", "update_button": "Actualizar datos de pago", "submit": "Actualización"}, "cancel": {"title": "Cancelar suscripción", "cancel_btn": "Confirme", "close_btn": "<PERSON><PERSON><PERSON>", "when": {"title": "En", "end_of_run": "Fin del periodo de facturación actual", "instantly": "Instantáneamente", "specific_date": "Fecha específica"}, "refund_type": {"title": "Tipo de reembolso", "none": "<PERSON><PERSON><PERSON>", "prorate": "Prorrateo del reembolso en función del uso", "full": "Reembolso completo"}, "cancelled_message": "Cancelado con éxito"}}, "usage_estimate": {"title": "Uso Estimación Coste", "usage": "Utilización", "estimate_cost": "Estimación de costes", "metric": "M<PERSON><PERSON><PERSON>"}, "metadata": {"title": "Metadatos", "no_metadata": "Sin metadatos"}}, "list": {"title": "Suscripciones", "email": "Cliente", "status": "Estado", "plan": "Plan", "no_subscriptions": "Actualmente no hay suscripciones", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Se ha producido un error", "filters": {"status": "Estado", "status_choices": {"cancelled": "Cancelado", "active": "Activo", "blocked": "Bloqueado", "overdue_payment_open": "<PERSON><PERSON>", "trial_active": "Prueba activa", "trial_ended": "<PERSON><PERSON><PERSON> final<PERSON>"}}, "loading": "Cargando suscripciones..."}, "menu": {"title": "Suscripciones", "subscriptions": "Suscripciones", "mass_change": "Cambio de masas", "subscriptions_list": "Lista de suscripción"}, "mass_change": {"list": {"title": "Suscripciones - Cambio masivo", "change_date": "Fecha de modificación", "status": "Estado", "created_at": "Creado en", "no_mass_change": "Actualmente no hay cambios masivos de suscripciones", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Se ha producido un error"}, "create": {"title": "Crear un cambio masivo", "criteria": {"title": "Criterios", "plan": "Plan", "price": "Precio", "brand": "<PERSON><PERSON>", "country": "<PERSON><PERSON>"}, "new": {"title": "Nuevos valores", "plan": "Nuevo plan", "price": "Precio nuevo"}, "change_date": {"title": "Fecha de modificación", "help_info": "Después de la fecha de cambio, todas las renovaciones se harán al nuevo precio. El plan de suscripción se cambiará directamente."}, "estimate": {"amount": "Esto producirá un cambio estimado de {amount} {currency} en el {schedule}"}, "submit_button": "Botón <PERSON>"}, "view": {"title": "Cambio de suscripción masiva", "criteria": {"title": "Criterios", "plan": "Plan", "price": "Precio", "brand": "<PERSON><PERSON>", "country": "<PERSON><PERSON>"}, "new_values": {"title": "Nuevos valores", "plan": "Plan", "price": "Precio"}, "change_date": {"title": "Fecha de modificación"}, "estimate": {"amount": "Esto producirá un cambio estimado de {amount} {currency} en el {schedule}"}, "export_button": "Exportar lista de clientes", "cancel": "<PERSON><PERSON><PERSON>", "uncancel": "<PERSON><PERSON>"}}}, "payment": {"list": {"title": "Pagos", "no_payments": "Actualmente no hay pagos", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "list": {"amount": "Importe", "currency": "Moneda", "customer": "Cliente", "status": "Estado", "created_at": "Creado en"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Se ha producido un error"}, "view": {"title": "Datos de pago", "main": {"title": "Detalles principales", "amount": "Importe", "currency": "Moneda", "external_reference": "Referencia externa", "status": "Estado", "created_at": "Creado en"}, "customer": {"title": "Cliente", "email": "Correo electrónico", "more_info": "Más información", "country": "<PERSON><PERSON>", "attach": "Adjuntar al cliente"}, "refunds": {"title": "Reembolsos", "amount": "Importe", "reason": "Razón", "created_by": "<PERSON><PERSON>o por", "created_at": "Creado en", "none": "No se han encontrado reembolsos"}, "subscriptions": {"title": "Suscripciones", "plan_name": "Nombre del plan", "more_info": "Más información", "none": "Pago no vinculado a suscripciones"}, "receipts": {"title": "Recibos", "created_at": "Creado en", "download": "<PERSON><PERSON><PERSON>", "none": "El pago no tiene recibos"}, "buttons": {"refund": "Emisión Reembolso", "generate_receipt": "Generar recibo"}, "modal": {"attach": {"title": "Adjuntar al cliente", "button": "Adjuntar"}, "refund": {"title": "Reembolso", "amount": {"title": "Importe", "help_info": "Esta es la cantidad de moneda menor. Así, 100 USD son 1,00 USD."}, "reason": {"title": "Razón"}, "submit": "Emisión Reembolso", "success_message": "Reembolso creado con éxito", "error_message": "Algo salió mal"}}}}, "refund": {"list": {"title": "Reembolsos", "no_refunds": "Actualmente no hay reembolsos", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "list": {"amount": "Importe", "currency": "Moneda", "customer": "Cliente", "status": "Estado", "created_by": "<PERSON><PERSON>o por", "created_at": "Creado en"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Se ha producido un error"}, "view": {"title": "Detalles del reembolso", "main": {"title": "Detalles principales", "amount": "Importe", "currency": "Moneda", "external_reference": "Referencia externa", "status": "Estado", "created_at": "Creado en"}, "buttons": {"refund": "Emisión Reembolso"}, "modal": {"refund": {"title": "Reembolso", "amount": {"title": "Importe", "help_info": "Esta es la cantidad de moneda menor. Así, 100 USD son 1,00 USD."}, "reason": {"title": "Razón"}, "submit": "Emisión Reembolso"}}}}, "transactions": {"menu": {"title": "Transacciones", "payments": "Pagos", "refunds": "Reembolsos", "charge_backs": "Contracargos", "invoices": "Facturas", "unpaid_invoices": "Facturas impagadas", "checkout": "Pedido", "countries": "Países", "tax_types": "Tipos de impuestos"}}, "settings": {"menu": {"title": "<PERSON><PERSON><PERSON><PERSON>", "user_settings": "Configuración del usuario", "invite": "Invite a", "pdf_templates": "Plantillas PDF", "email_templates": "Plantillas de correo electrónico", "tax_settings": "Ajustes fiscales", "brand_settings": "Ajustes de marca", "notification_settings": "Ajustes de notificación", "system_settings": "Configuración del sistema", "users": "Usuarios", "stripe": "<PERSON><PERSON>", "api_keys": "Claves API", "exchange_rates": "Tipos de cambio", "integrations": "Integraciones", "vat_sense": "VatSense", "audit_log": "Registro de auditoría"}, "pdf_template": {"list": {"title": "Plantilla", "name": "Nombre", "locale": "Local", "brand": "<PERSON><PERSON>", "create_btn": "<PERSON><PERSON>", "edit_btn": "<PERSON><PERSON>", "no_templates": "Sin plantillas", "error_message": "Se ha producido un error", "generator": "Actualizar la configuración del generador"}, "update": {"title": "Actualizar plantilla - {name}", "content": "Contenido", "save": "Guardar", "download": "Descargar Test PDF", "template": "Plantilla", "help_info": {"template": "Uso del lenguaje de plantillas Twig", "variable_docs": "Consulte la documentación para saber qué variables están disponibles"}}, "generator_settings": {"title": "Configuración del generador de PDF", "generator": "Generador", "tmp_dir": "Directorio temporal", "api_key": "Clave API", "bin": "Ubicación de la papelera", "submit": "Guardar", "help_info": {"generator": "El generador que se utilizará. Si no está seguro, utilice mpdf", "tmp_dir": "El directorio temporal que se utilizará. Si no está seguro, utilice /tmp", "api_key": "La clave API que se utilizará", "bin": "La ubicación de wkhtmltopdf"}}, "create": {"title": "Crear plantilla", "content": "Contenido", "save": "Guardar", "download": "Descargar Test PDF", "template": "Plantilla", "locale": "Local", "type": "Tipo", "brand": "<PERSON><PERSON>", "help_info": {"locale": "Configuración regional de la plantilla PDF", "brand": "La marca para la que es la plantilla PDF", "type": "El tipo de PDF para el que es la plantilla", "template": "Uso del lenguaje de plantillas Twig", "variable_docs": "Consulte la documentación para saber qué variables están disponibles"}}}, "brand_settings": {"list": {"title": "Ajustes de marca", "name": "Nombre", "edit_btn": "<PERSON><PERSON>", "no_brands": "No existen marcas", "create_new": "<PERSON><PERSON>", "error_message": "Se ha producido un error"}, "update": {"title": "Actualizar configuración de marca - {name}", "fields": {"name": "Nombre", "email": "Dirección de correo electrónico", "company_name": "Nombre de la empresa", "street_line_one": "Calle Línea 1", "street_line_two": "Calle Línea 2", "city": "Ciudad", "region": "Región", "country": "<PERSON><PERSON>", "postcode": "Código postal", "code": "Código", "tax_number": "Número de identificación fiscal", "tax_rate": "Tipo impositivo", "digital_services_tax_rate": "Tipo impositivo de los servicios digitales", "support_email": "Correo electrónico de asistencia", "support_phone_number": "Teléfono de asistencia"}, "help_info": {"name": "El nombre de la marca", "code": "El código que se utilizará para identificar la marca en las llamadas a la API. No se puede actualizar.", "email": "El correo electrónico que se utilizará cuando se envíen correos electrónicos al cliente de la marca", "company_name": "El nombre de la empresa a efectos de facturación", "street_line_one": "La primera línea de la dirección de facturación", "street_line_two": "La segunda línea de la dirección de facturación", "city": "Ciudad de la dirección de facturación", "region": "Región/estado de la dirección de facturación", "country": "El país de facturación del cliente - código de país ISO 3166-1 alfa-2.", "postcode": "El código postal de la dirección de facturación", "tax_number": "El número de identificación fiscal de la empresa/marca", "tax_rate": "El tipo impositivo que se utilizará en su país de origen o cuando no pueda encontrarse otro tipo impositivo", "digital_services_tax_rate": "El tipo impositivo que se utilizará para su país de origen o cuando no se pueda encontrar otro tipo impositivo para los servicios digitales", "support_email": "La dirección de correo electrónico para contactar con el servicio de asistencia", "support_phone_number": "El número de teléfono de contacto con el servicio de asistencia"}, "general": "Ajustes generales", "notifications": "Notificaciones", "address_title": "Dirección de facturación", "success_message": "Actualizado", "submit_btn": "Actualización", "notification": {"subscription_creation": "Creación de suscripciones", "subscription_cancellation": "Cancelación de la suscripción", "expiring_card_warning": "Aviso de tarjeta caducada", "expiring_card_warning_day_before": "Aviso de caducidad de la tarjeta - Día anterior", "invoice_created": "Factura creada", "invoice_overdue": "Factura vencida", "quote_created": "<PERSON><PERSON>", "trial_ending_warning": "Aviso de fin de juicio", "before_charge_warning": "Aviso antes de la carga", "before_charge_warning_options": {"none": "<PERSON><PERSON><PERSON>", "all": "Todos", "yearly": "Anualmente"}, "payment_failure": "Falta de pago"}, "support": "Datos de contacto de asistencia"}, "create": {"title": "<PERSON><PERSON><PERSON> a<PERSON>rca", "fields": {"name": "Nombre", "email": "Dirección de correo electrónico", "company_name": "Nombre de la empresa", "street_line_one": "Calle Línea 1", "street_line_two": "Calle Línea 2", "city": "Ciudad", "region": "Región", "country": "<PERSON><PERSON>", "post_code": "Código postal", "code": "Código", "tax_number": "Número de identificación fiscal", "tax_rate": "Tipo impositivo", "digital_services_tax_rate": "Tipo impositivo de los servicios digitales", "support_email": "Correo electrónico de asistencia", "support_phone_number": "Teléfono de asistencia"}, "help_info": {"name": "El nombre de la marca", "code": "El código que se utilizará para identificar la marca en las llamadas a la API. No se puede actualizar. Debe ser alfanumérico en minúsculas y con guiones bajos.", "tax_number": "El número de identificación fiscal de la marca/empresa", "email": "El correo electrónico que se utilizará cuando se envíen correos electrónicos al cliente de la marca", "company_name": "El nombre de la empresa a efectos de facturación", "street_line_one": "La primera línea de la dirección de facturación", "street_line_two": "La segunda línea de la dirección de facturación", "city": "Ciudad de la dirección de facturación", "region": "Región/estado de la dirección de facturación", "country": "El país de facturación del cliente - código de país ISO 3166-1 alfa-2.", "postcode": "El código postal de la dirección de facturación", "tax_rate": "El tipo impositivo que debe utilizarse para su país de origen o cuando no pueda encontrarse otro tipo impositivo", "digital_services_tax_rate": "El tipo impositivo que se utilizará para su país de origen o cuando no se pueda encontrar otro tipo impositivo para los servicios digitales", "support_email": "La dirección de correo electrónico para contactar con el servicio de asistencia", "support_phone_number": "El número de teléfono de contacto con el servicio de asistencia"}, "address_title": "Dirección de facturación", "success_message": "Actualizado", "submit_btn": "<PERSON><PERSON>", "support": "Datos de contacto de asistencia"}}, "email_template": {"list": {"title": "Plantillas de correo electrónico", "email": "Correo electrónico", "country": "<PERSON><PERSON>", "reference": "Referencia", "brand": "<PERSON><PERSON>", "no_customers": "Actualmente no existen plantillas de correo electrónico", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "locale": "Local", "view_btn": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Se ha producido un error"}, "create": {"title": "Crear plantilla de correo electrónico", "fields": {"name": "Nombre", "locale": "Local", "use_emsp_template": "Utilizar la plantilla del EMSP", "subject": "<PERSON><PERSON><PERSON>", "template_body": "<PERSON><PERSON>", "template_id": "ID de plantilla", "brand": "<PERSON><PERSON>"}, "help_info": {"name": "Para qué correo electrónico es esta plantilla", "locale": "Para qué configuración regional es esta plantilla.", "use_emsp_template": "Si se debe utilizar el sistema de plantillas del proveedor de servicios de correo electrónico que esté utilizando. Si no está seguro, deje la casilla sin marcar", "subject": "El mensaje que debe figurar en el asunto", "template_body": "La plantilla TWIG que se utilizará para generar el html del correo electrónico.", "template_id": "El ID de la plantilla que le proporcionó el proveedor de servicios de correo electrónico donde creó la plantilla. Si no está seguro, desmarque la casilla Usar plantilla emsp.", "brand": "La marca a la que va dirigida la plantilla de correo electrónico.", "variable_docs": "Consulte la documentación para saber qué variables están disponibles"}, "submit_btn": "<PERSON><PERSON>", "success_message": "Plantilla de correo electrónico creada con éxito"}, "update": {"title": "Actualizar plantilla de correo electrónico", "fields": {"name": "Nombre", "locale": "Local", "use_emsp_template": "Utilizar la plantilla del EMSP", "subject": "<PERSON><PERSON><PERSON>", "template_body": "<PERSON><PERSON>", "template_id": "ID de plantilla"}, "help_info": {"name": "Para qué correo electrónico es esta plantilla", "locale": "Para qué configuración regional es esta plantilla.", "use_emsp_template": "Si se debe utilizar el sistema de plantillas del proveedor de servicios de correo electrónico que esté utilizando. Si no está seguro, deje la casilla sin marcar", "subject": "El mensaje que debe figurar en el asunto", "template_body": "La plantilla TWIG que se utilizará para generar el html del correo electrónico.", "template_id": "El ID de la plantilla que le proporcionó el proveedor de servicios de correo electrónico donde creó la plantilla. Si no está seguro, desmarque la casilla Usar plantilla emsp.", "variable_docs": "Consulte la documentación para saber qué variables están disponibles"}, "submit_btn": "Actualización", "success_message": "Plantilla de correo electrónico actualizada correctamente", "test_email": "Enviar correo de <PERSON>"}}, "notification_settings": {"update": {"title": "Ajustes de notificación", "submit_btn": "Actualización", "success_message": "Ajustes de notificación actualizados", "fields": {"send_customer_notifications": "Enviar notificaciones a los clientes", "emsp": "Proveedor de servicios de correo electrónico", "emsp_api_key": "Proveedor de servicios de correo electrónico - Clave API", "emsp_api_url": "Proveedor de servicios de correo electrónico - URL API", "emsp_domain": "Proveedor de servicios de correo electrónico - Dominio", "default_outgoing_email": "Correo electrónico saliente por defecto"}, "help_info": {"emsp": "Qué proveedor de correo electrónico desea utilizar. Si no está seguro, utilice el sistema.", "emsp_api_key": "La clave API proporcionada por el proveedor de servicios de correo electrónico.", "emsp_api_url": "La URL de la API proporcionada por el proveedor de servicios de correo electrónico.", "emsp_domain": "El dominio por el proveedor de servicios de correo electrónico.", "send_customer_notifications": "Si quieres que BillaBear envíe notificaciones a los clientes como creación de suscripción, pausa, recibo de pago, etc.", "default_outgoing_email": "La dirección de correo electrónico predeterminada que se utilizará para enviar notificaciones cuando no exista ninguna configuración de marca"}}}, "system_settings": {"update": {"title": "Configuración del sistema", "submit_btn": "Actualización", "success_message": "Configuración del sistema actualizada", "fields": {"system_url": "URL del sistema", "timezone": "Zona horaria", "invoice_number_generation": "Generación de números de factura", "subsequential_number": "Número subsiguiente", "default_invoice_due_time": "Plazo de facturación por defecto", "format": "Formato", "invoice_generation": "Generación de facturas"}, "help_info": {"system_url": "La url base en la que se encuentra BillaBear.", "timezone": "La zona horaria por defecto del sistema", "invoice_number_generation": "Cómo se genera el número de factura. Random es una cadena aleatoria y subsequent significa que es un número que se incrementa", "subsequential_number": "El último número de factura utilizado. El siguiente número de factura tendrá un dígito más", "default_invoice_due_time": "Cuánto tiempo transcurre entre la creación de la factura y la fecha de vencimiento", "format": "El formato que se utilizará para la generación de números de factura. %S es el número subsiguiente y %R para 8 caracteres aleatorios.", "invoice_generation": "Cuándo hay que generar una nueva factura para las suscripciones"}, "invoice_number_generation": {"random": "Número aleatorio", "subsequential": "Posterior", "format": "Formato"}, "default_invoice_due_time": {"30_days": "30 días", "60_days": "60 días", "90_days": "90 días", "120_days": "120 días"}, "invoice_generation_types": {"periodically": "Periódicamente", "end_of_month": "Fin de mes"}}}, "user": {"list": {"title": "Usuario", "email": "Correo electrónico", "roles": "Funciones", "reference": "Referencia", "no_customers": "Actualmente no hay clientes", "create_new": "<PERSON><PERSON>", "invite": "Invitar a un nuevo usuario", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view_btn": "<PERSON>er", "list": {"email": "Correo electrónico", "role": "Funciones"}, "invite_title": "Invi<PERSON>", "invite_list": {"email": "Correo electrónico", "sent_at": "Enviado a", "role": "Funciones", "copy_link": "<PERSON><PERSON><PERSON> enlace", "copied_link": "Copiado"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Se ha producido un error", "audit_log": "Registro de auditoría"}, "update": {"title": "Actualizar usuario", "fields": {"email": "Correo electrónico", "roles": "Funciones"}, "help_info": {"email": "El correo electrónico que el usuario debe utilizar para iniciar sesión y recibir notificaciones.", "roles": "A qué debe tener acceso el usuario."}, "submit_btn": "Actualización", "success_message": "Actualizado correctamente el usuario"}}, "stripe": {"main": {"title": "Importación de rayas", "edit_config": "Editar configuración", "hide_config": "Ocultar configuración", "start_button": "Botón Iniciar importación", "already_in_progress": "Importación en curso", "list": {"state": "Estado", "last_id": "Última identificación procesada", "created_at": "Creado en", "updated_at": "Actualización en", "no_results": "Hasta ahora no ha habido importaciones de rayas.", "view": "<PERSON>er"}, "danger_zone": {"title": "Zona de peligro", "use_stripe_billing": "Utilice Stripe Billing para cobrar a los clientes.", "disable_billing": "Desactivar Stripe Billing", "enable_billing": "Activar Stripe Billing"}, "disable_billing_modal": {"title": "Desactivar Stripe Billing", "disable_all_subscriptions": "Al desactivar Stripe Billing, estás diciendo que ya no quieres que Stripe gestione los cobros a los clientes, sino que lo haga BillaBear. Esto le ahorrará dinero.", "warning": "Una vez desactivado, si desea volver a utilizar Stripe Billing tendrá que volver a suscribir manualmente a todo el mundo.", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirme"}, "webhook": {"title": "Gancho web", "url": "URL del webhook", "register_webhook": "Registrar Webhook", "deregister_webhook": "Dar de baja un webhook", "help_info": {"url": "Una URL https que está disponible públicamente para llamadas webhook."}}, "stripe_config": {"title": "Claves API de Stripe", "description": "Para utilizar Stripe es necesario configurar las claves API.", "stripe_private_key": "Clave privada", "help_info": {"stripe_private_key": "La clave de API que se utilizará para autenticar las solicitudes de backend", "stripe_public_key": "La clave de API que se utilizará para autenticar las solicitudes del frontend."}, "stripe_public_key": "Clave pública", "submit_button": "Enviar", "error": "No se pueden confirmar las claves API de Stripe."}}, "view_import": {"title": "Importación de rayas", "progress": "Progreso", "error": "Error", "last_updated_at": "Última actualización", "last_id_processed": "Última identificación procesada", "process": {"started": "<PERSON><PERSON><PERSON>", "customers": "Clientes", "products": "Productos", "prices": "<PERSON><PERSON><PERSON>", "subscriptions": "Suscripciones", "payments": "Pagos", "refunds": "Reembolsos", "charge_backs": "Contracargos", "completed": "Completado"}}}, "api_keys": {"main": {"title": "Claves API", "add_new_button": "Crear nueva clave API", "info": {"api_base_url": "URL base de la API"}, "list": {"name": "Nombre", "key": "Clave", "expires_at": "Caduca en", "created_at": "Creado en", "no_api_keys": "Actualmente no hay claves API", "disable_button": "Desactivar"}, "create": {"title": "<PERSON><PERSON>r nueva clave", "name": "Nombre", "expires": "Expira en", "close": "<PERSON><PERSON><PERSON>", "create_button": "<PERSON><PERSON>"}}}, "exchange_rates": {"title": "Tipos de cambio", "list": {"currency_code": "Moneda", "rate": "<PERSON><PERSON><PERSON>", "no_rates": "Sin tarifas"}}, "tax_settings": {"update": {"title": "Ajustes fiscales", "submit_btn": "Enviar", "success_message": "Configuración fiscal actualizada", "fields": {"tax_customers_with_tax_number": "Clientes fiscales con NIF", "eu_business_tax_rules": "Manejar las normas de la UE sobre fiscalidad de las empresas", "eu_one_stop_shop_rule": "Ventanilla única de la UE", "vat_sense_enabled": "VAT Sense Activado", "vat_sense_api_key": "Clave API VAT Sense", "validate_vat_ids": "Validar los CIF"}, "help_info": {"tax_customers_with_tax_number": "Si no se comprueba, los clientes que hayan facilitado un número de identificación fiscal no tendrán que pagar impuestos", "eu_business_tax_rules": "Si esta opción está activada, los clientes comerciales que hayan facilitado un número de IVA recibirán un tratamiento diferente al de los clientes normales", "eu_one_stop_shop_rule": "Aplicar la regla de la ventanilla única de la UE. Los países de la UE tributan independientemente del umbral.", "vat_sense_enabled": "Si desea sincronizar diariamente sus normas fiscales con la base de datos VAT Sense", "vat_sense_api_key": "Tu clave de la API de VAT Sense. <a href=\"https://vatsense.com/signup?referral=BILLABEAR\" target='_blank'>Obtenga una gratis aquí</a>.", "validate_vat_ids": "Si desea validar los identificadores de impuestos con la API VAT Sense."}}, "vatsense": {"title": "VatSense", "fields": {"vat_sense_enabled": "VAT Sense Activado", "vat_sense_api_key": "Clave API VAT Sense", "validate_vat_ids": "Validar los CIF"}, "help_info": {"vat_sense_enabled": "Si desea sincronizar diariamente sus normas fiscales con la base de datos VAT Sense", "vat_sense_api_key": "Tu clave de la API de VAT Sense. <a href=\"https://vatsense.com/signup?referral=BILLABEAR\" target='_blank'>Obtenga una gratis aquí</a>.", "validate_vat_ids": "Si desea validar los identificadores de impuestos con la API VAT Sense."}, "description": "Con nuestra integración con VAT Sense, podrá actualizar automáticamente sus normas fiscales cuando se produzcan cambios en la legislación fiscal de todo el mundo. También puede hacer que VAT Sense valide los CIF para asegurarse de que los clientes europeos tienen CIF válidos.", "create_account": "<PERSON><PERSON>e crear una cuenta gratuita.", "create_account_link": "<PERSON><PERSON><PERSON> una cuenta"}}}, "charge_backs": {"list": {"title": "Contracargos", "no_charge_backs": "Actualmente no hay devoluciones de cargos", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view_payment": "Ver pago", "list": {"amount": "Importe", "currency": "Moneda", "customer": "Cliente", "status": "Estado", "created_at": "Creado en"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}}}, "reports": {"dashboard": {"title": "Cuadro de mandos", "subscription_count": {"title": "Suscripciones activas"}, "subscription_creation": {"title": "Nuevas suscripciones"}, "subscription_cancellation": {"title": "Suscripciones canceladas"}, "payment_amount": {"title": "Ingresos obtenidos"}, "refund_amount": {"title": "Importe reembolsado"}, "charge_back_amount": {"title": "Importe impugnado"}, "estimated_mrr": "TMAR estimada", "estimated_arr": "ARR estimado", "header": {"active_subscriptions": "Suscripciones activas", "active_customers": "Clientes activos", "unpaid_invoices": "Facturas impagadas"}, "buttons": {"daily": "Diario", "monthly": "Mensualmente", "yearly": "Anualmente", "subscriptions": "Suscripciones", "payments": "Pagos"}, "links": {"customers": "Clientes", "subscriptions": "Suscripciones", "invoices": "Facturas"}, "latest_customers": {"title": "Últimos clientes", "list": {"email": "Correo electrónico", "creation_date": "Fecha de creación"}}, "latest_events": {"title": "Últimos acontecimientos", "list": {"event_type": "Tipo de evento", "customer": "Cliente", "creation_date": "Fecha de creación"}}, "latest_payments": {"title": "Últimos pagos", "list": {"amount": "Importe", "customer": "Cliente", "creation_date": "Fecha de creación"}}, "payments": {"title": "Totales de pago"}, "loading_chart": "Cargando datos del gráfico..."}, "expiring_cards": {"main": {"title": "Tarjetas que caducan", "list": {"customer_email": "Correo electrónico del cliente", "card_number": "Número de tarjeta", "no_expiring_cards": "Ninguna tarjeta a punto de caducar", "loading": "cargando", "view": "<PERSON>er"}}}, "menu": {"title": "Informes", "dashboard": "Cuadro de mandos", "expiring_cards": "Tarjetas que caducan", "subscriptions": "Suscripciones", "tax": "Impuesto", "churn": "Rotación de suscripciones", "lifetime": "De por vida"}, "subscriptions": {"overview": {"title": "Suscripciones", "plans": {"title": "Desglose de los planes"}, "schedules": {"title": "Desglose del calendario"}}, "churn": {"title": "Rotación de suscripciones", "buttons": {"daily": "Diario", "monthly": "Mensualmente", "yearly": "Anualmente"}}}, "vat": {"overview": {"title": "IVA", "list": {"amount": "Importe", "currency": "Moneda", "country": "<PERSON><PERSON>"}}}, "financial": {"lifetime": {"title": "Valor de vida útil", "lifespan": "Vida útil", "lifespan_value": "{lifespan} años", "lifetime": "Valor de vida útil", "customer_count": "Recuento de clientes", "filters": {"country": "<PERSON><PERSON>", "payment_schedule": "Calendario de pagos", "subscription_plan": "Plan de suscripción", "brand": "<PERSON><PERSON>"}, "help_info": {"country": "Para ver el valor de vida de los usuarios de este país", "payment_schedule": "Para ver el valor de vida de los usuarios que pagan con un plan de pago", "subscription_plan": "Para ver el valor de vida de los usuarios de un plan de suscripción", "brand": "Para ver el valor de vida de los usuarios de una marca"}, "schedules": {"week": "<PERSON><PERSON><PERSON>", "month": "Mensualmente", "year": "Anualmente"}, "chart": {"lifetime_values": "Valor de vida útil", "customer_counts": "Recuento de clientes"}, "submit": "Filtro"}}, "tax": {"title": "Informe fiscal", "map": {"title": "Impuesto recaudado por"}, "countries": {"title": "Umbrales por país", "transacted_amount": "<strong>Transacted:</strong> {currency}{transacted_amount}", "collected_amount": "<strong>Recaudado:</strong> {currency}{collected_amount}", "threshold_status": "<strong>Umbral:</strong> {status}", "threshold_reached": "Alcanzado", "threshold_not_reached": "No alcanzado"}, "transactions": {"title": "Ejemplo de exportación", "download": "Descargar exportación"}}}, "credit": {"create": {"title": "<PERSON><PERSON><PERSON> c<PERSON>", "amount": "Importe", "currency": "Moneda", "reason": "Razón", "type": "Tipo", "credit": "<PERSON><PERSON><PERSON><PERSON>", "debit": "Débito", "help_info": {"type": "Tipo de ajuste de crédito, crédito o débito", "amount": "El precio es la moneda de nivel menor. Así, 1,00 USD sería 100 y 9,99 sería 999.", "display_amount": "Este precio sería de {amount}.", "currency": "La divisa en la que se cobrará al cliente", "reason": "Una razón opcional que puede ser útil más adelante."}, "success_message": "<PERSON><PERSON><PERSON><PERSON> creado con éxito", "submit_btn": "<PERSON><PERSON>"}}, "invoices": {"list": {"title": "Facturas", "unpaid_title": "Facturas impagadas", "email": "Correo electrónico del cliente", "total": "Total", "currency": "Moneda", "created_at": "Creado en", "download": "<PERSON><PERSON><PERSON>", "charge": "Intento de pago", "no_invoices": "Aquí no hay facturas", "next": "Siguient<PERSON>", "prev": "Anterior", "view_btn": "Ver factura", "status": "Estado", "paid": "<PERSON><PERSON>", "outstanding": "Destacado", "filter": {"title": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico del cliente", "number": "Número de factura"}, "mark_as_paid": "Marcar como pagado"}, "menu": {"title": "Facturas", "invoices": "Lista completa", "unpaid_invoices": "Lista de impagados", "create": "<PERSON><PERSON><PERSON> factura", "quotes": "Citas", "settings": "<PERSON><PERSON><PERSON><PERSON>", "invoices_list": "Lista de facturas"}, "create": {"title": "<PERSON><PERSON><PERSON> factura", "create_invoice": "<PERSON><PERSON><PERSON> factura", "success_message": "Factura creada", "errors": {"no_customer": "Se necesita un cliente", "nothing_to_invoice": "Tiene que añadir una suscripción o un artículo puntual.", "same_currency_and_schedule": "La misma moneda y el mismo calendario deben utilizarse para las suscripciones", "currency": "Se necesita una moneda", "need_description": "Necesita una descripción", "need_amount": "Cantidad necesaria", "need_tax_type": "Necesita un tipo de impuesto"}, "customer": {"create_customer": "Crear cliente", "fields": {"customer": "Cliente", "currency": "Moneda", "due_date": "<PERSON><PERSON>nc<PERSON>o"}, "help_info": {"customer": "El cliente al que va dirigido el presupuesto", "currency": "La moneda que se utilizará para la factura", "due_date": "La fecha de vencimiento de la factura; si no se indica ninguna, se utilizará la fecha por defecto del sistema."}}, "subscriptions": {"title": "Suscripciones", "add_new": "<PERSON><PERSON><PERSON>", "list": {"subscription_plan": "Plan de suscripción", "price": "Precio", "seat_number": "Número de asiento"}, "no_subscriptions": "Sin suscripciones", "add_subscription": "<PERSON><PERSON><PERSON>"}, "items": {"title": "Partidas únicas", "add_item": "<PERSON><PERSON><PERSON> artí<PERSON>", "no_items": "No hay artículos puntuales", "list": {"description": "Descripción", "amount": "Importe", "tax_included": "Impuestos incluidos", "digital_product": "Producto digital", "tax_type": "Tipo de impuesto"}, "tax_types": {"digital_services": "Servicios digitales", "digital_goods": "Productos digitales", "physical": "Bienes y servicios físicos"}}}, "view": {"title": "Ver factura", "main": {"title": "Información sobre la factura", "created_at": "Creado en", "pay_link": "Enlace de pago", "due_date": "<PERSON><PERSON>nc<PERSON>o"}, "customer": {"title": "Cliente", "email": "Correo electrónico", "more_info": "Más información", "address": {"company_name": "Nombre de la empresa", "street_line_one": "Calle Línea 1", "street_line_two": "Calle Línea 2", "city": "Ciudad", "region": "Región", "post_code": "Código postal", "country": "<PERSON><PERSON>"}}, "biller": {"title": "Facturación", "email": "Correo electrónico", "more_info": "Más información", "address": {"company_name": "Nombre de la empresa", "street_line_one": "Calle Línea 1", "street_line_two": "Calle Línea 2", "city": "Ciudad", "region": "Región", "post_code": "Código postal", "country": "<PERSON><PERSON>"}}, "lines": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Descripción", "tax_rate": "Tipo impositivo", "amount": "Importe", "tax_exempt": "Exento de impuestos"}, "total": {"title": "Totales", "total": "Total", "sub_total": "Subtotal", "tax_total": "Total de impuestos"}, "status": {"paid": "Factura pagada con éxito en {date}", "outstanding": "Todavía hay que pagar la factura."}, "actions": {"charge_card": "Tarjeta de crédito", "mark_as_paid": "Marcar como pagado"}, "payment_failed": {"message": "No se ha podido efectuar el pago"}, "payment_succeeded": {"message": "Pago efectuado con éxito."}, "download": "<PERSON><PERSON><PERSON> factura", "invoice_delivery": {"title": "Entregas de facturas", "method": "<PERSON><PERSON><PERSON><PERSON>", "detail": "Detalle", "status": "Estado", "created_at": "Creado en", "no_invoice_deliveries": "Sin entrega de facturas"}}, "settings": {"title": "Configuración de factura", "update": "Actualizar"}, "delivery": {"create": {"title": "Crear nueva entrega de facturas", "fields": {"method": "<PERSON><PERSON><PERSON><PERSON>", "format": "Formato", "sftp": {"port": "Puerto", "hostname": "Nombre de host", "directory": "Directorio", "username": "Nombre de usuario", "password": "Contraseña"}, "webhook": {"method": "<PERSON><PERSON><PERSON><PERSON>", "url": "URL"}, "email": {"email": "Correo electrónico", "help_info": "Si no se indica ninguna dirección de correo electrónico, se utilizará por defecto la del cliente."}}, "save": "Guardar"}, "update": {"title": "Actualizar la entrega de facturas", "fields": {"method": "<PERSON><PERSON><PERSON><PERSON>", "format": "Formato", "sftp": {"port": "Puerto", "hostname": "Nombre de host", "directory": "Directorio", "username": "Nombre de usuario", "password": "Contraseña"}, "webhook": {"method": "<PERSON><PERSON><PERSON><PERSON>", "url": "URL"}, "email": {"email": "Correo electrónico", "help_info": "Si no se indica ninguna dirección de correo electrónico, se utilizará por defecto la del cliente."}}, "save": "Guardar"}, "format": {"pdf": "PDF", "zugferd_v1": "ZUGFeRD V1", "zugferd_v2": "ZUGFeRD V2 - XRechnung"}}, "download": {"loading_message": "Cargando...", "format": "Elija el formato para descargar", "download": "<PERSON><PERSON><PERSON>"}}, "home": {"stripe_import": {"text": "No has importado tus datos de banda.", "link": "Haga clic aquí para importar ahora", "dismiss": "Desestimar"}, "update_available": {"text": "Hay una actualización disponible", "link": "Detalles de la publicación", "dismiss": "Desestimar"}, "default_tax": {"text": "Su país no admite tipos impositivos por defecto. ¡Debe establecer un tipo impositivo en su marca por defecto!", "link": "Ver marcas"}}, "vouchers": {"create": {"title": "Crear vale", "submit": "Enviar", "success_message": "Con éxito, vale creado", "fields": {"name": "Nombre", "type": "Tipo", "type_percentage": "Po<PERSON>entaj<PERSON>", "type_fixed_credit": "Crédito fijo", "percentage": "Po<PERSON>entaj<PERSON>", "entry_type": "Tipo de entrada", "entry_type_manual": "Manual", "entry_type_automatic": "Automático", "amount": "Importe - {amount}", "code": "Código", "entry_event": "Evento", "event_expired_card_added": "Añadir nueva tarjeta de pago durante el aviso de tarjeta caducada"}, "help_info": {"name": "El nombre del vale", "type": "Porcentaje es un porcentaje de una factura y crédito fijo da un crédito fijo", "entry_type": "Manual significa que el usuario introduce un código, automático significa que se activa por un evento", "percentage": "El porcentaje de descuento", "amount": "El importe en {amount} que proporciona el vale", "code": "El código que el cliente deberá proporcionar para que se active el vale", "entry_event": "El evento que debe ocurrir para que se active el vale"}}, "list": {"title": "Vales", "no_vouchers": "Actualmente no hay vales", "create_new": "Crear nuevo vale", "list": {"name": "Nombre", "type": "Tipo", "entry_type": "Tipo de entrada"}, "view_btn": "<PERSON>er", "loading": "Vales de carga"}, "view": {"title": "Vale", "main": {"name": "Nombre", "type": "Tipo", "disabled": "Discapacitados", "entry_type": "Tipo de entrada", "percentage": "Po<PERSON>entaj<PERSON>", "amount": "Importe para {amount}", "code": "Código", "automatic_event": "Evento automático"}, "disable": "Desactivar", "enable": "Activar"}}, "quotes": {"create": {"title": "<PERSON><PERSON><PERSON> presupuesto", "create_quote": "<PERSON><PERSON><PERSON> presupuesto", "success_message": "<PERSON><PERSON> c<PERSON>a", "errors": {"no_customer": "Se necesita un cliente", "nothing_to_invoice": "Tiene que añadir una suscripción o un artículo puntual.", "same_currency_and_schedule": "La misma moneda y el mismo calendario deben utilizarse para las suscripciones", "currency": "Se necesita una moneda", "need_description": "Necesita una descripción", "need_amount": "Cantidad necesaria", "need_tax_type": "Necesita un tipo de impuesto"}, "customer": {"create_customer": "Crear cliente", "fields": {"customer": "Cliente", "currency": "Moneda", "expires_at": "Caduca en"}, "help_info": {"customer": "El cliente al que va dirigido el presupuesto", "currency": "La moneda que se utilizará para la cotización", "expires_at": "Cuando la cotización caduca y no se puede pagar"}}, "subscriptions": {"title": "Suscripciones", "add_new": "<PERSON><PERSON><PERSON>", "list": {"subscription_plan": "Plan de suscripción", "price": "Precio", "per_seat": "Por asiento"}, "no_subscriptions": "Sin suscripciones", "add_subscription": "<PERSON><PERSON><PERSON>"}, "items": {"title": "Partidas únicas", "add_item": "<PERSON><PERSON><PERSON> artí<PERSON>", "no_items": "No hay artículos puntuales", "list": {"description": "Descripción", "amount": "Importe", "tax_included": "Impuestos incluidos", "digital_product": "Producto digital", "tax_type": "Tipo de impuesto"}, "tax_types": {"digital_services": "Servicios digitales", "digital_goods": "Productos digitales", "physical": "Bienes y servicios físicos"}}}, "list": {"title": "Citas", "email": "Correo electrónico del cliente", "total": "Total", "currency": "Moneda", "created_at": "Creado en", "no_quotes": "Aquí no hay citas", "next": "Siguient<PERSON>", "prev": "Anterior", "view_btn": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico del cliente", "number": "Número de factura"}}, "view": {"title": "Ver presupuesto", "quote": {"title": "Información", "created_by": "<PERSON><PERSON>o por", "created_at": "Creado en", "expires_at": "Caduca en", "pay_link": "Enlace de pago"}, "status": {"paid": "Presupuesto pagado con éxito en {date}"}, "customer": {"title": "Cliente", "email": "Correo electrónico", "more_info": "Más información", "address": {"company_name": "Nombre de la empresa", "street_line_one": "Calle Línea 1", "street_line_two": "Calle Línea 2", "city": "Ciudad", "region": "Región", "post_code": "Código postal", "country": "<PERSON><PERSON>"}}, "lines": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Descripción", "schedule": "Calendario de pagos", "tax_rate": "Tipo impositivo", "amount": "Importe", "one_off": "Una sola vez", "tax_exempt": "Exento de impuestos"}, "total": {"title": "Totales", "total": "Total", "sub_total": "Subtotal", "tax_total": "Total de impuestos"}}}, "system": {"webhooks": {"webhook_endpoint": {"list": {"title": "Puntos finales Webhook", "add": "<PERSON><PERSON><PERSON> punto final", "view": "<PERSON>er", "list": {"name": "Nombre", "url": "URL", "status": "Estado"}, "no_endpoints": "Actualmente no hay puntos finales de webhook"}, "create": {"title": "Crear punto final Webhook", "fields": {"name": "Nombre", "url": "URL"}, "help_info": {"name": "El nombre para el punto final de webhook para ayudar a identificarlo más tarde", "url": "La URL a la que se enviarán las cargas útiles"}, "create_button": "<PERSON><PERSON>"}, "view": {"title": "Ver punto final", "main": {"title": "Información", "name": "Nombre", "url": "URL"}}}, "main": {"title": "Webhooks", "manage_endpoints": "Gestionar puntos finales", "list": {"type": "Tipo", "created_at": "Creado en", "view_btn": "Ver datos de eventos", "loading": "Carga de eventos Webhook", "no_events": "No se han producido eventos webhook"}}, "event": {"view": {"title": "Información sobre el evento", "main": {"title": "Datos del evento", "type": "Tipo de evento", "payload": "Carga útil", "created_at": "Creado en"}, "responses": {"title": "Solicitudes de punto final", "list": {"url": "URL", "status_code": "Código de estado", "body": "<PERSON><PERSON><PERSON>", "error": "Error", "view": "<PERSON>er", "created_at": "Creado en"}}, "info": {"title": "Ver Solicitar información", "error_message": "<PERSON><PERSON><PERSON>", "status_code": "Código de estado", "body": "Órgano de respuesta", "processing_time": "Tiempo de procesamiento"}}}}, "integrations": {"list": {"title": "Integraciones", "list": {"name": "Integración"}, "slack": {"name": "<PERSON><PERSON>ck", "button": "Configure"}}, "slack": {"webhooks": {"list": {"title": "Ganchos web de Slack", "name": "Nombre", "webhook": "Gancho web", "disable_btn": "Desactivar", "enable_btn": "Activar", "no_webhooks": "Aún no hay webhooks de Slack", "next": "Siguient<PERSON>", "prev": "Anterior", "error_message": "No se pueden obtener los webhooks de Slack", "create_new": "<PERSON><PERSON>"}, "create": {"title": "<PERSON><PERSON><PERSON>hook", "fields": {"name": "Nombre", "webhook": "Url del webhook"}, "help_info": {"name": "El nombre utilizado para identificar este webhook dentro de BillaBear", "webhook": "La URL proporcionada por Slack que se utilizará como webhook"}, "save_btn": "Guardar"}}, "notifications": {"list": {"title": "Notificación Slack", "event": "Evento", "webhook": "Gancho web", "disable_btn": "Desactivar", "template": "Plantilla", "enable_btn": "Activar", "no_notifications": "Aún no hay notificaciones de Slack", "next": "Siguient<PERSON>", "prev": "Anterior", "error_message": "No se pueden obtener notificaciones de Slack", "create_new": "<PERSON><PERSON>"}, "create": {"title": "Crear notificación Slack", "fields": {"webhook": "Gancho web", "event": "Evento", "template": "Plantilla"}, "help_info": {"event": "El evento que debe activar la notificación", "webhook": "El webhook de Slack que se utilizará para la notificación", "template": "La plantilla que se utilizará al enviar la notificación. <a href=\"https://docs.billabear.com/user/integration/slack\" target=\"_blank\">Las variables se pueden encontrar aquí</a>"}, "save_btn": "Guardar"}}, "menu": {"title": "<PERSON><PERSON>ck", "webhooks": "Webhooks", "notification": "Notificaciones"}}}, "menu": {"title": "Herramientas del sistema", "webhooks": "Webhooks", "integrations": "Integraciones"}}, "checkout": {"create": {"title": "Crear pago", "create_quote": "Crear pago", "success_message": "<PERSON>go creado", "errors": {"no_customer": "Se necesita un cliente", "nothing_to_invoice": "Tiene que añadir una suscripción o un artículo puntual.", "same_currency_and_schedule": "La misma moneda y el mismo calendario deben utilizarse para las suscripciones", "currency": "Se necesita una moneda", "need_description": "Necesita una descripción", "need_amount": "Cantidad necesaria", "need_tax_type": "Necesita un tipo de impuesto"}, "customer": {"create_customer": "Crear cliente", "fields": {"name": "Nombre", "permanent": "Permanente", "customer": "Cliente", "currency": "Moneda", "slug": "<PERSON>bosa", "expires_at": "Caduca en", "brand": "<PERSON><PERSON>"}, "help_info": {"permanent": "Si el pago es permanente o único", "name": "Nombre identificativo de la caja", "customer": "El cliente al que va dirigido el pago", "currency": "La moneda que se utilizará para el pago", "expires_at": "Cuando la cotización caduca y no se puede pagar", "slug": "El slug para la URL. Si quieres que el checkout tenga una url bonita usa esto.", "brand": "La marca a la que pertenece la caja"}}, "subscriptions": {"title": "Suscripciones", "add_new": "<PERSON><PERSON><PERSON>", "list": {"subscription_plan": "Plan de suscripción", "price": "Precio", "per_seat": "Por asiento"}, "no_subscriptions": "Sin suscripciones", "add_subscription": "<PERSON><PERSON><PERSON>"}, "items": {"title": "Partidas únicas", "add_item": "<PERSON><PERSON><PERSON> artí<PERSON>", "no_items": "No hay artículos puntuales", "list": {"description": "Descripción", "amount": "Importe", "tax_included": "Impuestos incluidos", "digital_product": "Producto digital", "tax_type": "Tipo de impuesto"}, "tax_types": {"digital_services": "Servicios digitales", "digital_goods": "Productos digitales", "physical": "Bienes y servicios físicos"}}}, "view": {"title": "Ver pago", "checkout": {"title": "Información de pago", "created_by": "<PERSON><PERSON>o por", "created_at": "Creado en", "expires_at": "Caduca en", "pay_link": "Enlace de pago", "name": "Nombre"}, "status": {"paid": "Presupuesto pagado con éxito en {date}"}, "customer": {"title": "Cliente", "email": "Correo electrónico", "more_info": "Más información", "address": {"company_name": "Nombre de la empresa", "street_line_one": "Calle Línea 1", "street_line_two": "Calle Línea 2", "city": "Ciudad", "region": "Región", "post_code": "Código postal", "country": "<PERSON><PERSON>"}}, "lines": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Descripción", "schedule": "Calendario de pagos", "tax_rate": "Tipo impositivo", "amount": "Importe", "one_off": "Una sola vez", "tax_exempt": "Exento de impuestos"}, "total": {"title": "Totales", "total": "Total", "sub_total": "Subtotal", "tax_total": "Total de impuestos"}}, "list": {"title": "Salidas", "email": "Correo electrónico", "country": "<PERSON><PERSON>", "reference": "Referencia", "no_checkouts": "Actualmente no hay ninguna caja", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view_btn": "<PERSON>er", "list": {"name": "Nombre", "created_at": "Creado en", "view": "<PERSON>er"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "name": "Nombre", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "loading": "Carga de resultados", "error_message": "Se ha producido un error"}}, "layout": {"topbar": {"menu": {"settings": "<PERSON><PERSON><PERSON><PERSON>", "signout": "<PERSON><PERSON><PERSON>"}}}, "workflows": {"cancellation_request": {"list": {"title": "Solicitudes de anulación", "email": "Cliente", "status": "Estado", "plan": "Plan", "no_cancellation_requests": "Actualmente no hay solicitudes de anulación", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view": "<PERSON>er", "edit_button": "<PERSON><PERSON>", "bulk_button": "Reprocesamiento a granel", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "T<PERSON>e Error"}, "error_message": "Se ha producido un error"}, "view": {"title": "Detalles de la solicitud de anulación", "subscription": {"title": "Detalles de la suscripción", "name": "Nombre del plan", "customer": "Cliente", "original_cancellation_date": "Fecha de cancelación original"}, "details": {"title": "Detalles de la cancelación", "state": "Estado", "when": "En", "refund_type": "Tipo de reembolso", "specific_date": "Fecha de cancelación"}, "error": {"title": "Error"}, "buttons": {"process": "Proceso de reintento"}}, "edit": {"title": "Editar solicitudes de anulación", "add_place": "<PERSON><PERSON><PERSON> luga<PERSON>", "add_place_modal": {"title": "<PERSON><PERSON><PERSON> luga<PERSON>", "from_place": "Desde el lugar", "to_place": "Para colocar", "name": "Nombre", "event_handler": "Gestor de eventos", "handler_options": "Opciones del manipulador", "add": "<PERSON><PERSON><PERSON>", "required": "Es necesario"}, "edit_place_modal": {"title": "<PERSON><PERSON> lugar", "delete_button": "Borrar lugar", "enable_button": "Activar", "disable_button": "Desactivar"}}}, "menu": {"title": "Flujos de trabajo Herramientas", "cancellation_requests": "Solicitudes de anulación", "subscription_creation": "Creación de suscripciones", "payment_creation": "Creación de pagos", "refund_created_process": "Proceso de creación de reembolsos", "payment_failure_process": "Proceso de impago", "charge_back_creation": "Creación de contracargos"}, "subscription_creation": {"list": {"title": "Creación de suscripciones", "email": "Cliente", "status": "Estado", "plan": "Plan", "no_cancellation_requests": "Actualmente no hay ninguna Suscripción Creación", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "T<PERSON>e Error"}, "error_message": "Se ha producido un error", "edit_button": "<PERSON><PERSON>", "bulk_button": "Reprocesamiento a granel"}, "view": {"title": "Detalles de la creación de la suscripción", "subscription": {"title": "Detalles de la suscripción", "name": "Nombre del plan", "customer": "Cliente", "view": "Ver suscripción"}, "details": {"title": "Detalles de la creación", "state": "Estado"}, "error": {"title": "Error"}, "buttons": {"process": "Proceso de reintento"}}, "edit": {"title": "Editar la creación de suscripciones", "add_place": "<PERSON><PERSON><PERSON> luga<PERSON>", "add_place_modal": {"title": "<PERSON><PERSON><PERSON> luga<PERSON>", "from_place": "Desde el lugar", "to_place": "Para colocar", "name": "Nombre", "event_handler": "Gestor de eventos", "handler_options": "Opciones del manipulador", "add": "<PERSON><PERSON><PERSON>", "required": "Es necesario"}}}, "payment_creation": {"list": {"title": "Creación de pagos", "email": "Cliente", "status": "Estado", "plan": "Plan", "no_results": "Actualmente no hay resultados", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "T<PERSON>e Error"}, "bulk_button": "Reprocesamiento a granel", "error_message": "Se ha producido un error", "edit_button": "<PERSON><PERSON>"}, "view": {"title": "Datos de creación del pago", "payment": {"title": "Datos de pago", "name": "Nombre del plan", "customer": "Cliente", "view": "Ver pago"}, "details": {"title": "Detalles de la creación", "state": "Estado"}, "error": {"title": "Error"}, "buttons": {"process": "Proceso de reintento"}}, "edit": {"title": "Editar creación de pago", "add_place": "<PERSON><PERSON><PERSON> luga<PERSON>", "add_place_modal": {"title": "<PERSON><PERSON><PERSON> luga<PERSON>", "from_place": "Desde el lugar", "to_place": "Para colocar", "name": "Nombre", "event_handler": "Gestor de eventos", "handler_options": "Opciones del manipulador", "add": "<PERSON><PERSON><PERSON>", "required": "Es necesario"}}}, "refund_created_process": {"list": {"title": "<PERSON><PERSON><PERSON><PERSON> creado", "email": "Cliente", "status": "Estado", "plan": "Plan", "no_results": "Actualmente no hay resultados", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "T<PERSON>e Error"}, "error_message": "Se ha producido un error", "edit_button": "<PERSON><PERSON>", "bulk_button": "Reprocesamiento a granel"}, "view": {"title": "Detalles del proceso de creación de reembolsos", "refund": {"title": "Detalles del reembolso", "name": "Nombre del plan", "customer": "Cliente", "view": "Ver reembolso"}, "details": {"title": "Detalles de la creación", "state": "Estado"}, "error": {"title": "Error"}, "buttons": {"process": "Proceso de reintento"}}, "edit": {"title": "Editar proceso de reembolso creado", "add_place": "<PERSON><PERSON><PERSON> luga<PERSON>", "add_place_modal": {"title": "<PERSON><PERSON><PERSON> luga<PERSON>", "from_place": "Desde el lugar", "to_place": "Para colocar", "name": "Nombre", "event_handler": "Gestor de eventos", "handler_options": "Opciones del manipulador", "add": "<PERSON><PERSON><PERSON>", "required": "Es necesario"}, "edit_place_modal": {"title": "<PERSON><PERSON> lugar", "disable_button": "Desactivar", "enable_button": "Activar"}}}, "payment_failure_process": {"list": {"title": "Falta de pago", "email": "Cliente", "status": "Estado", "plan": "Plan", "no_results": "Actualmente no hay resultados", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "T<PERSON>e Error"}, "error_message": "Se ha producido un error"}, "view": {"title": "Detalles del proceso de impago", "payment": {"title": "Detalles del intento de pago", "amount": "Importe", "customer": "Cliente", "view": "Ver factura"}, "details": {"title": "Detalles de la creación", "state": "Estado"}, "error": {"title": "Error"}, "buttons": {"process": "Proceso de reintento"}}}, "charge_back_creation": {"list": {"title": "Creación de contracargos", "email": "Cliente", "status": "Estado", "plan": "Plan", "no_results": "Actualmente no hay resultados", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "reference": "Referencia", "external_reference": "Referencia externa", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "T<PERSON>e Error"}, "error_message": "Se ha producido un error", "edit_button": "<PERSON><PERSON>", "bulk_button": "Reprocesamiento a granel"}, "view": {"title": "Detalles de la creación de la devolución de cargo", "payment": {"title": "Datos de pago", "name": "Nombre del plan", "customer": "Cliente", "view": "Ver pago"}, "details": {"title": "Detalles de la creación", "state": "Estado"}, "error": {"title": "Error"}, "buttons": {"process": "Proceso de reintento"}}, "edit": {"title": "Editar creación de contracargos", "add_place": "<PERSON><PERSON><PERSON> luga<PERSON>", "add_place_modal": {"title": "<PERSON><PERSON><PERSON> luga<PERSON>", "from_place": "Desde el lugar", "to_place": "Para colocar", "name": "Nombre", "event_handler": "Gestor de eventos", "handler_options": "Opciones del manipulador", "add": "<PERSON><PERSON><PERSON>", "required": "Es necesario"}}}}, "country": {"list": {"title": "Países", "no_countries": "Actualmente no hay países", "create_new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "prev": "Página anterior", "list": {"name": "Nombre", "iso_code": "Código", "tax_threshold": "Umbral impositivo", "collecting": "Recaudación de impuestos"}, "view": "<PERSON>er", "filter": {"title": "<PERSON><PERSON><PERSON>", "name": "Nombre", "code": "Código", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "collecting": "Recaudación de impuestos"}, "error_message": "Se ha producido un error"}, "create": {"title": "C<PERSON>r un nuevo país", "country": {"fields": {"name": "Nombre", "iso_code": "Código del p<PERSON>ís", "currency": "Moneda", "threshold": "Umbral", "in_eu": "¿En la UE?", "tax_year": "Inicio del ejercicio fiscal", "collecting": "Recaudar impuestos", "tax_number": "Número de identificación fiscal"}, "help_info": {"name": "El nombre del país", "iso_code": "El código ISO del país", "currency": "Moneda de referencia del país", "threshold": "El umbral impositivo del país", "in_eu": "¿Pertenece el país a la UE?", "tax_year": "La fecha de inicio del ejercicio fiscal para el país", "collecting": "Si siempre hay que recaudar impuestos para este país", "tax_number": "Su número de identificación fiscal para este país."}}, "create_button": "<PERSON><PERSON>"}, "view": {"title": "Ver el país", "fields": {"name": "Nombre", "iso_code": "Código del p<PERSON>ís", "threshold": "Umbral", "currency": "Moneda", "in_eu": "En Eu", "start_of_tax_year": "Inicio del ejercicio fiscal", "enabled": "Activado", "collecting": "Recaudación de impuestos", "tax_number": "Número de identificación fiscal", "transaction_threshold": "Umbral de transacción", "threshold_type": "Tipo de umbral"}, "edit_button": "<PERSON><PERSON>", "tax_rule": {"title": "Normas fiscales", "rate": "Tipo impositivo", "type": "Tipo de impuesto", "default": "Por defecto", "start_date": "Fecha de inicio", "end_date": "Fecha final", "no_tax_rules": "Sin normas fiscales", "add": "<PERSON><PERSON><PERSON> reg<PERSON>", "edit": "<PERSON><PERSON>"}, "add_tax_rule": {"tax_rate": "Tipo impositivo", "tax_type": "Tipo de impuesto", "valid_from": "<PERSON><PERSON><PERSON><PERSON>", "valid_until": "<PERSON><PERSON><PERSON><PERSON> hasta", "title": "<PERSON><PERSON><PERSON> reg<PERSON>", "default": "Norma fiscal por defecto", "save": "Guardar", "select_tax_type": "Seleccione el tipo de impuesto"}, "edit_tax_rule": {"tax_rate": "Tipo impositivo", "tax_type": "Tipo de impuesto", "valid_from": "<PERSON><PERSON><PERSON><PERSON>", "valid_until": "<PERSON><PERSON><PERSON><PERSON> hasta", "title": "Editar regla fiscal", "default": "Norma fiscal por defecto", "save": "Actualización", "select_tax_type": "Seleccione el tipo de impuesto"}, "states": {"title": "Estados", "add": "<PERSON><PERSON>dir nuevo Estado", "name": "Nombre", "code": "Código", "collecting": "¿Cobrar impuestos?", "threshold": "Umbral", "view": "<PERSON>er", "no_states": "No hay estados"}}, "edit": {"title": "<PERSON>ar p<PERSON>", "country": {"fields": {"name": "Nombre", "iso_code": "Código del p<PERSON>ís", "currency": "Moneda", "threshold": "Umbral", "in_eu": "¿En la UE?", "tax_year": "Inicio del ejercicio fiscal", "enabled": "Activado", "collecting": "Recaudar impuestos", "tax_number": "Número de identificación fiscal", "transaction_threshold": "Umbral de transacción", "threshold_type": "Tipo de umbral", "threshold_types": {"rolling": "Rotación anual", "calendar": "Año natural", "rolling_quarterly": "Rodar por trimestres", "rolling_accounting": "Desplazamiento por ejercicio contable"}}, "help_info": {"name": "El nombre del país", "iso_code": "El código ISO del país", "currency": "Moneda de referencia del país", "threshold": "El umbral impositivo del país", "in_eu": "¿Pertenece el país a la UE?", "tax_year": "La fecha de inicio del ejercicio fiscal para el país", "enabled": "Si el país está habilitado para el registro de clientes", "collecting": "Si siempre hay que recaudar impuestos para este país", "tax_number": "Su número de identificación fiscal para este país.", "transaction_threshold": "Cuál es el umbral de transacción del Estado", "threshold_type": "Cómo se determina el periodo de tiempo para el cálculo del umbral"}}, "update_button": "Actualización"}}, "tax_type": {"list": {"title": "Tipos de impuestos", "create_new": "<PERSON><PERSON><PERSON> nuevo", "error_message": "Se ha producido un error", "list": {"name": "Nombre", "make_default": "Por defecto", "is_default": "Por defecto", "default": "Por defecto", "update": "Actualización"}, "no_tax_types": "Actualmente no existen tipos impositivos"}, "create": {"title": "Crear tipo de impuesto", "tax_type": {"fields": {"name": "Nombre", "vat_sense_type": "Tipo de IVA"}, "help_info": {"name": "El nombre del impuesto", "vat_sense_type": "El tipo de impuesto en el sistema VAT Sense"}}, "create_button": "<PERSON><PERSON>"}, "update": {"title": "Actualizar tipo de impuesto", "tax_type": {"fields": {"name": "Nombre", "vat_sense_type": "Tipo de IVA"}, "help_info": {"name": "El nombre del impuesto", "vat_sense_type": "El tipo de impuesto en el sistema VAT Sense"}}, "update_button": "Actualización"}}, "finance": {"integration": {"title": "Integraciones", "fields": {"integration": "Integración", "api_key": "Clave API", "enabled": "Activado"}, "buttons": {"connect": "Conectar mediante <PERSON>uth", "disconnect": "Desconecte", "save": "Guardar"}, "settings": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "xero": {"account_id": "Código de cuenta para pagos"}, "errors": {"required": "Este campo es obligatorio", "invalid": "Este campo no es válido", "complete_error": "Se ha producido un error al intentar guardar estos ajustes. Por favor, inténtelo de nuevo."}}, "menu": {"integration": "Integración"}}, "tax": [], "state": {"view": {"title": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "fields": {"name": "Nombre", "code": "Código", "threshold": "Umbral", "collecting": "Recopilación", "transaction_threshold": "Umbral de transacción", "threshold_type": "Tipo de umbral"}, "tax_rule": {"title": "Normas fiscales", "rate": "Tipo impositivo", "type": "Tipo de impuesto", "default": "Por defecto", "start_date": "Fecha de inicio", "end_date": "Fecha final", "no_tax_rules": "Sin normas fiscales", "add": "<PERSON><PERSON><PERSON> reg<PERSON>", "edit": "<PERSON><PERSON>"}, "add_tax_rule": {"tax_rate": "Tipo impositivo", "tax_type": "Tipo de impuesto", "valid_from": "<PERSON><PERSON><PERSON><PERSON>", "valid_until": "<PERSON><PERSON><PERSON><PERSON> hasta", "title": "<PERSON><PERSON><PERSON> reg<PERSON>", "default": "Norma fiscal por defecto", "save": "Guardar", "select_tax_type": "Seleccione el tipo de impuesto"}, "edit_tax_rule": {"tax_rate": "Tipo impositivo", "tax_type": "Tipo de impuesto", "valid_from": "<PERSON><PERSON><PERSON><PERSON>", "valid_until": "<PERSON><PERSON><PERSON><PERSON> hasta", "title": "Editar regla fiscal", "default": "Norma fiscal por defecto", "save": "Actualización", "select_tax_type": "Seleccione el tipo de impuesto"}}, "create": {"title": "Crear un nuevo Estado", "state": {"fields": {"name": "Nombre", "code": "Código", "collecting": "Recopilación", "threshold": "Umbral"}, "help_info": {"name": "El nombre del Estado", "code": "El código que suele utilizarse como abreviatura del estado", "collecting": "Si siempre estamos recaudando impuestos para el estado", "threshold": "Cuál es el umbral económico del Estado"}}, "create_button": "<PERSON><PERSON>"}, "edit": {"title": "<PERSON><PERSON>", "state": {"fields": {"name": "Nombre", "code": "Código", "collecting": "Recopilación", "threshold": "Umbral", "transaction_threshold": "Umbral de transacción", "threshold_type": "Tipo de umbral", "threshold_types": {"rolling": "Rotación anual", "calendar": "Año natural", "rolling_quarterly": "Rodar por trimestres", "rolling_accounting": "Desplazamiento por ejercicio contable"}}, "help_info": {"name": "El nombre del Estado", "code": "El código que suele utilizarse como abreviatura del estado", "collecting": "Si siempre estamos recaudando impuestos para el estado", "threshold": "Cuál es el umbral económico del Estado", "transaction_threshold": "Cuál es el umbral de transacción del Estado", "threshold_type": "Cómo se determina el periodo de tiempo para el cálculo del umbral"}}, "update_button": "Actualización"}}, "onboarding": {"main": {"bar": {"message": "Stripe debe estar configurado antes de poder utilizar Bill<PERSON>"}, "dialog": {"title": "Incorporación", "has_stripe_key": {"text": "Introduzca claves API de Stripe válidas", "button": "Entrar aqu<PERSON>"}, "has_stripe_imports": {"text": "Importar datos de Stripe", "button": "Importar", "dismiss": "Desestimar"}, "has_product": {"text": "Crear el primer producto", "button": "Crear producto"}, "has_subscription_plan": {"text": "Crear el primer plan de suscripción", "button": "<PERSON><PERSON>"}, "has_customer": {"text": "Crear el primer cliente", "button": "<PERSON><PERSON>"}, "has_subscription": {"text": "Crear la primera suscripción", "button": "<PERSON><PERSON>"}}, "error": "¡Algo salió mal!"}}, "default_error_message": "¡Algo salió mal!", "metric": {"list": {"title": "M<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON>", "name": "Nombre", "no_metrics": "Aún no hay métricas.", "filter": {"name": "Nombre"}, "view_btn": "<PERSON>er"}, "create": {"title": "<PERSON><PERSON><PERSON> m<PERSON>", "fields": {"name": "Nombre", "code": "Código", "type": "Tipo", "aggregation_method": "Método de agregación", "aggregation_property": "Propiedad de agregación", "ingestion": "Ingestión", "filters": "<PERSON><PERSON><PERSON>"}, "help_info": {"name": "El nombre de la métrica", "code": "El código que se utilizará en las llamadas a la API. Sólo letras minúsculas, números y guión bajo.", "type": "Si el contador del cliente debe ponerse a cero al final de un período de suscripción", "aggregation_method": "Cómo deben agregarse los eventos que se envían a BillaBear.", "aggregation_property": "Qué propiedad de los datos del evento debe utilizarse para la agregación.", "ingestion": "Con qué frecuencia deben procesarse los eventos", "filters": "Los filtros que deben aplicarse a la carga útil del evento para excluirla en la agregación"}, "aggregation_methods": {"count": "C<PERSON><PERSON>", "sum": "<PERSON><PERSON>", "latest": "Última", "unique_count": "Recuento único", "max": "Max"}, "ingestion": {"real_time": "En tiempo real", "hourly": "Por hora", "daily": "Diario"}, "filter": {"name": "Nombre", "value": "Valor", "type": "Tipo", "no_filters": "Sin filtros"}, "filter_type": {"inclusive": "Inclusivo", "exclusive": "Exclusivo"}, "create_button": "<PERSON><PERSON>"}, "view": {"title": "<PERSON>er métrica", "main": {"name": "Nombre", "code": "Código", "type": "Tipo", "aggregation_method": "Método de agregación", "aggregation_property": "Propiedad de agregación", "event_ingestion": "Ingestión"}, "filters": {"title": "<PERSON><PERSON><PERSON>", "name": "Nombre", "value": "Valor", "type": "Tipo", "inclusive": "Inclusivo", "exclusive": "Exclusivo"}, "update": "Actualización"}, "update": {"title": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "update_button": "Guardar"}}, "usage_limit": {"create": {"title": "<PERSON><PERSON><PERSON> lí<PERSON> de uso", "fields": {"amount": "Importe", "action": "Acción"}, "help_info": {"amount": "El importe al que desea limitar al cliente antes de que se tomen medidas", "action": "La acción que debe realizarse cuando se supera el límite."}, "actions": {"warn": "<PERSON><PERSON>", "disable": "Desactivar"}, "submit": "<PERSON><PERSON>"}}, "customer_support": {"integration": {"title": "Integraciones de atención al cliente", "fields": {"integration": "Integración", "api_key": "Clave API", "enabled": "Activado"}, "buttons": {"connect": "Conectar mediante <PERSON>uth", "disconnect": "Desconecte", "save": "Guardar"}, "settings": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"required": "Este campo es obligatorio", "invalid": "Este campo no es válido", "complete_error": "Se ha producido un error al intentar guardar estos ajustes. Por favor, inténtelo de nuevo."}, "zendesk": {"token": "<PERSON><PERSON>", "subdomain": "Subdominio", "username": "Nombre de usuario"}, "freshdesk": {"subdomain": "Subdominio", "api_key": "Clave API"}}}, "integrations": {"newsletter": {"title": "Integración de boletines", "fields": {"marketing_list": "Lista de marketing", "announcement_list": "Lista de anuncios"}, "no_lists": "No hay listas disponibles. Introduzca primero los datos de conexión.", "errors": {"list_required": "No puedes activar hasta que hayas seleccionado una lista. Introduzca los datos de conexión, gu<PERSON>rdelos y seleccione una lista."}, "mailchimp": {"fields": {"server_prefix": "Prefijo del servidor"}}}, "menu": {"main": "Integraciones", "accounting": "Contabilidad", "customer_support": "Atención al cliente", "newsletter": "Boletín", "notifications": "Notificaciones", "crm": "CRM"}, "general": {"fields": {"integration": "Integración", "api_key": "Clave API", "enabled": "Activado"}, "buttons": {"connect": "Conectar mediante <PERSON>uth", "disconnect": "Desconecte", "save": "Guardar"}, "settings": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"required": "Este campo es obligatorio", "invalid": "Este campo no es válido", "complete_error": "Se ha producido un error al intentar guardar estos ajustes. Por favor, inténtelo de nuevo."}}, "crm": {"title": "Integraciones CRM", "fields": {"integration": "Integración"}, "buttons": {"connect": "Conectar <PERSON><PERSON>", "disconnect": "Desconecte", "save": "Guardar"}}}, "compliance": {"audit": {"all": {"title": "Registro de auditoría", "log": "Registro", "date": "<PERSON><PERSON>", "billing_admin": "Administrador de facturación conectado", "no_billing_admin": "Esto no lo hizo un administrador de facturación", "display_name": "Mostrar nombre", "context": "Contexto del registro", "no_logs": "No se han encontrado registros"}, "customer": {"title": "Registro de auditoría del cliente - {nombre}"}, "billing_admin": {"title": "Registro de auditoría de administración de facturación - {nombre}"}}}}, "install": {"title": "Instale", "submit_button": "Instale", "user": {"title": "Primer usuario administrador", "email": "Correo electrónico", "password": "Contraseña"}, "settings": {"title": "Configuración del sistema", "default_brand": "<PERSON><PERSON> por defecto", "from_email": "Dirección de correo electrónico del remitente por defecto", "timezone": "Zona horaria", "webhook_url": "Url base", "currency": "Moneda", "country": "<PERSON><PERSON>"}, "complete_text": "¡BillaBear ha sido instalado! Ya puedes iniciar sesión con los datos que has proporcionado.", "login_link": "Haga clic aquí para iniciar sesión", "unknown_error": "Error des<PERSON>.", "stripe": {"no_api_key": "Debe proporcionar una clave de API de Stripe en la variable ENV STRIPE_PRIVATE_API_KEY.", "doc_link": "Más información sobre cómo configurar BillaBear.", "invalid_api_key": "La clave API de Stripe no es válida", "support_link": "<PERSON><PERSON><PERSON> pedir ayuda aqu<PERSON>."}}}