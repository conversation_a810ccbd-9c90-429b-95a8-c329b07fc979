Feature: Per Unit Pricing

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public | Type     |
      | Product One | 1000   | USD      | true      | week     | true   | per_unit |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      | Billing Type |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   | invoice      |

  Scenario:
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                  | Next Charge | Status | Seats |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Active | 50    |
    And stripe billing is disabled
    When the background task to reinvoice active subscriptions
    Then the subscription for "<EMAIL>" will expire in a week
    And there the latest invoice for "<EMAIL>" will be for 50000 "USD"
