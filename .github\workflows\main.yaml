name: PHP test

on:
  push:
  pull_request:
  workflow_dispatch:
  repository_dispatch:
      types: [ push ]
  schedule: # Added to ensure build still works daily.
    - cron: '0 2 * * *' # run at 2 AM UTC


jobs:
  code_style:
    name: Code Style
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install
      - name: PHP_CS_FIXER_IGNORE_ENV - php 8.2 workaround
        run: echo "PHP_CS_FIXER_IGNORE_ENV=true" >> $GITHUB_ENV
      - name: Code Style
        run: |
          vendor/bin/php-cs-fixer fix src --dry-run

  behat_app_portal:
    needs: code_style
    name: Behat Portal
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/Portal

  behat_app_usage:
    needs: code_style
    name: Behat Usage
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
            sudo swapoff -a
            sudo sysctl -w vm.swappiness=1
            sudo sysctl -w fs.file-max=262144
            sudo sysctl -w vm.max_map_count=262144
            docker network create elastic
            docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
            docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install
      - name: Behat
        run: |
          vendor/bin/behat -f progress features/Usage

  behat_app_stripe:
    needs: code_style
    name: Behat Stripe
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/Stripe

  behat_app_subscriptions:
    needs: code_style
    name: Behat Subscriptions
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}

      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/Subscriptions

  behat_app_user:
    needs: code_style
    name: Behat User
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/User

  behat_tax:
    needs: code_style
    name: Behat Tax
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Create all the databases
        run: psql -h localhost -d parthenon_test -U appuser -f docker/database/init.sql
        env:
          PGPASSWORD: apppassword
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/Tax

  behat_system:
    needs: code_style
    name: Behat System
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Create all the databases
        run: psql -h localhost -d parthenon_test -U appuser -f docker/database/init.sql
        env:
          PGPASSWORD: apppassword
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/System

  behat_workflows:
    needs: code_style
    name: Behat Workflows
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Create all the databases
        run: psql -h localhost -d parthenon_test -U appuser -f docker/database/init.sql
        env:
          PGPASSWORD: apppassword
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/Workflows

  behat_invoice:
    needs: code_style
    name: Behat Invoice
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Create all the databases
        run: psql -h localhost -d parthenon_test -U appuser -f docker/database/init.sql
        env:
          PGPASSWORD: apppassword
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/Invoice

      - name: Behat logs
        if: ${{ failure() }}
        run: |
          cat var/log/test.log
          exit 1


  behat_app_background:
    needs: code_style
    name: Behat Background
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Create all the databases
        run: psql -h localhost -d parthenon_test -U appuser -f docker/database/init.sql
        env:
          PGPASSWORD: apppassword
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/Background

  behat_stats:
    needs: code_style
    name: Behat Stats
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Create all the databases
        run: psql -h localhost -d parthenon_test -U appuser -f docker/database/init.sql
        env:
          PGPASSWORD: apppassword
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/Stats

  behat_app_app:
    needs: code_style
    name: Behat APP
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Create all the databases
        run: psql -h localhost -d parthenon_test -U appuser -f docker/database/init.sql
        env:
          PGPASSWORD: apppassword
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/App

  behat_api:
    needs: code_style
    name: Behat API
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Create all the databases
        run: psql -h localhost -d parthenon_test -U appuser -f docker/database/init.sql
        env:
          PGPASSWORD: apppassword
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/Api

  behat_compliance:
    needs: code_style
    name: Behat Compliance
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    services:
      postgres:
        image: timescale/timescaledb-ha:pg16
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: 'parthenon_test'
          POSTGRES_USER: 'appuser'
          POSTGRES_PASSWORD: 'apppassword'
      redis:
        image: redis

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Create all the databases
        run: psql -h localhost -d parthenon_test -U appuser -f docker/database/init.sql
        env:
          PGPASSWORD: apppassword
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install

      - name: Behat
        run: |
          vendor/bin/behat -f progress features/Compliance

  front_end:
    needs: code_style
    name: Frontend
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Set Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 17.x

      - name: Install yarn
        uses: borales/actions-yarn@v3.0.0
        with:
          cmd: install

      - name: encore
        uses: borales/actions-yarn@v3.0.0
        with:
          cmd: encore production

      - name: Run vitest
        uses: borales/actions-yarn@v3.0.0
        with:
          cmd: vitest

  phpunit:
    needs: code_style
    name: PHPUnit
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: [ '8.3', '8.4' ]
        experimental: [false]

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: 'Setup Elasticsearch'
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144
          docker network create elastic
          docker run -d --name=elasticsearch --network=elastic -p 9200:9200 -e discovery.type=single-node -e node.name=es docker.elastic.co/elasticsearch/elasticsearch:7.15.2 sh -c "./bin/elasticsearch-plugin install --batch ingest-attachment && /usr/local/bin/docker-entrypoint.sh"
          docker run --rm --network=elastic curlimages/curl --max-time 120 --retry-max-time 120 --retry 120 --retry-delay 5 --retry-all-errors --show-error --silent http://elasticsearch:9200

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2 #https://github.com/shivammathur/setup-php
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit-bridge
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, pgsql, mongodb, http, apcu
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
      - name: Install dependencies
        run: |
          composer install
      - name: PHPUnit
        run: |
          vendor/bin/phpunit
