Feature: Payment Details Delete
  In order to remove invalid payment details
  As an APP user
  I need to be able to delete payment details

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year | Name     |
      | <EMAIL> | 0444      | 03           | 25          | Card One |
      | <EMAIL> | 0444      | 03           | 25          | Card Two |

  Scenario: Get customer info
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I delete the payment details "Card One" for "<EMAIL>" via APP
    Then the payment details "Card One" for "<EMAIL>" should be deleted
