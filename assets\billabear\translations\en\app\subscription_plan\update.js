export const SUBSCRIPTION_PLAN_UPDATE_TRANSLATIONS = {
    title: "Update Subscription Plan",
    advance: "advance",
    submit_btn: "Update Subscription Plan",
    show_advanced: "Advanced",
    success_message: "Successfully updated subscription plan",
    address_title: "Address",
    fields: {
        name: "Name",
        code_name: "Code Name",
        user_count: "User Count",
        public: "Publicly Available Plan",
        per_seat: "Per Seat",
        free: "Free",
        prices: "Prices",
        features: "Features",
        limits: "Limits",
        has_trial: "Has Trial",
        trial_length_days: "Trial Length",
        is_trial_standalone: "Is Trial Standalone?"
    },
    help_info: {
        name: "The name for the plan",
        code_name: "The code name for the plan to be used with the API.",
        user_count: "The number of users allowed for this plan",
        public: "Is the plan available to the public or a custom plan",
        free: "Is this a free plan?",
        per_seat: "Is the plan charged per seat?",
        has_trial: "If the plan has a trial period by default",
        trial_length_days: "How long the trial should be in days",
        is_trial_standalone: "If a Trial is standalone it doesn't need a price and the subscription pauses at the end of the trial"
    },
    features: {
        title: 'Features',
        add_feature: 'Add Feature'
    },
    limits: {
        title: 'Limits',
        add_limit: 'Add Limits'
    },
    prices: {
        title: 'Prices',
        add_price: 'Add Price'
    }
}
