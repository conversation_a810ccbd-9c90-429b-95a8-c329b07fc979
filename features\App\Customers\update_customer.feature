Feature: Customer Update API
  In order to keep customer data up to date
  As an API user
  I need to be update a customer

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |

  Scenario: Get customer info
    When I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    When I update the customer info via the APP for "<EMAIL>" with:
      | Email              | <EMAIL> |
      | Country            | GB                       |
      | External Reference | cust_4945959             |
      | Reference          | Test Customer            |
    Then the customer "<EMAIL>" should have the reference "Test Customer"

