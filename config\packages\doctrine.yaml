doctrine:
    dbal:
        url: '%env(resolve:DATABASE_URL)%'
        platform_service: 'BillaBear\Database\Doctrine\TimescalePlatform'

        # IMPORTANT: You MUST configure your server version,
        # either here or in the DATABASE_URL env var (see .env file)
        #server_version: '13'
    orm:
        metadata_cache_driver:
            type: pool
            pool: doctrine.system_cache_pool
        query_cache_driver:
            type: pool
            pool: doctrine.system_cache_pool
        result_cache_driver:
            type: pool
            pool: doctrine.result_cache_pool
        dql:
            string_functions:
                DAY: DoctrineExtensions\Query\Postgresql\Day
                MONTH: DoctrineExtensions\Query\Postgresql\Month
                YEAR: DoctrineExtensions\Query\Postgresql\Year
        auto_generate_proxy_classes: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        auto_mapping: true
        resolve_target_entities:
            Parthenon\User\Entity\UserInterface: BillaBear\Entity\User
            Parthenon\User\Entity\TeamInterface: BillaBear\Entity\User
            Parthenon\Billing\Entity\CustomerInterface: BillaBear\Entity\Customer
            Parthenon\Billing\Entity\BillingAdminInterface: BillaBear\Entity\User
            Parthenon\Billing\Entity\PaymentInterface: BillaBear\Entity\Payment
            Parthenon\Billing\Entity\ProductInterface: BillaBear\Entity\Product
            Parthenon\Billing\Entity\PriceInterface: BillaBear\Entity\Price
            Parthenon\Billing\Entity\TierComponentInterface: BillaBear\Entity\TierComponent
            Parthenon\Billing\Entity\ReceiptInterface: BillaBear\Entity\Receipt
            Parthenon\Billing\Entity\ReceiptLineInterface: BillaBear\Entity\ReceiptLine
            Parthenon\Billing\Entity\SubscriptionInterface: BillaBear\Entity\Subscription
            Parthenon\Billing\Entity\SubscriptionPlanInterface: BillaBear\Entity\SubscriptionPlan
        mappings:
            App:
                is_bundle: false
                dir: '%kernel.project_dir%/src/BillaBear/Entity'
                prefix: 'BillaBear\Entity'
                alias: App

when@test:
    doctrine:
        dbal:
            # "TEST_TOKEN" is typically set by ParaTest
            dbname_suffix: '_test%env(default::TEST_TOKEN)%'

when@prod:
    doctrine:
        orm:
            auto_generate_proxy_classes: false
            query_cache_driver:
                type: pool
                pool: doctrine.system_cache_pool
            result_cache_driver:
                type: pool
                pool: doctrine.result_cache_pool

    framework:
        cache:
            pools:
                doctrine.result_cache_pool:
                    adapter: cache.app
                doctrine.system_cache_pool:
                    adapter: cache.system
