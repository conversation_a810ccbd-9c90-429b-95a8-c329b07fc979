Feature: Read quote

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
      | Product Two | prod_jf9j542       |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 2000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price 1000 in "USD" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price 2000 in "USD" with:
      | Name       | Test Two |
      | Public     | True     |
      | Per Seat   | False    |
      | User Count | 10       |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price 3000 in "USD" with:
      | Name       | Test Three |
      | Public     | True     |
      | Per Seat   | False    |
      | User Count | 10       |
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      | Billing Type |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   | invoice      |
      | <EMAIL>   | UK      | cust_dfugfdu       | Customer Two   | card         |
      | <EMAIL> | UK      | cust_mlklfdu       | Customer Three | card         |
      | <EMAIL>  | UK      | cust_dkkoadu       | Customer Four  | card         |
      | <EMAIL>  | UK      | cust_ddsjfu        | Customer Five  | card         |
      | <EMAIL>   | UK      | cust_jliujoi       | Customer Six   | card         |
      | <EMAIL> | UK      | cust_jliujoi       | Customer Six   | invoice      |

  Scenario: Read Quote
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And a quote for "<EMAIL>" exists in "USD":
      | Description | Total | Sub Total | Vat Total|
      | Setup costs | 10000 | 8000      | 2000     |
    And a quote for "<EMAIL>" exists in "USD":
      | Description | Total | Sub Total | Vat Total|
      | Setup costs | 12000 | 8000      | 4000     |
    And a quote for "<EMAIL>" exists in "USD":
      | Description | Total | Sub Total | Vat Total|
      | Setup costs | 14000 | 10000     | 4000     |
    When I view the quotes via the APP
    Then I will see a 3 quotes