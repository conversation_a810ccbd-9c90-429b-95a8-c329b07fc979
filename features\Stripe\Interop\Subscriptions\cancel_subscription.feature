Feature:

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
      | Product Two | prod_jf9j542       |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |


  Scenario: Cancel subscription
    Given I have authenticated to the API
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   |
      | <EMAIL>   | UK      | cust_dfugfdu       | Customer Two   |
      | <EMAIL> | UK      | cust_dfsdfdsdu     | Customer three |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
    When I cancel via the stripe interopt api the subscription "Test Plan" for "<EMAIL>"
    Then the subscription "Test Plan" for "<EMAIL>" will be pending cancel
    And the monthly recurring revenue estimate should be 6000
    And the annual recurring revenue estimate should be 72000


  Scenario: Cancel subscription
    Given I have authenticated to the API
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   |
      | <EMAIL>   | UK      | cust_dfugfdu       | Customer Two   |
      | <EMAIL> | UK      | cust_dfsdfdsdu     | Customer three |
    And the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   |
      | Test Plan         | 3000         | USD            | month          | <EMAIL> |
    When I cancel via the stripe interopt api the subscription "Test Plan" for "<EMAIL>" with prorata cancellation
    Then the subscription "Test Plan" for "<EMAIL>" will be cancelled
    And the monthly recurring revenue estimate should be 6000
    And the annual recurring revenue estimate should be 72000
