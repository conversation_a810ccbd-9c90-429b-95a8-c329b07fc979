export const SUBSCRIPTION_PLAN_VIEW_TRANSLATIONS = {
    title: 'View Subscription Plan Details',
    update: 'Update',
    error: {
        not_found: "No such subscription plan found",
        unknown: "An unknown error has occurred"
    },
    main: {
        title: "Main Details",
        name: "Name",
        code_name: "Code Name",
        per_seat: "Per Seat",
        free: "Free",
        user_count: "User Count",
        public: "Publicly Available",
        has_trial: "Has Trial",
        trial_length_days: "Trial Length",
        is_trial_standalone: "Is Trial Standalone?"
    },
    limits: {
        title: "Limits",
        list: {
            feature: "Feature",
            limit: "Limit",
            no_limits: "No limits"
        }
    },
    features: {
        title: "Features",
        list: {
            feature: "Feature",
            no_features: "No features"
        }
    },
    price: {
        title: "Prices",
        list: {
            amount: "Amount",
            currency: "Currency",
            recurring: "Recurring Payment",
            schedule: "Payment Schedule",
            including_tax: "Price Includes Tax",
            public: "Public Price",
            external_reference: "External Reference",
            usage: "Usage"
        }
    }
}
