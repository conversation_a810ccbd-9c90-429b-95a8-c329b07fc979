services:
  _defaults:
    autowire: true
    autoconfigure: false # false to stop twig injecting the sandbox into the main twig env

  template.security_policy:
    class: Twig\Sandbox\SecurityPolicy
    factory: [ '@BillaBear\Twig\SecurityPolicyFactory', 'create' ]

  template_twig:
    class: Twig\Environment
    arguments:
      - '@twig.loader'
    calls:
      - method: addExtension
        arguments:
          - '@template.sandbox'


    # Define the sandbox extension with the custom security policy
  template.sandbox:
    public: false
    tags: ['not-extension']
    class: Twig\Extension\SandboxExtension
    arguments:
      - '@template.security_policy'
      - true

