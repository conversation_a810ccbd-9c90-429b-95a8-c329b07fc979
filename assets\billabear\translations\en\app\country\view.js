export const COUNTRY_VIEW_TRANSLATIONS = {
    title: "View Country",
    fields: {
        name: "Name",
        iso_code: "Country Code",
        threshold: "Threshold",
        currency: "Currency",
        in_eu: "In Eu",
        start_of_tax_year: "Start Of Tax Year",
        enabled: "Enabled",
        collecting: "Collecting Tax",
        tax_number: "Tax Number",
        transaction_threshold: "Transaction Threshold",
        threshold_type: "Threshold Type",
    },
    edit_button: "Edit",
    tax_rule: {
        title: "Tax Rules",
        rate: "Tax Rate",
        type: "Tax Type",
        default: "Is Default",
        start_date: "Start Date",
        end_date: "End Date",
        no_tax_rules: "No Tax Rules",
        add: "Add Tax Rule",
        edit: "Edit",
    },
    add_tax_rule: {
        tax_rate: "Tax Rate",
        tax_type: "Tax Type",
        valid_from: "Valid From",
        valid_until: "Valid Until",
        title: "Add Tax Rule",
        default: "Default tax rule",
        save: "Save",
        select_tax_type: "Select Tax Type"
    },
    edit_tax_rule: {
        tax_rate: "Tax Rate",
        tax_type: "Tax Type",
        valid_from: "Valid From",
        valid_until: "Valid Until",
        title: "Edit Tax Rule",
        default: "Default tax rule",
        save: "Update",
        select_tax_type: "Select Tax Type"
    },
    states: {
        title: "States",
        add: "Add New State",
        name: "Name",
        code: "Code",
        collecting: "Collecting Tax?",
        threshold: "Threshold",
        view: "View",
        no_states: "There are no states"
    }
}
