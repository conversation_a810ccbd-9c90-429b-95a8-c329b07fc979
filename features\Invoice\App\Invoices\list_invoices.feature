Feature: List invoices

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
      | Product Two | prod_jf9j542       |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Plan |
      | Public     | True      |
      | Per Seat   | False     |
      | User Count | 10        |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Two |
      | Public     | True     |
      | Per Seat   | False    |
      | User Count | 10       |
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      | Billing Type |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   | invoice      |
      | <EMAIL>   | UK      | cust_dfugfdu       | Customer Two   | card         |
      | <EMAIL> | UK      | cust_mlklfdu       | Customer Three | card         |
      | <EMAIL>  | UK      | cust_dkkoadu       | Customer Four  | card         |
      | <EMAIL>  | UK      | cust_ddsjfu        | Customer Five  | card         |
      | <EMAIL>   | UK      | cust_jliujoi       | Customer Six   | card         |
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +3 Minutes  | Active    |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>   | +3 Minutes  | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL> | +3 Minutes  | Active    |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>  | +3 Minutes  | Cancelled |
      | Test Plan         | 3000         | USD            | month          | <EMAIL>  | +10 Minutes | Active    |
      | Test Two          | 30000        | USD            | year           | <EMAIL>   | +10 Minutes | Active    |

  Scenario:
    Given the following invoices exist:
      | Customer                 |
      | <EMAIL> |
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    When I view the invoice list
    Then I will see an invoice for "<EMAIL>"