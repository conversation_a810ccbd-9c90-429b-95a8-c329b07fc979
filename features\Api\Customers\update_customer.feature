Feature: Customer Update API
  In order to keep customer data up to date
  As an API user
  I need to be update a customer


  Scenario: Get customer info
    Given I have authenticated to the API
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
    When I update the customer info via the API for "<EMAIL>" with:
      | Email              | <EMAIL> |
      | Country            | GB                       |
      | External Reference | cust_4945959             |
      | Reference          | Test Customer            |
    Then the customer "<EMAIL>" should have the reference "Test Customer"

