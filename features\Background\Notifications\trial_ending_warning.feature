Feature: Trial Ending Warning

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
      | <PERSON>   | <EMAIL>   | AF@k3P@ss |
      | <PERSON> | <EMAIL> | AF@k3Pass |
    And the follow products exist:
      | Name        | External Reference |
      | Product One | prod_jf9j545       |
      | Product Two | prod_jf9j542       |
    And there are the following tax types:
      | Name             | Physical |
      | Digital Goods    | False    |
      | Digital Services | False    |
      | Physical         | True     |
    And that the following countries exist:
      | Name           | ISO Code | Threshold | Currency | In EU |
      | United Kingdom | GB       | 1770      | GBP      | False |
      | United States  | US       | 0         | USD      | False |
      | Germany        | DE       | 0         | EUR      | True  |
    And the following country tax rules exist:
      | Country        | Tax Type      | Tax Rate | Valid From |
      | United States  | Digital Goods | 17.5     | -10 days   |
      | United Kingdom | Digital Goods | 20       | -10 days   |
      | Germany        | Digital Goods | 17.5     | -10 days   |
      | Germany        | Physical      | 10.5     | -10 days   |
    And the follow prices exist:
      | Product     | Amount | Currency | Recurring | Schedule | Public |
      | Product One | 1000   | USD      | true      | week     | true   |
      | Product One | 3000   | USD      | true      | month    | true   |
      | Product One | 30000  | USD      | true      | year     | false  |
    And the following features exist:
      | Name          | Code          | Description     |
      | Feature One   | feature_one   | A dummy feature |
      | Feature Two   | feature_two   | A dummy feature |
      | Feature Three | feature_three | A dummy feature |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name             | Test Plan |
      | Public           | True      |
      | Per Seat         | False     |
      | User Count       | 10        |
      | Standalone Trial | true      |
    Given a Subscription Plan exists for product "Product One" with a feature "Feature One" and a limit for "Feature Two" with a limit of 10 and price "Price One" with:
      | Name       | Test Two |
      | Public     | True     |
      | Per Seat   | False    |
      | User Count | 10       |
    And the follow customers exist:
      | Email                      | Country | External Reference | Reference      | Billing Type | Payment Reference | Tax Number |
      | <EMAIL>   | DE      | cust_jf9j545       | Customer One   | invoice      | null              | FJDKSLfjdf |
      | <EMAIL>   | GB      | cust_dfugfdu       | Customer Two   | card         | ref_valid         | ssdfds     |
      | <EMAIL> | GB      | cust_mlklfdu       | Customer Three | card         | ref_valid         | gfdgsfd    |
      | <EMAIL>  | GB      | cust_dkkoadu       | Customer Four  | card         | ref_fails         | 35435 43   |
      | <EMAIL>  | GB      | cust_ddsjfu        | Customer Five  | card         | ref_valid         | dfadf      |
      | <EMAIL>   | GB      | cust_jliujoi       | Customer Six   | card         | ref_fails         | fdsafd     |


  Scenario: Send trial ending warning email
    Given that trial ending emails are to be sent
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status       |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +7 days     | trial_active |
    When the trial ending check is ran
    Then the trial ending warning email is sent to the customer

  Scenario: Do not send trial ending warning email
    Given that trial ending emails are not to be sent
    Given the following subscriptions exist:
      | Subscription Plan | Price Amount | Price Currency | Price Schedule | Customer                   | Next Charge | Status       |
      | Test Plan         | 1000         | USD            | week           | <EMAIL>   | +7 days     | trial_active |
    When the trial ending check is ran
    Then the trial ending warning email is not sent to the customer