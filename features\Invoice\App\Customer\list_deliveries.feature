Feature:

  Background:
    Given the following accounts exist:
      | Name        | Email                   | Password  |
      | <PERSON> | <EMAIL> | AF@k3P@ss |
    And the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | DE      | cust_jf9j54d       | Customer Two |

  Scenario:
    Given I have logged in as "<EMAIL>" with the password "AF@k3P@ss"
    And the following invoice delivery setups exist:
      | Customer                 | Type    | SFTP User | SFTP Password | SFTP Host   | SFTP Port | SFTP Dir | Webhook URL         | Webhook Method | Format |
      | <EMAIL> | Email   |           |               |             |           |          |                     |                | PDF    |
      | <EMAIL> | SFTP    | user      | password      | example.org | 2222      | .        |                     |                | PDF    |
      | <EMAIL> | Webhook |           |               |             |           |          | https://example.net | POST           | PDF    |
      | <EMAIL> | Webhook |           |               |             |           |          | https://example.org | POST           | PDF    |
    When I view the delivery methods for "<EMAIL>"
    Then I will see an invoice delivery for "Email"
    And I will see an invoice delivery for SFTP to SFTP Host "example.org"
    And I will see an invoice delivery for Webhook to Webhook URL "https://example.net"
    But I will not see an invoice delivery for Webhook to Webhook URL "https://example.org"
