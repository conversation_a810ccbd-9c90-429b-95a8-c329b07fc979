monolog:
    channels:
        - deprecation # Deprecations are logged in the dedicated "deprecation" channel when it exists\
        - obol
        - controller
        - audit

when@dev:
    monolog:
        handlers:
            main:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: debug
                channels: ["!event", "!audit"]
            rollbar:
                type: service
                id: BillaBear\Logger\Error\RollbarHandler
            # uncomment to get logging in your browser
            # you may have to allow bigger header sizes in your Web server configuration
            #firephp:
            #    type: firephp
            #    level: info
            #chromephp:
            #    type: chromephp
            #    level: info
            console:
                type: console
                process_psr_3_messages: false
                channels: ["!event", "!doctrine", "!console"]

when@test:
    monolog:
        handlers:
            main:
                type: fingers_crossed
                action_level: error
                handler: nested
                excluded_http_codes: [404, 405]
                channels: ["!event", "!audit"]
            nested:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: debug

when@prod:
    monolog:
        handlers:
            main:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: info
                channels: ["!event", "!deprecation", "!audit"]
            rollbar:
                type: service
                id: BillaBear\Logger\Error\RollbarHandler
