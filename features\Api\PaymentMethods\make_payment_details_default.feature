Feature: Customer Read APP
  In order to manage payment details
  As an API user
  I need to be see customer's payment details

  Background:
    Given the follow customers exist:
      | Email                    | Country | External Reference | Reference    |
      | <EMAIL> | DE      | cust_jf9j545       | Customer One |
      | <EMAIL> | UK      | cust_dfugfdu       | Customer Two |
    And the following payment details:
      | Customer Email           | Last Four | Expiry Month | Expiry Year | Name     |
      | <EMAIL> | 0444      | 03           | 25          | Card One |
      | <EMAIL> | 0444      | 03           | 25          | Card Two |

  Scenario: Get customer info
    Given I have authenticated to the API
    When I make the payment methods "Card One" for "<EMAIL>" default
    Then the payment details "Card One" for "<EMAIL>" should be default
    Then the payment details "Card Two" for "<EMAIL>" should not be default
