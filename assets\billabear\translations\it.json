{"global": {"loading": "Caricamento", "country": {"AU": "Australia", "BE": "Belgio", "CA": "Canada", "HR": "Croazia", "CZ": "Repubblica Ceca", "DK": "Danimarca", "EE": "Estonia", "FI": "Finlandia", "FR": "Francia", "DE": "Germania", "GR": "Grecia", "HU": "Ungheria", "IS": "Islanda", "LV": "<PERSON><PERSON><PERSON>", "LI": "Liechtenstein", "LT": "Lituania", "LU": "Lussemburgo", "GB": "Regno Unito", "US": "Stati Uniti", "NL": "<PERSON><PERSON>", "RO": "Romania", "SK": "Slovacchia", "SI": "Slovenia", "ES": "Spagna", "SE": "Svezia", "AF": "Afghanistan", "AL": "Albania", "DZ": "Algeria", "AD": "Andorra", "AO": "Angola", "AI": "<PERSON><PERSON><PERSON>", "AQ": "Antartide", "AG": "Antigua e Barbuda", "AR": "Argentina", "AM": "Armenia", "AW": "Aruba", "AT": "Austria", "AZ": "Azerbaigian", "BS": "Bahamas", "BH": "Bahrain", "BD": "Bangladesh", "BB": "Barbados", "BY": "Bielorussia", "BZ": "Belize", "BJ": "Benin", "BM": "Bermuda", "BT": "Bhutan", "BO": "Bolivia", "BA": "Bosnia ed <PERSON><PERSON><PERSON>", "BW": "Botswana", "BR": "Brasile", "IO": "Territorio britannico dell'Oceano Indiano", "BN": "Brunei Darussalam", "BG": "Bulgaria", "BF": "Burkina Faso", "BI": "Burundi", "CV": "Cabo Verde", "KH": "Cambogia", "CM": "<PERSON><PERSON>", "KY": "Isole Cayman", "CF": "Repubblica Centrafricana", "TD": "Chad", "CL": "Cile", "CN": "Cina", "CX": "Isola di Natale", "CC": "<PERSON><PERSON> (Keeling)", "CO": "Colombia", "KM": "Comore", "CG": "Congo", "CD": "Congo, Repubblica Democratica del", "CK": "<PERSON><PERSON>", "CR": "Costa Rica", "CI": "Costa d'Avorio", "CU": "Cuba", "CY": "Cipro", "DJ": "Gibuti", "DM": "Dominica", "DO": "Repubblica Dominicana", "EC": "Ecuador", "EG": "Egitto", "SV": "El Salvador", "GQ": "Guinea Equatoriale", "ER": "Eritrea", "SZ": "<PERSON><PERSON><PERSON><PERSON>", "ET": "Etiopia", "FK": "Isole Falkland (Malvinas)", "FO": "<PERSON><PERSON>", "FJ": "Figi", "GF": "Guiana francese", "PF": "Polinesia francese", "GA": "Gabon", "GM": "Gambia", "GE": "Georgia", "GH": "Ghana", "GI": "Gibilterra", "GL": "Groenlandia", "GD": "Grenada", "GP": "Guadalupa", "GT": "Guatemala", "GG": "Guernsey", "GN": "Guinea", "GW": "Guinea-Bissau", "GY": "Guyana", "HT": "Haiti", "HN": "Honduras", "HK": "Hong Kong", "IN": "India", "ID": "Indonesia", "IR": "Iran, Repubblica Islamica di", "IQ": "Iraq", "IE": "Irlanda", "IM": "Isola di Man", "IL": "<PERSON><PERSON>", "IT": "Italia", "JM": "Giamaic<PERSON>", "JP": "Giappone", "JE": "Maglia", "JO": "Giordania", "KZ": "Kazakistan", "KE": "Kenya", "KI": "Kiribati", "KP": "Corea, Repubblica Popolare Democratica di", "KR": "Corea, Repubblica di", "KW": "Kuwait", "KG": "Kirghizistan", "LA": "Repubblica Democratica Popolare del Laos", "LB": "Libano", "LS": "Lesotho", "LR": "Liberia", "LY": "Libia", "MO": "Macao", "MG": "Madagascar", "MW": "Malawi", "MY": "Malesia", "MV": "Maldive", "ML": "Mali", "MT": "Malta", "MH": "<PERSON><PERSON>", "MQ": "Martinica", "MR": "Mauritania", "MU": "Mauritius", "YT": "Mayotte", "MX": "Messico", "FM": "Micronesia, Stati Federati di", "MD": "Moldavia, Repubblica di", "MC": "Monaco", "MN": "Mongolia", "ME": "Montenegro", "MS": "Montserrat", "MA": "Marocco", "MZ": "Mozambico", "MM": "Myanmar", "NA": "Namibia", "NR": "Nauru", "NP": "Nepal", "NC": "Nuova Caledonia", "NZ": "Nuova Zelanda", "NI": "Nicaragua", "NE": "Niger", "NG": "Nigeria", "NU": "Niue", "NF": "Isola di Norfolk", "MK": "Macedonia del Nord", "NO": "Norvegia", "OM": "Oman", "PK": "Pakistan", "PW": "<PERSON><PERSON>", "PS": "Palestina, Stato di", "PA": "Panama", "PG": "Papua Nuova Guinea", "PY": "Paraguay", "PE": "<PERSON><PERSON>", "PH": "<PERSON><PERSON><PERSON>", "PN": "Pitcairn", "PL": "Polonia", "PT": "Portogallo", "QA": "Qatar", "RE": "Riunione", "RU": "Federazione Russa", "RW": "<PERSON><PERSON><PERSON>", "BL": "<PERSON>", "SH": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> e <PERSON>", "KN": "Saint Kitts e Nevis", "LC": "Santa Lucia", "MF": "<PERSON> (parte francese)", "PM": "Saint-<PERSON>on", "VC": "Saint Vincent e Grenadine", "WS": "Samoa", "SM": "San Marino", "ST": "São Tomé e Principe", "SA": "Arabia Saudita", "SN": "Senegal", "RS": "Serbia", "SC": "Seychelles", "SL": "Sierra Leone", "SG": "Singapore", "SX": "<PERSON><PERSON> (parte olandese)", "SB": "Isole Salomone", "SO": "Somalia", "ZA": "Sudafrica", "GS": "Georgia del Sud e Isole Sandwich del Sud", "SS": "Sud Sudan", "LK": "Sri Lanka", "SD": "Sudan", "SR": "Suriname", "SJ": "Svalbard <PERSON>", "CH": "Svizzera", "SY": "Repubblica Araba Siriana", "TW": "Taiwan, provincia della Cina", "TJ": "Tagikistan", "TZ": "Tanzania, Repubblica Unita di", "TH": "Thailandia", "TL": "Timor Est", "TG": "Togo", "TK": "Tokelau", "TO": "Tonga", "TT": "Trinidad e Tobago", "TN": "Tunisia", "TR": "Tu<PERSON><PERSON>", "TM": "Turkmenistan", "TC": "Isole Turks e Caicos", "TV": "Tuvalu", "UG": "Uganda", "UA": "Ucraina", "AE": "Emirati Arabi Uniti", "UM": "Isole minori degli Stati Uniti", "UY": "Uruguay", "UZ": "Uzbekistan", "VU": "Vanuatu", "VE": "Venezuela", "VN": "Vietnam", "VG": "<PERSON><PERSON>gini britanniche", "VI": "<PERSON><PERSON>, Stati Uniti", "WF": "Wallis e Futuna", "EH": "Sahara occidentale", "YE": "Yemen", "ZM": "Zambia", "ZW": "Zimbabwe"}, "select_country": "Selezionare il Paese"}, "public": {"login": {"title": "Accesso", "email": "Email", "password": "Password", "login_button": "Accesso", "remember_me_label": "Ricordati di me", "forgot_password_link": "Avete dimenticato la password?", "signup_link": "Registrazione di un account", "logging_in": "Accesso"}, "signup": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "Email", "email_error": "L'e-mail deve essere fornita", "email_invalid_error": "È necessario fornire un'e-mail valida", "password": "Password", "password_error": "La password deve essere fornita", "password_confirm": "Conferma la password", "password_confirm_error": "La password deve corrispondere", "signup_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signing_up": "In corso", "remember_me_label": "Ricordati di me", "forgot_password_link": "Avete dimenticato la password?", "login_link": "Hai già un account? Effettua il login ora.", "success_message": "L'iscrizione è avvenuta con successo. Controlla la tua e-mail."}, "forgot_password": {"title": "Reimpostare la password", "email": "Email", "email_error": "È necessario fornire un'e-mail.", "in_progress": "In corso", "login_link": "Ricorda la tua password? Accesso", "success_message": "Controllare la posta elettronica", "request_button": "Reimpostare la password"}, "forgot_password_confirm": {"title": "Reimpostare la password", "password": "Password", "password_error": "È necessario fornire una password.", "password_length_error": "La password deve essere composta da almeno 7 caratteri", "password_confirm": "<PERSON><PERSON><PERSON>", "password_confirm_error": "Le <PERSON> devono corrispondere", "reset_button": "Reimpostare la password", "in_progress": "In corso", "login_link": "Fare clic qui per accedere.", "success_message": "La password è stata ripristinata. Ora è possibile effettuare il login.", "request_button": "Reimpostare la password"}, "confirm_email": {"error_message": "Questo è un link non valido", "success_message": "L'e-mail è stata confermata ed è possibile effettuare il login.", "login_link": "Fare clic qui per accedere."}}, "app": {"menu": {"main": {"reports": "Rapporti", "subscriptions": "Abbonamenti", "finance": "Finanza", "settings": "Impostazioni", "customers": "Clienti", "products": "<PERSON><PERSON>tti", "invoices": "Fatture", "system": "Sistema", "docs": "Documentazione", "workflows": "Flussi di lavoro", "developers": "Sviluppatori", "home": "Casa", "customer_list": "Elenco clienti", "mobile": {"show": "Mostra menu", "hide": "Nascondi menu"}, "tax": "Imposta", "customer_support_integrations": "Integrazioni di supporto"}}, "team": {"main": {"title": "Impostazioni del team", "add_team_member": "Aggiungi un membro del team"}, "invite": {"title": "Aggiungi un membro del team", "close": "<PERSON><PERSON><PERSON>", "email": "Email", "invite_successfully_sent": "L'invito è stato inviato con successo.", "send": "Inviare l'invito", "sending": "Invio", "send_another": "Invia un altro"}, "pending_invites": {"title": "<PERSON><PERSON>ti in sospeso", "none": "Non ci sono inviti in sospeso", "email": "Email", "invited_at": "Invitato a", "cancel": "Annullamento", "cancelling": "Annullamento"}, "members": {"email": "Email", "created_at": "Iscritta a", "disable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabling": "In corso", "active": "Attivo", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "plan": {"main": {"title": "Piano", "payment_schedule_yearly": "Annuale", "payment_schedule_monthly": "<PERSON><PERSON><PERSON>", "payment_schedule_label": "Piano di pagamento", "select_plan": "Sele<PERSON><PERSON><PERSON> il piano", "selected_plan": "Attualmente attivo", "change": "Modifica del piano", "payment_settings": "Impostazioni di pagamento", "cancel_button": "Annullamento", "in_progress": "Elaborazione", "features": "<PERSON><PERSON><PERSON><PERSON>", "your_current_plan": "Il vostro piano attuale", "plan_options": "Opzioni del piano"}}, "user": {"settings": {"title": "Impostazioni utente", "name": "Nome", "email": "Posta elettronica", "password": "Password", "locale": "Locale", "save": "Risparmiare", "error_message": "Si è verificato un problema nel salvataggio delle impostazioni dell'utente. Controllare gli errori.", "success_message": "Le impostazioni sono state salvate con successo.", "danger_zone": "Zona di pericolo", "current_password": "Password corrente", "new_password": "Nuova password", "new_password_again": "Conferma la password", "change_password": "Modifica della password", "need_current_password": "È necessario fornire la password attuale", "need_new_password": "È necessario fornire una nuova password", "need_valid_password": "La password deve essere composta da più di 8 caratteri", "need_password_to_match": "Le <PERSON> devono corrispondere", "in_progress": "In corso"}, "invite": {"title": "Invita l'utente", "email": "E-mail", "send": "Inviare", "in_progress": "In corso", "success_message": "Invito inviato con successo!", "need_email": "È necessario fornire un'e-mail", "error_message": "Impossibile inviare l'invito.", "role": "<PERSON><PERSON><PERSON>"}}, "billing": {"details": {"title": "Dettagli di fatturazione", "street_line_one": "Linea stradale uno", "street_line_two": "Linea stradale due", "city": "Città", "region": "Stato", "country": "<PERSON><PERSON>", "postal_code": "Codice postale", "submit": "Risparmiare"}, "main": {"title": "Fatturazione", "details": "Dettagli di fatturazione", "methods": "Metodi di pagamento", "invoices": "Fatture"}, "card_form": {"name": "Nome", "number": "Numero di carta", "exp_month": "Mese di scadenza", "exp_year": "Anno di scadenza", "cvc": "Codice di sicurezza", "add_card": "Aggiungi scheda"}, "payment_methods": {"title": "Metodi di pagamento", "card_number": "Numero", "card_expiry": "Data di scadenza della carta", "is_default": "Metodo di pagamento predefinito", "make_default_btn": "Rendere predefinito", "delete_btn": "Cancellare", "add_card_btn": "Aggiungi una nuova scheda", "no_saved_payment_methods": "<PERSON><PERSON><PERSON> metodo di pagamento sicuro"}}, "customer": {"list": {"title": "Clienti", "email": "Email", "country": "<PERSON><PERSON>", "reference": "Riferimento", "no_customers": "Attualmente non ci sono clienti esistenti", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view_btn": "Vista", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "no_filters": "<PERSON><PERSON><PERSON> filtro", "country": "<PERSON><PERSON>", "company_name": "Nome della società"}, "loading": "Risultati del caricamento", "error_message": "Si è verificato un errore", "company_name": "Nome della società"}, "create": {"title": "Creare un nuovo cliente", "email": "Email", "street_line_one": "Linea stradale 1", "street_line_two": "Linea stradale 2", "city": "Città", "region": "Regione", "country": "<PERSON><PERSON>", "post_code": "Codice postale", "reference": "Riferimento", "external_reference": "Riferimento esterno", "advance": "anticipo", "submit_btn": "<PERSON>rea cliente", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Cliente creato con successo", "address_title": "<PERSON><PERSON><PERSON><PERSON>", "locale": "Locale", "billing_type": "Tipo di fatturazione", "billing_type_card": "Scheda", "billing_type_invoice": "Fattura", "company_name": "Nome della società", "brand": "<PERSON><PERSON>", "tax_number": "Codice fiscale", "standard_tax_rate": "Aliquota fiscale standard", "type": "Tipo di cliente", "type_business": "A<PERSON><PERSON>", "type_individual": "Individuale", "help_info": {"email": "L'email del cliente a cui devono essere inviate le fatture", "locale": "Il locale da utilizzare per la lingua", "company": "Il nome dell'azienda", "street_line_one": "La prima riga dell'indirizzo di fatturazione", "street_line_two": "La seconda riga dell'indirizzo di fatturazione stradale", "city": "La città per l'indirizzo di fatturazione", "region": "La regione/Stato per l'indirizzo di fatturazione", "country": "Paese di fatturazione del cliente - codice paese ISO 3166-1 alpha-2.", "post_code": "Il codice postale dell'indirizzo di fatturazione", "reference": "Il vostro riferimento interno per il cliente", "billing_type": "Come deve essere fatturata al cliente. Carta significa che i pagamenti saranno automatici tramite una carta registrata. Fattura significa che il cliente riceve una fattura e paga manualmente", "external_reference": "Il riferimento del cliente utilizzato dal fornitore del pagamento. Las<PERSON><PERSON> vuoto a meno che non si sia estremamente sicuri di avere il riferimento corretto.", "brand": "Il marchio a cui il cliente appartiene.", "tax_number": "Il codice fiscale del cliente", "standard_tax_rate": "L'aliquota d'imposta da applicare per il cliente per tutto ciò che non è servizio digitale", "type": "Se il cliente è un'azienda o un privato", "invoice_format": "Il formato da utilizzare per la creazione e la consegna di una fattura."}, "failed_message": "Impossibile creare un cliente", "invoice_format": "Formato della fattura", "metadata": {"title": "<PERSON><PERSON><PERSON>", "name": "Nome", "value": "Valore", "no_values": "<PERSON><PERSON><PERSON> valore di metadati", "add": "Aggiungere metadati"}}, "view": {"title": "Visualizza i dettagli del cliente", "update": "Aggiornamento", "disable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enable": "Abilitazione", "error": {"not_found": "Nessun cliente trovato", "unknown": "Si è verificato un errore sconosciuto"}, "main": {"title": "Dettagli principali", "email": "Email", "reference": "Riferimento interno", "external_reference": "Riferimento esterno", "status": "Stato", "locale": "Locale", "brand": "<PERSON><PERSON>", "billing_type": "Tipo di fatturazione", "tax_number": "Codice fiscale", "standard_tax_rate": "Aliquota fiscale standard", "type": "Tipo", "marketing_opt_in": "Marketing Opt In"}, "address": {"company_name": "Nome della società", "title": "<PERSON><PERSON><PERSON><PERSON>", "street_line_one": "Linea stradale 1", "street_line_two": "Linea stradale 2", "city": "Città", "region": "Regione", "post_code": "Codice postale", "country": "<PERSON><PERSON>"}, "credit_notes": {"title": "Note di credito", "list": {"amount": "Importo", "currency": "Valuta", "created_by": "<PERSON><PERSON><PERSON> <PERSON>", "created_at": "<PERSON>reato a"}, "no_credit_notes": "Nessuna nota di credito per questo cliente"}, "credit": {"title": "Rettifiche di credito", "list": {"amount": "Importo", "currency": "Valuta", "created_by": "<PERSON><PERSON><PERSON> <PERSON>", "created_at": "<PERSON>reato a"}, "no_credit": "<PERSON><PERSON>un credito per questo cliente", "add_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "subscriptions": {"title": "Abbonamenti", "list": {"plan_name": "Piano", "status": "Stato", "schedule": "Programma", "created_at": "<PERSON>reato a", "valid_until": "Il prossimo fatturato", "view": "Vista"}, "add_new": "Aggiungi un nuovo abbonamento", "no_subscriptions": "<PERSON><PERSON><PERSON> a<PERSON>"}, "subscription_events": {"title": "Eventi in abbonamento", "list": {"event": "Evento", "subscription": "Abbonamento", "created_at": "<PERSON>reato a"}, "no_subscription_events": "<PERSON><PERSON>un evento in abbonamento"}, "payments": {"title": "<PERSON><PERSON><PERSON>", "list": {"amount": "Importo", "currency": "Valuta", "status": "Stato", "created_at": "<PERSON>reato a"}, "no_payments": "Non ci sono ancora pagamenti per questo cliente"}, "refunds": {"title": "<PERSON><PERSON><PERSON><PERSON>", "list": {"amount": "Importo", "currency": "Valuta", "created_by": "<PERSON><PERSON><PERSON> <PERSON>", "created_at": "<PERSON>reato a"}, "no_refunds": "<PERSON><PERSON><PERSON> per questo cliente"}, "payment_details": {"title": "Dettagli sul pagamento", "list": {"brand": "<PERSON><PERSON>", "last_four": "<PERSON>lt<PERSON><PERSON> quattro", "default": "Pagamento predefinito", "expiry_month": "Mese di scadenza", "expiry_year": "Anno di scadenza", "name": "Nome"}, "add_token": "Con gettone", "add_new": "Aggiungi nuovo", "no_payment_details": "<PERSON><PERSON><PERSON> di pagamento", "delete": "Cancellare", "make_default": "Rendere predefinito"}, "limits": {"title": "Limiti", "list": {"feature": "Caratteristica", "limit": "Limite"}, "no_limits": "<PERSON><PERSON>"}, "features": {"title": "<PERSON><PERSON><PERSON><PERSON>", "list": {"feature": "Caratteristica"}, "no_features": "<PERSON><PERSON><PERSON> caratteristica"}, "invoices": {"title": "Fatture", "list": {"amount": "Importo", "currency": "Valuta", "status": "Stato", "outstanding": "Eccezionale", "overdue": "In ritardo", "paid": "<PERSON><PERSON><PERSON>", "created_at": "<PERSON>reato a", "view_btn": "Vista"}, "no_invoices": "Nessuna fattura", "next": "<PERSON><PERSON>", "prev": "Precedente"}, "invoice_delivery": {"title": "Consegna della fattura", "add_new": "Aggiungi nuovo", "list": {"method": "<PERSON><PERSON><PERSON>", "format": "Formato", "detail": "Dettaglio", "view": "Vista"}, "no_delivery_methods": "<PERSON><PERSON><PERSON> metodo di consegna"}, "metric_counters": {"title": "Contatori metrici", "list": {"name": "Nome", "usage": "<PERSON><PERSON><PERSON><PERSON>", "cost": "Costo stimato"}, "no_counters": "Non ci sono contatori metrici"}, "usage_limits": {"title": "Limiti di utilizzo", "add_new": "Aggiungi nuovo", "list": {"amount": "Importo", "warn_level": "Azione"}, "warn_levels": {"warn": "Avvertire", "disable": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "no_limits": "Non ci sono limiti di utilizzo per questo cliente"}, "metadata": {"title": "<PERSON><PERSON><PERSON>", "no_metadata": "<PERSON><PERSON><PERSON> metadato"}, "audit_log": "Registro di controllo"}, "update": {"title": "Aggiornamento del cliente", "email": "Email", "street_line_one": "Linea stradale 1", "street_line_two": "Linea stradale 2", "city": "Città", "region": "Regione", "country": "<PERSON><PERSON>", "post_code": "Codice postale", "reference": "Riferimento", "company_name": "Nome della società", "external_reference": "Riferimento esterno", "advance": "anticipo", "submit_btn": "Aggiornato", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Aggiornare con successo il cliente", "address_title": "<PERSON><PERSON><PERSON><PERSON>", "tax_number": "Codice fiscale", "standard_tax_rate": "Aliquota fiscale standard", "locale": "Locale", "error": {"not_found": "Nessun cliente trovato", "unknown": "Si è verificato un errore sconosciuto"}, "billing_type": "Tipo di fatturazione", "billing_type_card": "Scheda", "billing_type_invoice": "Fattura", "type": "Tipo di cliente", "type_business": "A<PERSON><PERSON>", "type_individual": "Individuale", "help_info": {"email": "L'email del cliente a cui devono essere inviate le fatture", "locale": "Il locale da utilizzare per la lingua", "company_name": "Il nome dell'azienda", "street_line_one": "La prima riga dell'indirizzo di fatturazione", "street_line_two": "La seconda riga dell'indirizzo di fatturazione stradale", "city": "La città per l'indirizzo di fatturazione", "region": "La regione/Stato per l'indirizzo di fatturazione", "country": "Paese di fatturazione del cliente - codice paese ISO 3166-1 alpha-2.", "post_code": "Il codice postale dell'indirizzo di fatturazione", "reference": "Il vostro riferimento interno per il cliente", "billing_type": "Come deve essere fatturata al cliente. Carta significa che i pagamenti saranno automatici tramite una carta registrata. Fattura significa che il cliente riceve una fattura e paga manualmente", "external_reference": "Il riferimento del cliente utilizzato dal fornitore del pagamento. Las<PERSON><PERSON> vuoto a meno che non si sia estremamente sicuri di avere il riferimento corretto.", "tax_number": "Il codice fiscale del cliente", "standard_tax_rate": "L'aliquota d'imposta da applicare per il cliente per tutto ciò che non è servizio digitale", "type": "Se il cliente è un'azienda o un privato", "invoice_format": "Il formato da utilizzare per la creazione e la consegna di una fattura.", "marketing_opt_in": "Se il cliente ha scelto di ricevere e-mail di marketing. Questo riguarda le integrazioni di newsletter."}, "invoice_format": "Formato della fattura", "marketing_opt_in": "Marketing Opt In", "metadata": {"title": "<PERSON><PERSON><PERSON>", "name": "Nome", "value": "Valore", "no_values": "<PERSON><PERSON><PERSON> valore di metadati", "add": "Aggiungere metadati"}}, "menu": {"title": "Clienti", "customers": "Clienti"}}, "product": {"list": {"title": "<PERSON><PERSON>tti", "name": "Nome", "physical": "Fisico", "no_products": "Al momento non ci sono prodotti esistenti", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "filter": {"title": "<PERSON><PERSON><PERSON>", "name": "Nome", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Si è verificato un errore"}, "create": {"title": "<PERSON><PERSON>re un nuovo prodotto", "name": "Nome", "external_reference": "Riferimento esterno", "advance": "anticipo", "submit_btn": "<PERSON><PERSON> prodotto", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "<PERSON><PERSON><PERSON> creato con successo", "failed_message": "Impossibile creare il prodotto", "tax_rate": "Aliquota fiscale", "tax_type": "Tipo di imposta", "physical": "Fisico", "tax_types": {"digital_services": "Servizi digitali", "digital_goods": "Beni digitali", "physical": "<PERSON>i/servizi fisici"}, "help_info": {"name": "Il nome del prodotto", "external_reference": "Il riferimento del prodotto utilizzato dal fornitore del pagamento. Las<PERSON><PERSON> vuoto a meno che non si sia estremamente sicuri di avere il riferimento corretto.", "tax_type": "Questo per aiutare a tassare correttamente. I beni e i servizi fisici sono tassati in modo diverso dai beni digitali. E in alcuni Paesi esiste una tassa sui servizi digitali.", "tax_rate": "L'aliquota fiscale da utilizzare per questo prodotto. Essa prevarrà sulle altre aliquote fiscali.", "physical": "Questo prodotto è fisico?"}}, "view": {"title": "Visualizza i dettagli del prodotto", "update": "Aggiornamento", "error": {"not_found": "<PERSON><PERSON><PERSON> prodotto trovato", "unknown": "Si è verificato un errore sconosciuto"}, "main": {"title": "Dettagli principali", "name": "Nome", "physical": "Fisico", "external_reference": "Riferimento esterno", "tax_rate": "Aliquota fiscale", "tax_type": "Tipo di imposta", "tax_types": {"digital_services": "Servizi digitali", "digital_goods": "Beni digitali", "physical": "<PERSON>i/servizi fisici"}}, "price": {"title": "<PERSON><PERSON>", "create": "<PERSON>rea nuovo prezzo", "no_prices": "Al momento non ci sono prezzi", "hide": "Rendere il prezzo privato", "show": "Rendere pubblico il prezzo", "list": {"amount": "Importo", "currency": "Valuta", "recurring": "Pagamento ricorrente", "schedule": "Piano di pagamento", "including_tax": "Il prezzo include le tasse", "public": "Prezzo al pubblico", "external_reference": "Riferimento esterno", "usage": "<PERSON><PERSON><PERSON><PERSON>"}}, "subscription_plan": {"title": "Piani di abbonamento", "create": "<PERSON><PERSON><PERSON> un nuovo piano", "no_subscription_plans": "Al momento non ci sono piani di abbonamento", "view": "Vista", "list": {"name": "Nome", "external_reference": "Riferimento esterno", "code_name": "Nome del codice"}}}, "update": {"title": "Aggiornamento del prodotto", "name": "Nome", "external_reference": "Riferimento esterno", "advance": "anticipo", "submit_btn": "Aggiornato", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Aggiornamento del prodotto riuscito", "address_title": "<PERSON><PERSON><PERSON><PERSON>", "error": {"not_found": "<PERSON><PERSON><PERSON> prodotto trovato", "unknown": "Si è verificato un errore sconosciuto"}, "tax_type": "Tipo di imposta", "tax_types": {"digital_services": "Servizi digitali", "digital_goods": "Beni digitali", "physical": "<PERSON>i/servizi fisici"}, "tax_rate": "Aliquota fiscale", "help_info": {"name": "Il nome del prodotto", "external_reference": "Il riferimento del prodotto utilizzato dal fornitore del pagamento. Las<PERSON><PERSON> vuoto a meno che non si sia estremamente sicuri di avere il riferimento corretto.", "tax_type": "Questo per aiutare a tassare correttamente. I beni e i servizi fisici sono tassati in modo diverso dai beni digitali. E in alcuni Paesi esiste una tassa sui servizi digitali.", "tax_rate": "L'aliquota fiscale da utilizzare per questo prodotto. Essa prevarrà sulle altre aliquote fiscali."}}, "menu": {"title": "<PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON>tti", "features": "<PERSON><PERSON><PERSON><PERSON>", "vouchers": "<PERSON><PERSON><PERSON>", "products_list": "Elenco dei prodotti", "metrics": "<PERSON><PERSON><PERSON>"}}, "price": {"create": {"title": "<PERSON>rea nuovo prezzo", "amount": "Importo", "external_reference": "Riferimento esterno", "advance": "anticipo", "submit_btn": "<PERSON><PERSON><PERSON> il prezzo", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Prezzo creato con successo", "schedule_label": "Piano di pagamento", "currency": "Valuta", "recurring": "È ricorrente?", "including_tax": "Il prezzo include le tasse?", "public": "Pubblico", "help_info": {"amount": "Il prezzo è la valuta del livello minore. Quindi 1,00 USD corrisponde a 100 e 9,99 a 999.", "display_amount": "Il prezzo sarebbe {amount}.", "external_reference": "Il riferimento del prodotto utilizzato dal fornitore del pagamento. Las<PERSON><PERSON> vuoto a meno che non si sia estremamente sicuri di avere il riferimento corretto.", "recurring": "Se si tratta di un pagamento ricorrente o una tantum.", "currency": "La valuta di addebito al cliente", "schedule": "Con quale frequenza deve essere addebitato al cliente", "including_tax": "Se si vuole nascondere l'imposta all'interno del prezzo o se si vuole far pagare l'imposta al cliente stesso", "public": "Se si tratta di un prezzo esposto al pubblico", "usage": "Se il cliente viene fatturato in base all'utilizzo di una metrica o in base al posto.", "metric_type": "Se la metrica di utilizzo viene azzerata alla fine del piano di pagamento e viene utilizzata completamente per la fatturazione o in modo continuo e viene utilizzata la differenza tra l'ultima fattura e la fattura successiva."}, "schedule": {"week": "<PERSON><PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON>", "year": "Annuale"}, "metric": "Metric<PERSON>", "metric_type": "Tipo metrico", "create_metric": "È necessario creare una metrica", "metric_types": {"resettable": "Azzerabile", "continuous": "Continuo"}, "type": "Tipo", "types": {"fixed_price": "Prezzo fisso", "package": "<PERSON><PERSON><PERSON>", "per_unit": "Per unità/Per posto", "tiered_volume": "Volume a livelli", "tiered_graduated": "Grad<PERSON><PERSON> a livelli"}, "usage": "<PERSON><PERSON><PERSON><PERSON>", "units": "Unità", "tiers": "<PERSON><PERSON>", "tiers_fields": {"first_unit": "Prima unità", "last_unit": "Ultima unità", "unit_price": "Prezzo unitario", "flat_fee": "<PERSON><PERSON> for<PERSON>"}}}, "feature": {"list": {"title": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nome", "code": "Codice", "reference": "Riferimento", "no_features": "Al momento non ci sono funzioni esistenti", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Si è verificato un errore", "loading": "Caratteristiche di caricamento"}, "create": {"title": "C<PERSON>re una nuova funzione", "advance": "anticipo", "submit_btn": "Crea funzione", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Funzione creata con successo", "address_title": "<PERSON><PERSON><PERSON><PERSON>", "fields": {"name": "Nome", "code": "Nome in codice", "description": "Descrizione"}, "help_info": {"name": "Il nome della funzione", "code": "Il nome in codice della funzione. Viene utilizzato quando si registra un utilizzo o si controllano i limiti.", "description": "La descrizione della funzione"}}}, "subscription_plan": {"create": {"title": "<PERSON><PERSON>re un nuovo piano di abbonamento", "main_section": {"title": "Dettagli principali", "fields": {"name": "Nome", "code_name": "Nome del codice", "user_count": "Conteggio degli utenti", "public": "Piano disponibile al pubblico", "per_seat": "Per posto a sedere", "free": "<PERSON><PERSON><PERSON><PERSON>"}, "help_info": {"name": "Il nome del piano di abbonamento", "code_name": "Il nome in codice del piano da utilizzare con l'API.", "user_count": "Il numero di utenti consentito per questo piano", "public": "Il piano è disponibile al pubblico o è un piano personalizzato?", "free": "Si tratta di un piano gratuito?", "per_seat": "Il piano viene addebitato per ogni posto?"}}, "trial_section": {"title": "Dettagli del processo", "fields": {"has_trial": "Ha un processo", "is_trial_standalone": "Il processo è autonomo", "trial_length_days": "Durata del processo in giorni"}, "help_info": {"has_trial": "Se il piano ha un periodo di prova predefinito", "trial_length_days": "Quanto deve durare il processo in giorni", "is_trial_standalone": "Se una prova è indipendente, non ha bisogno di un prezzo e l'abbonamento si interrompe al termine della prova"}}, "features_section": {"title": "<PERSON><PERSON><PERSON><PERSON>", "columns": {"feature": "Caratteristica", "description": "Descrizione"}, "create": {"name": "Nome", "code_name": "Nome del codice", "description": "Descrizione", "button": "<PERSON><PERSON><PERSON>"}, "add_feature": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "existing": "Caratteristiche esistenti", "new": "Crea nuovo", "no_features": "<PERSON><PERSON><PERSON> caratteristica"}, "limits_section": {"title": "Limiti", "columns": {"limit": "Limite", "feature": "Caratteristica", "description": "Descrizione"}, "fields": {"limit": "Limite", "feature": "Caratteristica"}, "add_limit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "no_limits": "<PERSON><PERSON>"}, "prices_section": {"title": "<PERSON><PERSON>", "columns": {"amount": "Importo", "currency": "Valuta", "schedule": "Programma"}, "create": {"amount": "Importo", "currency": "Valuta", "recurring": "<PERSON><PERSON><PERSON>", "schedule": "Programma", "including_tax": "Inclusa l'imposta", "public": "Pubblico", "button": "<PERSON><PERSON><PERSON>"}, "add_price": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "existing": "<PERSON><PERSON> esistenti", "new": "Crea nuovo", "no_prices": "<PERSON><PERSON><PERSON>zzo"}, "submit_btn": "<PERSON><PERSON><PERSON> un piano"}, "view": {"title": "Visualizza i dettagli del piano di abbonamento", "update": "Aggiornamento", "error": {"not_found": "Nessun piano di abbonamento trovato", "unknown": "Si è verificato un errore sconosciuto"}, "main": {"title": "Dettagli principali", "name": "Nome", "code_name": "Nome del codice", "per_seat": "Per posto a sedere", "free": "<PERSON><PERSON><PERSON><PERSON>", "user_count": "Conteggio degli utenti", "public": "Disponibile al pubblico", "has_trial": "Ha un processo", "trial_length_days": "<PERSON><PERSON> della prova", "is_trial_standalone": "Il processo è autonomo?"}, "limits": {"title": "Limiti", "list": {"feature": "Caratteristica", "limit": "Limite", "no_limits": "N<PERSON>un limite"}}, "features": {"title": "<PERSON><PERSON><PERSON><PERSON>", "list": {"feature": "Caratteristica", "no_features": "<PERSON><PERSON><PERSON> caratteristica"}}, "price": {"title": "<PERSON><PERSON>", "list": {"amount": "Importo", "currency": "Valuta", "recurring": "Pagamento ricorrente", "schedule": "Piano di pagamento", "including_tax": "Il prezzo include le tasse", "public": "Prezzo al pubblico", "external_reference": "Riferimento esterno", "usage": "<PERSON><PERSON><PERSON><PERSON>"}}}, "update": {"title": "Aggiornamento del piano di abbonamento", "advance": "anticipo", "submit_btn": "Aggiornamento del piano di abbonamento", "show_advanced": "<PERSON><PERSON><PERSON>", "success_message": "Piano di abbonamento aggiornato con successo", "address_title": "<PERSON><PERSON><PERSON><PERSON>", "fields": {"name": "Nome", "code_name": "Nome del codice", "user_count": "Conteggio degli utenti", "public": "Piano disponibile al pubblico", "per_seat": "Per posto a sedere", "free": "<PERSON><PERSON><PERSON><PERSON>", "prices": "<PERSON><PERSON>", "features": "<PERSON><PERSON><PERSON><PERSON>", "limits": "Limiti", "has_trial": "Ha un processo", "trial_length_days": "<PERSON><PERSON> della prova", "is_trial_standalone": "Il processo è autonomo?"}, "help_info": {"name": "Il nome del piano", "code_name": "Il nome in codice del piano da utilizzare con l'API.", "user_count": "Il numero di utenti consentito per questo piano", "public": "Il piano è disponibile al pubblico o è un piano personalizzato?", "free": "Si tratta di un piano gratuito?", "per_seat": "Il piano viene addebitato per ogni posto?", "has_trial": "Se il piano ha un periodo di prova predefinito", "trial_length_days": "Quanto deve durare il processo in giorni", "is_trial_standalone": "Se una prova è indipendente, non ha bisogno di un prezzo e l'abbonamento si interrompe al termine della prova"}, "features": {"title": "<PERSON><PERSON><PERSON><PERSON>", "add_feature": "Aggiungi funzione"}, "limits": {"title": "Limiti", "add_limit": "Aggiungi limiti"}, "prices": {"title": "<PERSON><PERSON>", "add_price": "<PERSON><PERSON><PERSON><PERSON><PERSON> prezzo"}}, "menu": {"subscription_plans": "Piani di abbonamento", "products": "<PERSON><PERSON>tti", "features": "<PERSON><PERSON><PERSON><PERSON>"}}, "payment_details": {"add": {"title": "Aggiungi dettagli di pagamento"}, "add_with_token": {"title": "Aggiungere il dettaglio del pagamento con il token", "field": {"token": "Gettone"}, "help_info": {"token": "Il token fornito da Stripe."}, "submit": "Invia"}}, "subscription": {"create": {"title": "Creare una nuova sottoscrizione", "subscription_plans": "Piani di abbonamento", "payment_details": "Dettagli sul pagamento", "no_eligible_prices": "Non ci sono prezzi ammissibili", "prices": "<PERSON><PERSON>", "success_message": "Abbonamento creato con successo", "submit_btn": "<PERSON><PERSON><PERSON>", "trial": "<PERSON><PERSON> gratuita", "trial_length_days": "Numero di giorni", "unknown_error": "Si è verificato un errore sconosciuto durante la creazione", "seats": "Numero di posti a sedere", "help_info": {"eligible_prices": "Se un cliente ha già un abbonamento attivo, i nuovi abbonamenti devono riguardare lo stesso periodo di fatturazione e la stessa valuta.", "trial": "Se un cliente ha già un abbonamento attivo, non ha diritto a un'altra prova gratuita.", "no_trial": "Questo piano non ha una prova gratuita", "seats": "Il numero di posti a sedere per cui l'abbonamento deve essere sottoscritto"}}, "view": {"title": "Visualizza l'abbonamento", "main": {"title": "Dati di abbonamento", "status": "Stato", "plan": "Piano", "plan_change": "Piano di cambiamento", "customer": "Cliente", "main_external_reference": "Riferimento esterno principale", "created_at": "<PERSON>reato a", "ended_at": "Terminato a", "valid_until": "Valido fino a", "seat_number": "Numero di sedile", "change_seat": "Cambiare i posti a sedere"}, "pricing": {"title": "<PERSON><PERSON>", "price": "Prezzo", "recurring": "<PERSON><PERSON><PERSON>", "schedule": "Programma", "change": "Cambiamento", "no_price": "<PERSON><PERSON><PERSON> prezzo fissato per l'abbonamento"}, "payments": {"title": "<PERSON><PERSON><PERSON>", "amount": "Importo", "created_at": "<PERSON>reato a", "view": "Vista", "no_payments": "Non ci sono ancora pagamenti"}, "payment_method": {"title": "Metodo di pagamento", "last_four": "<PERSON>lt<PERSON><PERSON> quattro", "expiry_month": "Mese di scadenza", "expiry_year": "Anno di scadenza", "brand": "Tipo di carta", "invoiced": "<PERSON><PERSON><PERSON>"}, "subscription_events": {"title": "Eventi in abbonamento", "list": {"event": "Evento", "subscription": "Abbonamento", "created_at": "<PERSON>reato a"}, "no_subscription_events": "<PERSON><PERSON>un evento in abbonamento"}, "buttons": {"cancel": "Annullamento", "payment_method": "Aggiornare i dettagli del pagamento", "audit_log": "Registro di controllo"}, "modal": {"seats": {"seats": "Posti a sedere", "seats_help": "Il numero di posti per il piano", "submit": "Risparmiare"}, "price": {"price": "Nuovo prezzo", "price_help": "Il nuovo prezzo da addebitare alla prossima fattura", "submit": "Aggiornamento"}, "plan": {"plan": "Nuovo piano", "plan_help": "Il piano a cui si desidera modificare l'abbonamento", "price": "Nuovo prezzo", "price_help": "Il nuovo prezzo da addebitare alla prossima fattura", "submit": "Aggiornamento", "when": {"title": "Quando", "next_cycle": "Utiliz<PERSON><PERSON> per il prossimo ciclo di fatturazione", "instantly": "Immediatamente", "specific_date": "Data specifica"}}, "payment_method": {"payment_method": "Utiliz<PERSON><PERSON> i dettagli di pagamento", "payment_method_help": "Questi dati saranno utilizzati per il successivo addebito al cliente.", "update_button": "Aggiornare i dettagli del pagamento", "submit": "Aggiornamento"}, "cancel": {"title": "Annullamento dell'abbonamento", "cancel_btn": "<PERSON><PERSON><PERSON>", "close_btn": "<PERSON><PERSON><PERSON>", "when": {"title": "Quando", "end_of_run": "Fine del periodo di fatturazione corrente", "instantly": "Immediatamente", "specific_date": "Data specifica"}, "refund_type": {"title": "Tipo di rimborso", "none": "<PERSON><PERSON><PERSON>", "prorate": "Proroga del rimborso in base all'utilizzo", "full": "<PERSON><PERSON><PERSON><PERSON> completo"}, "cancelled_message": "<PERSON><PERSON><PERSON> con <PERSON>o"}}, "usage_estimate": {"title": "Stima dell'utilizzo Costo", "usage": "<PERSON><PERSON><PERSON><PERSON>", "estimate_cost": "Stima dei costi", "metric": "Metric<PERSON>"}, "metadata": {"title": "<PERSON><PERSON><PERSON>", "no_metadata": "<PERSON><PERSON><PERSON> metadato"}}, "list": {"title": "Abbonamenti", "email": "Cliente", "status": "Stato", "plan": "Piano", "no_subscriptions": "Al momento non ci sono abbonamenti", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view": "Vista", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Si è verificato un errore", "filters": {"status": "Stato", "status_choices": {"cancelled": "<PERSON><PERSON><PERSON>", "active": "Attivo", "blocked": "Bloccato", "overdue_payment_open": "Pagamenti in ritardo aperti", "trial_active": "<PERSON>va attiva", "trial_ended": "<PERSON><PERSON> conclusa"}}, "loading": "Caricamento abbonamenti..."}, "menu": {"title": "Abbonamenti", "subscriptions": "Abbonamenti", "mass_change": "Cambiamento di massa", "subscriptions_list": "Elenco abbonamenti"}, "mass_change": {"list": {"title": "Abbonamenti - Cambio di massa", "change_date": "Data di modifica", "status": "Stato", "created_at": "<PERSON>reato a", "no_mass_change": "Al momento non ci sono modifiche di massa agli abbonamenti", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view": "Vista", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Si è verificato un errore"}, "create": {"title": "Creare un cambiamento di massa", "criteria": {"title": "<PERSON><PERSON><PERSON>", "plan": "Piano", "price": "Prezzo", "brand": "<PERSON><PERSON>", "country": "<PERSON><PERSON>"}, "new": {"title": "<PERSON><PERSON><PERSON> valori", "plan": "Nuovo piano", "price": "Nuovo prezzo"}, "change_date": {"title": "Data di modifica", "help_info": "Dopo la data di modifica, tutti i rinnovi saranno al nuovo prezzo. Il piano di abbonamento verrà modificato immediatamente."}, "estimate": {"amount": "Questo produrrà una modifica stimata di {amount} {currency} al {schedule}"}, "submit_button": "Pulsante di invio"}, "view": {"title": "Modifica dell'abbonamento di massa", "criteria": {"title": "<PERSON><PERSON><PERSON>", "plan": "Piano", "price": "Prezzo", "brand": "<PERSON><PERSON>", "country": "<PERSON><PERSON>"}, "new_values": {"title": "<PERSON><PERSON><PERSON> valori", "plan": "Piano", "price": "Prezzo"}, "change_date": {"title": "Data di modifica"}, "estimate": {"amount": "Questo produrrà una modifica stimata di {amount} {currency} al {schedule}"}, "export_button": "Esportazione dell'elenco clienti", "cancel": "Annullamento", "uncancel": "<PERSON><PERSON><PERSON>"}}}, "payment": {"list": {"title": "<PERSON><PERSON><PERSON>", "no_payments": "Al momento non ci sono pagamenti", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "list": {"amount": "Importo", "currency": "Valuta", "customer": "Cliente", "status": "Stato", "created_at": "<PERSON>reato a"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Si è verificato un errore"}, "view": {"title": "Dettagli sul pagamento", "main": {"title": "Dettagli principali", "amount": "Importo", "currency": "Valuta", "external_reference": "Riferimento esterno", "status": "Stato", "created_at": "<PERSON>reato a"}, "customer": {"title": "Cliente", "email": "Email", "more_info": "Per saperne di più", "country": "<PERSON><PERSON>", "attach": "Allegare al cliente"}, "refunds": {"title": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Importo", "reason": "Motivo", "created_by": "<PERSON><PERSON><PERSON> <PERSON>", "created_at": "<PERSON>reato a", "none": "<PERSON><PERSON><PERSON> trovato"}, "subscriptions": {"title": "Abbonamenti", "plan_name": "Nome del piano", "more_info": "Per saperne di più", "none": "Pagamento non legato agli abbonamenti"}, "receipts": {"title": "Ricevute", "created_at": "<PERSON>reato a", "download": "Scaricare", "none": "Il pagamento non ha ricevute"}, "buttons": {"refund": "Emissione Rimborso", "generate_receipt": "Generare la ricevuta"}, "modal": {"attach": {"title": "Allegare al cliente", "button": "Allegare"}, "refund": {"title": "<PERSON><PERSON><PERSON><PERSON>", "amount": {"title": "Importo", "help_info": "È l'importo della valuta minore. Quindi 100 USD corrispondono a 1,00 USD."}, "reason": {"title": "Motivo"}, "submit": "Emissione Rimborso", "success_message": "<PERSON><PERSON><PERSON><PERSON> creato con successo", "error_message": "Qualcosa è andato storto"}}}}, "refund": {"list": {"title": "<PERSON><PERSON><PERSON><PERSON>", "no_refunds": "Al momento non sono previsti rimborsi", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "list": {"amount": "Importo", "currency": "Valuta", "customer": "Cliente", "status": "Stato", "created_by": "<PERSON><PERSON><PERSON> <PERSON>", "created_at": "<PERSON>reato a"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Si è verificato un errore"}, "view": {"title": "Dettagli sul rimborso", "main": {"title": "Dettagli principali", "amount": "Importo", "currency": "Valuta", "external_reference": "Riferimento esterno", "status": "Stato", "created_at": "<PERSON>reato a"}, "buttons": {"refund": "Emissione Rimborso"}, "modal": {"refund": {"title": "<PERSON><PERSON><PERSON><PERSON>", "amount": {"title": "Importo", "help_info": "È l'importo della valuta minore. Quindi 100 USD corrispondono a 1,00 USD."}, "reason": {"title": "Motivo"}, "submit": "Emissione Rimborso"}}}}, "transactions": {"menu": {"title": "Transazioni", "payments": "<PERSON><PERSON><PERSON>", "refunds": "<PERSON><PERSON><PERSON><PERSON>", "charge_backs": "Schienali di carica", "invoices": "Fatture", "unpaid_invoices": "Fatture non pagate", "checkout": "Cass<PERSON>", "countries": "<PERSON><PERSON>", "tax_types": "Tipi di imposta"}}, "settings": {"menu": {"title": "Impostazioni", "user_settings": "Impostazioni utente", "invite": "Invito", "pdf_templates": "Modelli PDF", "email_templates": "Modelli di e-mail", "tax_settings": "Impostazioni fiscali", "brand_settings": "Impostazioni del marchio", "notification_settings": "Impostazioni di notifica", "system_settings": "Impostazioni di sistema", "users": "<PERSON><PERSON><PERSON>", "stripe": "Striscia", "api_keys": "Chiavi API", "exchange_rates": "Tassi di cambio", "integrations": "Integrazioni", "vat_sense": "VatSense", "audit_log": "Registro di controllo"}, "pdf_template": {"list": {"title": "<PERSON><PERSON>", "name": "Nome", "locale": "Locale", "brand": "<PERSON><PERSON>", "create_btn": "<PERSON><PERSON><PERSON>", "edit_btn": "Modifica", "no_templates": "<PERSON><PERSON><PERSON>", "error_message": "Si è verificato un errore", "generator": "Aggiornare le impostazioni del generatore"}, "update": {"title": "Aggiorna modello - {name}", "content": "<PERSON><PERSON><PERSON>", "save": "Risparmiare", "download": "Scarica il test in PDF", "template": "<PERSON><PERSON>", "help_info": {"template": "Utilizzo del linguaggio di template Twig", "variable_docs": "Controllare la documentazione per vedere quali variabili sono disponibili"}}, "generator_settings": {"title": "Impostazioni del generatore PDF", "generator": "Generatore", "tmp_dir": "La directory temporanea", "api_key": "<PERSON><PERSON>", "bin": "Posizione del cestino", "submit": "Risparmiare", "help_info": {"generator": "Il generatore da utilizzare. Se non si è sicuri, usare mpdf", "tmp_dir": "La directory temporanea da utilizzare. Se non si è sicuri, usare /tmp", "api_key": "La chiave API da utilizzare", "bin": "La posizione di wkhtmltopdf"}}, "create": {"title": "<PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON>", "save": "Risparmiare", "download": "Scarica il test in PDF", "template": "<PERSON><PERSON>", "locale": "Locale", "type": "Tipo", "brand": "<PERSON><PERSON>", "help_info": {"locale": "Il locale per cui è stato creato il modello PDF", "brand": "Il marchio per cui è stato creato il modello PDF", "type": "Il tipo di PDF per cui è stato creato il modello", "template": "Utilizzo del linguaggio di template Twig", "variable_docs": "Controllare la documentazione per vedere quali variabili sono disponibili"}}}, "brand_settings": {"list": {"title": "Impostazioni del marchio", "name": "Nome", "edit_btn": "Modifica", "no_brands": "Non esistono marchi", "create_new": "<PERSON><PERSON><PERSON>", "error_message": "Si è verificato un errore"}, "update": {"title": "Aggiorna le impostazioni del marchio - {name}", "fields": {"name": "Nome", "email": "Indirizzo e-mail", "company_name": "Nome della società", "street_line_one": "Linea stradale 1", "street_line_two": "Linea stradale 2", "city": "Città", "region": "Regione", "country": "<PERSON><PERSON>", "postcode": "Codice postale", "code": "Codice", "tax_number": "Codice fiscale", "tax_rate": "Aliquota fiscale", "digital_services_tax_rate": "Aliquota d'imposta sui servizi digitali", "support_email": "Supporto e-mail", "support_phone_number": "Numero di telefono dell'assistenza"}, "help_info": {"name": "Il nome del marchio", "code": "Il codice da utilizzare per identificare il marchio nelle chiamate API. Non può essere aggiornato.", "email": "L'email da utilizzare per l'invio di email al marchio cliente", "company_name": "Il nome dell'azienda per la fatturazione", "street_line_one": "La prima riga dell'indirizzo di fatturazione", "street_line_two": "La seconda riga dell'indirizzo di fatturazione stradale", "city": "La città per l'indirizzo di fatturazione", "region": "La regione/Stato per l'indirizzo di fatturazione", "country": "Paese di fatturazione del cliente - codice paese ISO 3166-1 alpha-2.", "postcode": "Il codice postale dell'indirizzo di fatturazione", "tax_number": "Il codice fiscale dell'azienda/marchio", "tax_rate": "L'aliquota fiscale da utilizzare per il paese d'origine o quando non è possibile trovare un'altra aliquota fiscale", "digital_services_tax_rate": "L'aliquota fiscale da utilizzare per il paese d'origine o quando non è possibile trovare un'altra aliquota fiscale per i servizi digitali", "support_email": "L'indirizzo e-mail per il contatto con l'assistenza", "support_phone_number": "Il numero di telefono per il contatto con l'assistenza"}, "general": "Impostazioni generali", "notifications": "Notifiche", "address_title": "Indirizzo di fatturazione", "success_message": "Aggiornato", "submit_btn": "Aggiornamento", "notification": {"subscription_creation": "Creazione di abbonamenti", "subscription_cancellation": "Cancellazione dell'abbonamento", "expiring_card_warning": "Avviso di carta in scadenza", "expiring_card_warning_day_before": "Avviso di carta in scadenza - Giorno prima", "invoice_created": "Fat<PERSON> creata", "invoice_overdue": "Fattura scaduta", "quote_created": "Citazione Creata", "trial_ending_warning": "Avviso di fine prova", "before_charge_warning": "Avviso prima della carica", "before_charge_warning_options": {"none": "<PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON>", "yearly": "Annuale"}, "payment_failure": "Mancato pagamento"}, "support": "Dettagli di contatto dell'assistenza"}, "create": {"title": "Creare le impostazioni del marchio", "fields": {"name": "Nome", "email": "Indirizzo e-mail", "company_name": "Nome della società", "street_line_one": "Linea stradale 1", "street_line_two": "Linea stradale 2", "city": "Città", "region": "Regione", "country": "<PERSON><PERSON>", "post_code": "Codice postale", "code": "Codice", "tax_number": "Codice fiscale", "tax_rate": "Aliquota fiscale", "digital_services_tax_rate": "Aliquota d'imposta sui servizi digitali", "support_email": "Supporto e-mail", "support_phone_number": "Numero di telefono dell'assistenza"}, "help_info": {"name": "Il nome del marchio", "code": "Il codice da utilizzare per identificare il marchio nelle chiamate API. Non può essere aggiornato. Deve essere per lo più alfanumerico in minuscolo e solo con caratteri di sottolineatura.", "tax_number": "Il codice fiscale del marchio/dell'azienda", "email": "L'email da utilizzare per l'invio di email al marchio cliente", "company_name": "Il nome dell'azienda per la fatturazione", "street_line_one": "La prima riga dell'indirizzo di fatturazione", "street_line_two": "La seconda riga dell'indirizzo di fatturazione stradale", "city": "La città per l'indirizzo di fatturazione", "region": "La regione/Stato per l'indirizzo di fatturazione", "country": "Paese di fatturazione del cliente - codice paese ISO 3166-1 alpha-2.", "postcode": "Il codice postale dell'indirizzo di fatturazione", "tax_rate": "L'aliquota fiscale da utilizzare per il paese d'origine o quando non è possibile trovare un'altra aliquota fiscale", "digital_services_tax_rate": "L'aliquota fiscale da utilizzare per il paese d'origine o quando non è possibile trovare un'altra aliquota fiscale per i servizi digitali", "support_email": "L'indirizzo e-mail per il contatto con l'assistenza", "support_phone_number": "Il numero di telefono per il contatto con l'assistenza"}, "address_title": "Indirizzo di fatturazione", "success_message": "Aggiornato", "submit_btn": "<PERSON><PERSON><PERSON>", "support": "Dettagli di contatto dell'assistenza"}}, "email_template": {"list": {"title": "Modelli di posta elettronica", "email": "Email", "country": "<PERSON><PERSON>", "reference": "Riferimento", "brand": "<PERSON><PERSON>", "no_customers": "Al momento non ci sono modelli di email esistenti", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "locale": "Locale", "view_btn": "Vista", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Si è verificato un errore"}, "create": {"title": "Creare un modello di e-mail", "fields": {"name": "Nome", "locale": "Locale", "use_emsp_template": "Util<PERSON><PERSON><PERSON> il modello EMSP", "subject": "<PERSON><PERSON><PERSON>", "template_body": "Modello Corpo", "template_id": "ID modello", "brand": "<PERSON><PERSON>"}, "help_info": {"name": "Per quale e-mail è stato creato questo modello", "locale": "Per quale locale è questo modello.", "use_emsp_template": "Se deve essere utilizzato il sistema di modelli del provider di servizi e-mail in uso. Se non si è sicuri, lasciare deselezionato", "subject": "Il messaggio da inserire nell'oggetto", "template_body": "Il modello TWIG da usare per generare l'html dell'email.", "template_id": "L'ID del modello fornito dal provider di servizi e-mail in cui è stato creato il modello. Se non si è sicuri, deselezionare la voce Usa modello emsp.", "brand": "Il marchio per cui è stato creato il modello di e-mail.", "variable_docs": "Controllare la documentazione per vedere quali variabili sono disponibili"}, "submit_btn": "<PERSON><PERSON><PERSON>", "success_message": "Modello di e-mail creato con successo"}, "update": {"title": "Aggiornamento del modello di e-mail", "fields": {"name": "Nome", "locale": "Locale", "use_emsp_template": "Util<PERSON><PERSON><PERSON> il modello EMSP", "subject": "<PERSON><PERSON><PERSON>", "template_body": "Modello Corpo", "template_id": "ID modello"}, "help_info": {"name": "Per quale e-mail è stato creato questo modello", "locale": "Per quale locale è questo modello.", "use_emsp_template": "Se deve essere utilizzato il sistema di modelli del provider di servizi e-mail in uso. Se non si è sicuri, lasciare deselezionato", "subject": "Il messaggio da inserire nell'oggetto", "template_body": "Il modello TWIG da usare per generare l'html dell'email.", "template_id": "L'ID del modello fornito dal provider di servizi e-mail in cui è stato creato il modello. Se non si è sicuri, deselezionare la voce Usa modello emsp.", "variable_docs": "Controllare la documentazione per vedere quali variabili sono disponibili"}, "submit_btn": "Aggiornamento", "success_message": "Modello di e-mail aggiornato con successo", "test_email": "Inviare un'e-mail di prova"}}, "notification_settings": {"update": {"title": "Impostazioni di notifica", "submit_btn": "Aggiornamento", "success_message": "Impostazioni di notifica aggiornate", "fields": {"send_customer_notifications": "Inviare notifiche ai clienti", "emsp": "Fornitore di servizi e-mail", "emsp_api_key": "Fornitore di servizi e-mail - Chiave API", "emsp_api_url": "Provider di servizi e-mail - URL API", "emsp_domain": "Fornitore di servizi e-mail - Dominio", "default_outgoing_email": "Email in uscita predefinita"}, "help_info": {"emsp": "Quale provider di posta elettronica si desidera utilizzare. Se non si è sicuri, utilizzare il sistema.", "emsp_api_key": "La chiave API fornita dal fornitore di servizi e-mail.", "emsp_api_url": "L'URL API fornito dal fornitore di servizi e-mail.", "emsp_domain": "Il dominio del provider di servizi e-mail.", "send_customer_notifications": "Se volete che BillaBear invii notifiche ai clienti, come la creazione di un abbonamento, la pausa, la ricezione di un pagamento, ecc.", "default_outgoing_email": "L'indirizzo e-mail predefinito da utilizzare per l'invio delle notifiche quando non esistono impostazioni del marchio"}}}, "system_settings": {"update": {"title": "Impostazioni di sistema", "submit_btn": "Aggiornamento", "success_message": "Impostazioni di sistema aggiornate", "fields": {"system_url": "URL del sistema", "timezone": "<PERSON><PERSON> orario", "invoice_number_generation": "Generazione del numero di fattura", "subsequential_number": "Numero successivo", "default_invoice_due_time": "Scadenza predefinita della fattura", "format": "Formato", "invoice_generation": "Generazione di fatture"}, "help_info": {"system_url": "L'url di base in cui si trova BillaBear.", "timezone": "Il fuso orario predefinito del sistema", "invoice_number_generation": "Come viene generato il numero di fattura. Casuale è una stringa casuale e successivo significa che è un numero che aumenta", "subsequential_number": "L'ultimo numero di fattura utilizzato. Il numero di fattura successivo sarà di una cifra superiore", "default_invoice_due_time": "Quanto tempo intercorre tra la creazione della fattura e la data di scadenza", "format": "Il formato da utilizzare per la generazione del numero di fattura. %S è il numero successivo e %R per 8 caratteri casuali.", "invoice_generation": "Quando deve essere generata una nuova fattura per gli abbonamenti"}, "invoice_number_generation": {"random": "Numero casuale", "subsequential": "Successiva", "format": "Formato"}, "default_invoice_due_time": {"30_days": "30 giorni", "60_days": "60 giorni", "90_days": "90 giorni", "120_days": "120 giorni"}, "invoice_generation_types": {"periodically": "Periodicamente", "end_of_month": "Fine del mese"}}}, "user": {"list": {"title": "Utente", "email": "Email", "roles": "<PERSON><PERSON><PERSON>", "reference": "Riferimento", "no_customers": "Attualmente non ci sono clienti esistenti", "create_new": "<PERSON><PERSON><PERSON>", "invite": "Invita un nuovo utente", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view_btn": "Vista", "list": {"email": "Email", "role": "<PERSON><PERSON><PERSON>"}, "invite_title": "<PERSON><PERSON><PERSON>", "invite_list": {"email": "Email", "sent_at": "Inviato a", "role": "<PERSON><PERSON><PERSON>", "copy_link": "Copiare il link", "copied_link": "Copiato"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "error_message": "Si è verificato un errore", "audit_log": "Registro di controllo"}, "update": {"title": "Aggiornamento Utente", "fields": {"email": "Email", "roles": "<PERSON><PERSON><PERSON>"}, "help_info": {"email": "L'e-mail che l'utente deve utilizzare per effettuare il login e ricevere le notifiche.", "roles": "A cosa deve avere accesso l'utente."}, "submit_btn": "Aggiornamento", "success_message": "L'aggiornamento dell'utente è riuscito"}}, "stripe": {"main": {"title": "Importazione di strisce", "edit_config": "Modifica configurazione", "hide_config": "Nascondi configurazione", "start_button": "Pulsante Avvia importazione", "already_in_progress": "Importazione già in corso", "list": {"state": "Stato", "last_id": "Ultimo ID elaborato", "created_at": "<PERSON>reato a", "updated_at": "Aggiornamento a", "no_results": "Finora non sono state effettuate importazioni di strisce.", "view": "Vista"}, "danger_zone": {"title": "Zona di pericolo", "use_stripe_billing": "Utilizzate Stripe Billing per addebitare i clienti.", "disable_billing": "Disattivare la fatturazione con Stripe", "enable_billing": "Abilitare la fatturazione con Stripe"}, "disable_billing_modal": {"title": "Disattivare la fatturazione con Stripe", "disable_all_subscriptions": "Disabilitando la fatturazione con Stripe, non volete più che Stripe gestisca gli addebiti ai clienti, ma che sia BillaBear a gestirli. Questo vi farà risparmiare denaro.", "warning": "Una volta disattivato, se si desidera tornare a utilizzare Stripe Billing, è necessario iscrivere nuovamente tutti manualmente.", "cancel": "Annullamento", "confirm": "<PERSON><PERSON><PERSON>"}, "webhook": {"title": "Webhook", "url": "URL Webhook", "register_webhook": "Registrazione di un webhook", "deregister_webhook": "Cancellare un webhook", "help_info": {"url": "Un URL https disponibile pubblicamente per le chiamate webhook."}}, "stripe_config": {"title": "Chiavi API di Stripe", "description": "Per utilizzare Stripe è necessario configurare le chiavi API.", "stripe_private_key": "Chiave privata", "help_info": {"stripe_private_key": "La chiave API da utilizzare per autenticare le richieste del backend", "stripe_public_key": "La chiave API da usare per autenticare le richieste del frontend."}, "stripe_public_key": "Chiave pubblica", "submit_button": "Invia", "error": "Impossibile confermare le chiavi API di Stripe."}}, "view_import": {"title": "Importazione di strisce", "progress": "Progressi", "error": "Errore", "last_updated_at": "Ultimo aggiornamento al", "last_id_processed": "Ultimo ID elaborato", "process": {"started": "Avviato", "customers": "Clienti", "products": "<PERSON><PERSON>tti", "prices": "<PERSON><PERSON>", "subscriptions": "Abbonamenti", "payments": "<PERSON><PERSON><PERSON>", "refunds": "<PERSON><PERSON><PERSON><PERSON>", "charge_backs": "Schienali di carica", "completed": "Completato"}}}, "api_keys": {"main": {"title": "Chiavi API", "add_new_button": "C<PERSON>re una nuova chiave API", "info": {"api_base_url": "URL base API"}, "list": {"name": "Nome", "key": "Chiave", "expires_at": "Scade a", "created_at": "<PERSON>reato a", "no_api_keys": "Al momento non ci sono chiavi API", "disable_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "create": {"title": "<PERSON><PERSON><PERSON> una nuova chiave", "name": "Nome", "expires": "Scadenza", "close": "<PERSON><PERSON><PERSON>", "create_button": "<PERSON><PERSON><PERSON>"}}}, "exchange_rates": {"title": "Tassi di cambio", "list": {"currency_code": "Valuta", "rate": "Tasso", "no_rates": "Nessuna tariffa"}}, "tax_settings": {"update": {"title": "Impostazioni fiscali", "submit_btn": "Invia", "success_message": "Impostazioni fiscali aggiornate", "fields": {"tax_customers_with_tax_number": "Clienti fiscali con codice fiscale", "eu_business_tax_rules": "Gestire le norme fiscali dell'UE per le imprese", "eu_one_stop_shop_rule": "Regola dello sportello unico dell'UE", "vat_sense_enabled": "Senso dell'IVA abilitato", "vat_sense_api_key": "Chiave API VAT Sense", "validate_vat_ids": "Convalidare gli ID IVA"}, "help_info": {"tax_customers_with_tax_number": "Se non viene controllato, ai clienti che hanno fornito un codice fiscale non viene addebitata l'imposta", "eu_business_tax_rules": "Se abilitato, i clienti commerciali che hanno fornito un numero di partita IVA saranno gestiti in modo diverso dai clienti normali", "eu_one_stop_shop_rule": "Applicare la regola dello sportello unico dell'UE. In cui i Paesi dell'UE sono tassati indipendentemente dalla soglia.", "vat_sense_enabled": "Se si desidera sincronizzare le regole fiscali con il database di VAT Sense ogni giorno", "vat_sense_api_key": "La vostra chiave API VAT Sense. <a href=\"https://vatsense.com/signup?referral=BILLABEAR\" target='_blank'>O<PERSON>eni una chiave gratuita qui</a>.", "validate_vat_ids": "Se si desidera convalidare gli id delle tasse rispetto all'API VAT Sense."}}, "vatsense": {"title": "VatSense", "fields": {"vat_sense_enabled": "Senso dell'IVA abilitato", "vat_sense_api_key": "Chiave API VAT Sense", "validate_vat_ids": "Convalidare gli ID IVA"}, "help_info": {"vat_sense_enabled": "Se si desidera sincronizzare le regole fiscali con il database di VAT Sense ogni giorno", "vat_sense_api_key": "La vostra chiave API VAT Sense. <a href=\"https://vatsense.com/signup?referral=BILLABEAR\" target='_blank'>O<PERSON>eni una chiave gratuita qui</a>.", "validate_vat_ids": "Se si desidera convalidare gli id delle tasse rispetto all'API VAT Sense."}, "description": "Grazie all'integrazione con VAT Sense, potrete aggiornare automaticamente le vostre regole fiscali in caso di modifiche alle leggi fiscali in tutto il mondo. È inoltre possibile far convalidare gli ID IVA da VAT Sense, in modo da garantire che i clienti europei abbiano ID IVA validi.", "create_account": "È possibile creare un account gratuito.", "create_account_link": "Creare un account"}}}, "charge_backs": {"list": {"title": "Schienali di carica", "no_charge_backs": "Al momento non ci sono rimborsi di spese", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view_payment": "Visualizza il pagamento", "list": {"amount": "Importo", "currency": "Valuta", "customer": "Cliente", "status": "Stato", "created_at": "<PERSON>reato a"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}}}, "reports": {"dashboard": {"title": "Cruscotto", "subscription_count": {"title": "Abbonamenti attivi"}, "subscription_creation": {"title": "Nuovi abbonamenti"}, "subscription_cancellation": {"title": "Abbonamenti esauriti"}, "payment_amount": {"title": "<PERSON><PERSON>"}, "refund_amount": {"title": "Importo <PERSON>"}, "charge_back_amount": {"title": "Importo contestato"}, "estimated_mrr": "MRR stimato", "estimated_arr": "ARR stimato", "header": {"active_subscriptions": "Abbonamenti attivi", "active_customers": "Clienti attivi", "unpaid_invoices": "Fatture non pagate"}, "buttons": {"daily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "Annuale", "subscriptions": "Abbonamenti", "payments": "<PERSON><PERSON><PERSON>"}, "links": {"customers": "Clienti", "subscriptions": "Abbonamenti", "invoices": "Fatture"}, "latest_customers": {"title": "Ultimi clienti", "list": {"email": "Email", "creation_date": "Data di creazione"}}, "latest_events": {"title": "Ultimi eventi", "list": {"event_type": "Tipo di evento", "customer": "Cliente", "creation_date": "Data di creazione"}}, "latest_payments": {"title": "Ultimi pagamenti", "list": {"amount": "Importo", "customer": "Cliente", "creation_date": "Data di creazione"}}, "payments": {"title": "Totali dei pagamenti"}, "loading_chart": "Caricamento dei dati del grafico..."}, "expiring_cards": {"main": {"title": "Carte in scadenza", "list": {"customer_email": "Email del cliente", "card_number": "Numero di carta", "no_expiring_cards": "Nessuna carta in scadenza", "loading": "caricamento", "view": "Vista"}}}, "menu": {"title": "Rapporti", "dashboard": "Cruscotto", "expiring_cards": "Carte in scadenza", "subscriptions": "Abbonamenti", "tax": "Imposta", "churn": "Abbandono di abbonamenti", "lifetime": "Vita"}, "subscriptions": {"overview": {"title": "Abbonamenti", "plans": {"title": "Ripartizione dei piani"}, "schedules": {"title": "Ripartizione del programma"}}, "churn": {"title": "Abbandono di abbonamenti", "buttons": {"daily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "Annuale"}}}, "vat": {"overview": {"title": "IVA", "list": {"amount": "Importo", "currency": "Valuta", "country": "<PERSON><PERSON>"}}}, "financial": {"lifetime": {"title": "Valore della vita", "lifespan": "<PERSON>rata della vita", "lifespan_value": "{lifespan}", "lifetime": "Valore della vita", "customer_count": "Conteggio clienti", "filters": {"country": "<PERSON><PERSON>", "payment_schedule": "Piano di pagamento", "subscription_plan": "Piano di abbonamento", "brand": "<PERSON><PERSON>"}, "help_info": {"country": "Per vedere il valore della vita degli utenti di questo paese", "payment_schedule": "Per vedere il valore di vita degli utenti che pagano in base a un piano di pagamento", "subscription_plan": "Per vedere il valore di vita degli utenti per un piano di abbonamento", "brand": "Per vedere il valore di vita degli utenti di un marchio"}, "schedules": {"week": "<PERSON><PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON>", "year": "Annuale"}, "chart": {"lifetime_values": "Valore della vita", "customer_counts": "Conteggio clienti"}, "submit": "Filtro"}}, "tax": {"title": "Rapporto fiscale", "map": {"title": "Imposta riscossa per"}, "countries": {"title": "Soglie paese", "transacted_amount": "<strong>Transato:</strong> {currency}{transacted_amount}", "collected_amount": "<strong>Incasso:</strong> {currency}{collected_amount}", "threshold_status": "<strong>Soglia:</strong> {status}", "threshold_reached": "<PERSON><PERSON><PERSON><PERSON>", "threshold_not_reached": "Non raggiunto"}, "transactions": {"title": "Esempio di esportazione", "download": "Scaricare Esportazione"}}}, "credit": {"create": {"title": "<PERSON><PERSON><PERSON> credito", "amount": "Importo", "currency": "Valuta", "reason": "Motivo", "type": "Tipo", "credit": "Credito", "debit": "Debito", "help_info": {"type": "Tipo di rettifica del credito, a credito o a debito", "amount": "Il prezzo è la valuta del livello minore. Quindi 1,00 USD corrisponde a 100 e 9,99 a 999.", "display_amount": "Il prezzo sarebbe {amount}.", "currency": "La valuta di addebito al cliente", "reason": "Un motivo opzionale che può essere utile in seguito."}, "success_message": "Credito creato con successo", "submit_btn": "<PERSON><PERSON><PERSON>"}}, "invoices": {"list": {"title": "Fatture", "unpaid_title": "Fatture non pagate", "email": "Email del cliente", "total": "Totale", "currency": "Valuta", "created_at": "<PERSON>reato a", "download": "Scaricare", "charge": "Tentativo di pagamento", "no_invoices": "Non ci sono fatture qui", "next": "<PERSON><PERSON>", "prev": "Precedente", "view_btn": "Visualizza fattura", "status": "Stato", "paid": "<PERSON><PERSON><PERSON>", "outstanding": "Eccezionale", "filter": {"title": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON>", "email": "Email del cliente", "number": "Numero di fattura"}, "mark_as_paid": "Contrassegnare come pagato"}, "menu": {"title": "Fatture", "invoices": "Elenco di tutti", "unpaid_invoices": "Elenco non pagato", "create": "<PERSON><PERSON><PERSON> una fattura", "quotes": "Citazioni", "settings": "Impostazioni", "invoices_list": "Elenco fatture"}, "create": {"title": "<PERSON><PERSON><PERSON> una fattura", "create_invoice": "<PERSON><PERSON><PERSON> una fattura", "success_message": "Fat<PERSON> creata", "errors": {"no_customer": "È necessario un cliente", "nothing_to_invoice": "È necessario aggiungere un abbonamento o un articolo unico.", "same_currency_and_schedule": "La stessa valuta e lo stesso calendario devono essere utilizzati per gli abbonamenti", "currency": "È richiesta una valuta", "need_description": "Necessità di una descrizione", "need_amount": "Quantità necessaria", "need_tax_type": "Serve un tipo di imposta"}, "customer": {"create_customer": "<PERSON>rea cliente", "fields": {"customer": "Cliente", "currency": "Valuta", "due_date": "Data di scadenza"}, "help_info": {"customer": "Il cliente a cui è destinato il preventivo", "currency": "La valuta da utilizzare per la fattura", "due_date": "La data di scadenza della fattura; se non viene indicata, viene utilizzato il valore predefinito del sistema."}}, "subscriptions": {"title": "Abbonamenti", "add_new": "Aggiungi abbonamento", "list": {"subscription_plan": "Piano di abbonamento", "price": "Prezzo", "seat_number": "Numero di sedile"}, "no_subscriptions": "<PERSON><PERSON><PERSON> a<PERSON>", "add_subscription": "Aggiungi abbonamento"}, "items": {"title": "Voci una tantum", "add_item": "Aggiungi una voce una tantum", "no_items": "Nessun articolo unico", "list": {"description": "Descrizione", "amount": "Importo", "tax_included": "<PERSON>ssa inclusa", "digital_product": "Prodotto digitale", "tax_type": "Tipo di imposta"}, "tax_types": {"digital_services": "Servizi digitali", "digital_goods": "Beni digitali", "physical": "<PERSON>i/servizi fisici"}}}, "view": {"title": "Visualizza fattura", "main": {"title": "Informazioni sulla fattura", "created_at": "<PERSON>reato a", "pay_link": "Collegamento a pagamento", "due_date": "Data di scadenza"}, "customer": {"title": "Cliente", "email": "Email", "more_info": "Per saperne di più", "address": {"company_name": "Nome della società", "street_line_one": "Linea stradale 1", "street_line_two": "Linea stradale 2", "city": "Città", "region": "Regione", "post_code": "Codice postale", "country": "<PERSON><PERSON>"}}, "biller": {"title": "<PERSON><PERSON>", "email": "Email", "more_info": "Per saperne di più", "address": {"company_name": "Nome della società", "street_line_one": "Linea stradale 1", "street_line_two": "Linea stradale 2", "city": "Città", "region": "Regione", "post_code": "Codice postale", "country": "<PERSON><PERSON>"}}, "lines": {"title": "Articoli", "description": "Descrizione", "tax_rate": "Aliquota fiscale", "amount": "Importo", "tax_exempt": "<PERSON><PERSON>e da imposte"}, "total": {"title": "Totale", "total": "Totale", "sub_total": "Totale parziale", "tax_total": "Imposta Totale"}, "status": {"paid": "Fattura pagata con successo alla {date}", "outstanding": "La fattura deve ancora essere pagata."}, "actions": {"charge_card": "Carta di credito", "mark_as_paid": "Segna come pagato"}, "payment_failed": {"message": "Impossibile effettuare il pagamento"}, "payment_succeeded": {"message": "Il pagamento è stato effettuato con successo."}, "download": "Scarica la fattura", "invoice_delivery": {"title": "Consegne di fatture", "method": "<PERSON><PERSON><PERSON>", "detail": "Dettaglio", "status": "Stato", "created_at": "<PERSON>reato a", "no_invoice_deliveries": "Nessuna consegna di fatture"}}, "settings": {"title": "Impostazioni della fattura", "update": "Aggiornamento"}, "delivery": {"create": {"title": "Creare una nuova fattura di consegna", "fields": {"method": "<PERSON><PERSON><PERSON>", "format": "Formato", "sftp": {"port": "Porto", "hostname": "Nome host", "directory": "Elenco", "username": "Nome utente", "password": "Password"}, "webhook": {"method": "<PERSON><PERSON><PERSON>", "url": "URL"}, "email": {"email": "Email", "help_info": "Se non viene fornita alcuna e-mail, l'e-mail del cliente verrà utilizzata per impostazione predefinita."}}, "save": "Risparmiare"}, "update": {"title": "Aggiornare la consegna della fattura", "fields": {"method": "<PERSON><PERSON><PERSON>", "format": "Formato", "sftp": {"port": "Porto", "hostname": "Nome host", "directory": "Elenco", "username": "Nome utente", "password": "Password"}, "webhook": {"method": "<PERSON><PERSON><PERSON>", "url": "URL"}, "email": {"email": "Email", "help_info": "Se non viene fornita alcuna e-mail, l'e-mail del cliente verrà utilizzata per impostazione predefinita."}}, "save": "Risparmiare"}, "format": {"pdf": "PDF", "zugferd_v1": "ZUGFeRD V1", "zugferd_v2": "ZUGFeRD V2 - XRechnung"}}, "download": {"loading_message": "Caricamento...", "format": "Scegliere il formato per il download", "download": "Scaricare"}}, "home": {"stripe_import": {"text": "Non avete importato i dati di stripe.", "link": "Clicca qui per importare ora", "dismiss": "<PERSON><PERSON><PERSON>"}, "update_available": {"text": "È disponibile un aggiornamento", "link": "Dettagli di rilascio", "dismiss": "<PERSON><PERSON><PERSON>"}, "default_tax": {"text": "Il vostro paese non è supportato per le aliquote fiscali di default. È necessario impostare un'aliquota fiscale sul proprio marchio predefinito!", "link": "Vew Brands"}}, "vouchers": {"create": {"title": "<PERSON><PERSON><PERSON> un buono", "submit": "Invia", "success_message": "<PERSON><PERSON><PERSON> con successo il voucher", "fields": {"name": "Nome", "type": "Tipo", "type_percentage": "Percent<PERSON><PERSON>", "type_fixed_credit": "<PERSON><PERSON> fisso", "percentage": "Percent<PERSON><PERSON>", "entry_type": "Tipo di iscrizione", "entry_type_manual": "Manuale", "entry_type_automatic": "Automatico", "amount": "Importo - {amount}", "code": "Codice", "entry_event": "Evento", "event_expired_card_added": "Aggiungere una nuova carta di pagamento durante l'avviso di carta scaduta"}, "help_info": {"name": "Il nome del buono", "type": "Percentuale è una percentuale di sconto su una fattura e credito fisso è un credito fisso", "entry_type": "Manuale significa che l'utente inserisce un codice, automatico significa che viene attivato da un evento", "percentage": "La percentuale di sconto", "amount": "L'importo in {amount} che il buono fornisce", "code": "Il codice che il cliente dovrà fornire per l'attivazione del voucher", "entry_event": "L'evento che deve verificarsi affinché il buono sia attivato"}}, "list": {"title": "<PERSON><PERSON><PERSON>", "no_vouchers": "Al momento non ci sono voucher", "create_new": "<PERSON><PERSON>re un nuovo buono", "list": {"name": "Nome", "type": "Tipo", "entry_type": "Tipo di iscrizione"}, "view_btn": "Vista", "loading": "Buoni di carico"}, "view": {"title": "Voucher", "main": {"name": "Nome", "type": "Tipo", "disabled": "Di<PERSON><PERSON><PERSON>", "entry_type": "Tipo di iscrizione", "percentage": "Percent<PERSON><PERSON>", "amount": "Importo per {amount}", "code": "Codice", "automatic_event": "Evento automatico"}, "disable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enable": "Abilitazione"}}, "quotes": {"create": {"title": "Crea preventivo", "create_quote": "Crea preventivo", "success_message": "Citazione creata", "errors": {"no_customer": "È necessario un cliente", "nothing_to_invoice": "È necessario aggiungere un abbonamento o un articolo unico.", "same_currency_and_schedule": "La stessa valuta e lo stesso calendario devono essere utilizzati per gli abbonamenti", "currency": "È richiesta una valuta", "need_description": "Necessità di una descrizione", "need_amount": "Quantità necessaria", "need_tax_type": "Serve un tipo di imposta"}, "customer": {"create_customer": "<PERSON>rea cliente", "fields": {"customer": "Cliente", "currency": "Valuta", "expires_at": "Scade a"}, "help_info": {"customer": "Il cliente a cui è destinato il preventivo", "currency": "La valuta da utilizzare per la quotazione", "expires_at": "Quando il preventivo scade e non può essere pagato"}}, "subscriptions": {"title": "Abbonamenti", "add_new": "Aggiungi abbonamento", "list": {"subscription_plan": "Piano di abbonamento", "price": "Prezzo", "per_seat": "Per posto a sedere"}, "no_subscriptions": "<PERSON><PERSON><PERSON> a<PERSON>", "add_subscription": "Aggiungi abbonamento"}, "items": {"title": "Voci una tantum", "add_item": "Aggiungi una voce una tantum", "no_items": "Nessun articolo unico", "list": {"description": "Descrizione", "amount": "Importo", "tax_included": "<PERSON>ssa inclusa", "digital_product": "Prodotto digitale", "tax_type": "Tipo di imposta"}, "tax_types": {"digital_services": "Servizi digitali", "digital_goods": "Beni digitali", "physical": "<PERSON>i/servizi fisici"}}}, "list": {"title": "Citazioni", "email": "Email del cliente", "total": "Totale", "currency": "Valuta", "created_at": "<PERSON>reato a", "no_quotes": "Non ci sono citazioni qui", "next": "<PERSON><PERSON>", "prev": "Precedente", "view_btn": "Vista", "filter": {"title": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON>", "email": "Email del cliente", "number": "Numero di fattura"}}, "view": {"title": "Visualizza preventivo", "quote": {"title": "Info sul preventivo", "created_by": "<PERSON><PERSON><PERSON> <PERSON>", "created_at": "<PERSON>reato a", "expires_at": "Scade a", "pay_link": "Collegamento a pagamento"}, "status": {"paid": "Preventivo pagato con successo alla {date}"}, "customer": {"title": "Cliente", "email": "Email", "more_info": "Per saperne di più", "address": {"company_name": "Nome della società", "street_line_one": "Linea stradale 1", "street_line_two": "Linea stradale 2", "city": "Città", "region": "Regione", "post_code": "Codice postale", "country": "<PERSON><PERSON>"}}, "lines": {"title": "Articoli", "description": "Descrizione", "schedule": "Piano di pagamento", "tax_rate": "Aliquota fiscale", "amount": "Importo", "one_off": "Una tantum", "tax_exempt": "<PERSON><PERSON>e da imposte"}, "total": {"title": "Totale", "total": "Totale", "sub_total": "Totale parziale", "tax_total": "Imposta Totale"}}}, "system": {"webhooks": {"webhook_endpoint": {"list": {"title": "Endpoint Webhook", "add": "Aggiu<PERSON>i endpoint", "view": "Vista", "list": {"name": "Nome", "url": "URL", "status": "Stato"}, "no_endpoints": "Al momento non ci sono endpoint webhook"}, "create": {"title": "Creare un endpoint webhook", "fields": {"name": "Nome", "url": "URL"}, "help_info": {"name": "Il nome dell'endpoint del webhook, per aiutarne l'identificazione in seguito", "url": "L'URL in cui devono essere inviati i payloads"}, "create_button": "<PERSON><PERSON><PERSON>"}, "view": {"title": "Visualizza il punto finale", "main": {"title": "Info", "name": "Nome", "url": "URL"}}}, "main": {"title": "Ganci web", "manage_endpoints": "Gestire gli endpoint", "list": {"type": "Tipo", "created_at": "<PERSON>reato a", "view_btn": "Visualizza i dati dell'evento", "loading": "Caricamento degli eventi Webhook", "no_events": "Non si sono verificati eventi webhook"}}, "event": {"view": {"title": "Informazioni sull'evento", "main": {"title": "Dati dell'evento", "type": "Tipo di evento", "payload": "Carico utile", "created_at": "<PERSON>reato a"}, "responses": {"title": "<PERSON><PERSON> di endpoint", "list": {"url": "URL", "status_code": "Codice di stato", "body": "Corpo", "error": "Errore", "view": "Vista", "created_at": "<PERSON>reato a"}}, "info": {"title": "Visualizza Richiedi informazioni", "error_message": "Messaggio di errore", "status_code": "Codice di stato", "body": "Corpo di risposta", "processing_time": "Tempo di elaborazione"}}}}, "integrations": {"list": {"title": "Integrazioni", "list": {"name": "Integrazione"}, "slack": {"name": "<PERSON><PERSON>ck", "button": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "slack": {"webhooks": {"list": {"title": "Webhook di Slack", "name": "Nome", "webhook": "Webhook", "disable_btn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enable_btn": "Abilitazione", "no_webhooks": "Non ci sono ancora webhook per slack", "next": "<PERSON><PERSON>", "prev": "Precedente", "error_message": "Impossibile recuperare i webhook di slack", "create_new": "<PERSON><PERSON><PERSON>"}, "create": {"title": "<PERSON><PERSON><PERSON> Slack", "fields": {"name": "Nome", "webhook": "URL del webhook"}, "help_info": {"name": "Il nome utilizzato per identificare questo webhook all'interno di BillaBear", "webhook": "L'URL fornito da Slack da utilizzare come webhook"}, "save_btn": "Risparmiare"}}, "notifications": {"list": {"title": "Notifica di Slack", "event": "Evento", "webhook": "Webhook", "disable_btn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "template": "<PERSON><PERSON>", "enable_btn": "Abilitazione", "no_notifications": "Non ci sono ancora notifiche su Slack", "next": "<PERSON><PERSON>", "prev": "Precedente", "error_message": "Impossibile recuperare le notifiche di Slack", "create_new": "<PERSON><PERSON><PERSON>"}, "create": {"title": "<PERSON><PERSON><PERSON> una notifica S<PERSON>ck", "fields": {"webhook": "Webhook", "event": "Evento", "template": "<PERSON><PERSON>"}, "help_info": {"event": "L'evento che deve attivare la notifica", "webhook": "Il webhook slack da utilizzare per la notifica", "template": "Il modello da utilizzare per l'invio della notifica. <a href=\"https://docs.billabear.com/user/integration/slack\" target=\"_blank\">Le variabili si trovano qui</a>"}, "save_btn": "Risparmiare"}}, "menu": {"title": "<PERSON><PERSON>ck", "webhooks": "Ganci web", "notification": "Notifiche"}}}, "menu": {"title": "Strumenti di sistema", "webhooks": "Ganci web", "integrations": "Integrazioni"}}, "checkout": {"create": {"title": "C<PERSON>re la cassa", "create_quote": "C<PERSON>re la cassa", "success_message": "Cassa creata", "errors": {"no_customer": "È necessario un cliente", "nothing_to_invoice": "È necessario aggiungere un abbonamento o un articolo unico.", "same_currency_and_schedule": "La stessa valuta e lo stesso calendario devono essere utilizzati per gli abbonamenti", "currency": "È richiesta una valuta", "need_description": "Necessità di una descrizione", "need_amount": "Quantità necessaria", "need_tax_type": "Serve un tipo di imposta"}, "customer": {"create_customer": "<PERSON>rea cliente", "fields": {"name": "Nome", "permanent": "Permanente", "customer": "Cliente", "currency": "Valuta", "slug": "Lumaca", "expires_at": "Scade a", "brand": "<PERSON><PERSON>"}, "help_info": {"permanent": "Se il checkout è permanente o una tantum", "name": "Il nome identificativo della cassa", "customer": "Il cliente per cui è stato effettuato il checkout", "currency": "La valuta da utilizzare per il checkout", "expires_at": "Quando il preventivo scade e non può essere pagato", "slug": "Lo slug per l'URL. Se si vuole che il checkout abbia un URL carino, utilizzare questo.", "brand": "Il marchio della cassa appartiene"}}, "subscriptions": {"title": "Abbonamenti", "add_new": "Aggiungi abbonamento", "list": {"subscription_plan": "Piano di abbonamento", "price": "Prezzo", "per_seat": "Per posto a sedere"}, "no_subscriptions": "<PERSON><PERSON><PERSON> a<PERSON>", "add_subscription": "Aggiungi abbonamento"}, "items": {"title": "Voci una tantum", "add_item": "Aggiungi una voce una tantum", "no_items": "Nessun articolo unico", "list": {"description": "Descrizione", "amount": "Importo", "tax_included": "<PERSON>ssa inclusa", "digital_product": "Prodotto digitale", "tax_type": "Tipo di imposta"}, "tax_types": {"digital_services": "Servizi digitali", "digital_goods": "Beni digitali", "physical": "<PERSON>i/servizi fisici"}}}, "view": {"title": "Visualizza la cassa", "checkout": {"title": "Informazioni sulla cassa", "created_by": "<PERSON><PERSON><PERSON> <PERSON>", "created_at": "<PERSON>reato a", "expires_at": "Scade a", "pay_link": "Collegamento a pagamento", "name": "Nome"}, "status": {"paid": "Preventivo pagato con successo alla {date}"}, "customer": {"title": "Cliente", "email": "Email", "more_info": "Per saperne di più", "address": {"company_name": "Nome della società", "street_line_one": "Linea stradale 1", "street_line_two": "Linea stradale 2", "city": "Città", "region": "Regione", "post_code": "Codice postale", "country": "<PERSON><PERSON>"}}, "lines": {"title": "Articoli", "description": "Descrizione", "schedule": "Piano di pagamento", "tax_rate": "Aliquota fiscale", "amount": "Importo", "one_off": "Una tantum", "tax_exempt": "<PERSON><PERSON>e da imposte"}, "total": {"title": "Totale", "total": "Totale", "sub_total": "Totale parziale", "tax_total": "Imposta Totale"}}, "list": {"title": "<PERSON><PERSON>", "email": "Email", "country": "<PERSON><PERSON>", "reference": "Riferimento", "no_checkouts": "Al momento non ci sono casse esistenti", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view_btn": "Vista", "list": {"name": "Nome", "created_at": "<PERSON>reato a", "view": "Vista"}, "filter": {"title": "<PERSON><PERSON><PERSON>", "name": "Nome", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro"}, "loading": "Risultati del caricamento", "error_message": "Si è verificato un errore"}}, "layout": {"topbar": {"menu": {"settings": "Impostazioni", "signout": "<PERSON><PERSON><PERSON>"}}}, "workflows": {"cancellation_request": {"list": {"title": "Richieste di cancellazione", "email": "Cliente", "status": "Stato", "plan": "Piano", "no_cancellation_requests": "Al momento non ci sono richieste di cancellazione", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view": "Vista", "edit_button": "Modifica", "bulk_button": "Ritrattamento alla rinfusa", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Ha un errore"}, "error_message": "Si è verificato un errore"}, "view": {"title": "Dettagli della richiesta di cancellazione", "subscription": {"title": "Dettagli sull'abbonamento", "name": "Nome del piano", "customer": "Cliente", "original_cancellation_date": "Data di cancellazione originale"}, "details": {"title": "Dettagli sulla cancellazione", "state": "Stato", "when": "Quando", "refund_type": "Tipo di rimborso", "specific_date": "Data di cancellazione"}, "error": {"title": "Errore"}, "buttons": {"process": "Processo di ritentativo"}}, "edit": {"title": "Modifica delle richieste di cancellazione", "add_place": "Aggiungi luogo", "add_place_modal": {"title": "Aggiungi luogo", "from_place": "Dal luogo", "to_place": "Da collocare", "name": "Nome", "event_handler": "Gestore dell'evento", "handler_options": "Opzioni del gestore", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": "<PERSON> richiesto"}, "edit_place_modal": {"title": "Modifica del luogo", "delete_button": "Cancellare il luogo", "enable_button": "Abilitazione", "disable_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "menu": {"title": "Strumenti per i flussi di lavoro", "cancellation_requests": "Richieste di cancellazione", "subscription_creation": "Creazione di abbonamenti", "payment_creation": "Creazione del pagamento", "refund_created_process": "Processo di creazione del rimborso", "payment_failure_process": "Processo di fallimento del pagamento", "charge_back_creation": "Creazione di charge back"}, "subscription_creation": {"list": {"title": "Creazione di abbonamenti", "email": "Cliente", "status": "Stato", "plan": "Piano", "no_cancellation_requests": "Al momento non ci sono Creazione di abbonamenti", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view": "Vista", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Ha un errore"}, "error_message": "Si è verificato un errore", "edit_button": "Modifica", "bulk_button": "Ritrattamento alla rinfusa"}, "view": {"title": "Dettagli sulla creazione dell'abbonamento", "subscription": {"title": "Dettagli sull'abbonamento", "name": "Nome del piano", "customer": "Cliente", "view": "Visualizza l'abbonamento"}, "details": {"title": "Dettagli sulla creazione", "state": "Stato"}, "error": {"title": "Errore"}, "buttons": {"process": "Processo di ritentativo"}}, "edit": {"title": "Modifica della creazione di abbonamenti", "add_place": "Aggiungi luogo", "add_place_modal": {"title": "Aggiungi luogo", "from_place": "Dal luogo", "to_place": "Da collocare", "name": "Nome", "event_handler": "Gestore dell'evento", "handler_options": "Opzioni del gestore", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": "<PERSON> richiesto"}}}, "payment_creation": {"list": {"title": "Creazione del pagamento", "email": "Cliente", "status": "Stato", "plan": "Piano", "no_results": "Al momento non ci sono risultati", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view": "Vista", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Ha un errore"}, "bulk_button": "Ritrattamento alla rinfusa", "error_message": "Si è verificato un errore", "edit_button": "Modifica"}, "view": {"title": "Dettagli sulla creazione del pagamento", "payment": {"title": "Dettagli sul pagamento", "name": "Nome del piano", "customer": "Cliente", "view": "Visualizza il pagamento"}, "details": {"title": "Dettagli sulla creazione", "state": "Stato"}, "error": {"title": "Errore"}, "buttons": {"process": "Processo di ritentativo"}}, "edit": {"title": "Modifica della creazione del pagamento", "add_place": "Aggiungi luogo", "add_place_modal": {"title": "Aggiungi luogo", "from_place": "Dal luogo", "to_place": "Da collocare", "name": "Nome", "event_handler": "Gestore dell'evento", "handler_options": "Opzioni del gestore", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": "<PERSON> richiesto"}}}, "refund_created_process": {"list": {"title": "<PERSON><PERSON><PERSON><PERSON> creato", "email": "Cliente", "status": "Stato", "plan": "Piano", "no_results": "Al momento non ci sono risultati", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view": "Vista", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Ha un errore"}, "error_message": "Si è verificato un errore", "edit_button": "Modifica", "bulk_button": "Ritrattamento alla rinfusa"}, "view": {"title": "Dettagli del processo di creazione del rimborso", "refund": {"title": "Dettagli sul rimborso", "name": "Nome del piano", "customer": "Cliente", "view": "Visualizza il rimborso"}, "details": {"title": "Dettagli sulla creazione", "state": "Stato"}, "error": {"title": "Errore"}, "buttons": {"process": "Processo di ritentativo"}}, "edit": {"title": "Modifica del processo creato per il rimborso", "add_place": "Aggiungi luogo", "add_place_modal": {"title": "Aggiungi luogo", "from_place": "Dal luogo", "to_place": "Da collocare", "name": "Nome", "event_handler": "Gestore dell'evento", "handler_options": "Opzioni del gestore", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": "<PERSON> richiesto"}, "edit_place_modal": {"title": "Modifica del luogo", "disable_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enable_button": "Abilitazione"}}}, "payment_failure_process": {"list": {"title": "Mancato pagamento", "email": "Cliente", "status": "Stato", "plan": "Piano", "no_results": "Al momento non ci sono risultati", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view": "Vista", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Ha un errore"}, "error_message": "Si è verificato un errore"}, "view": {"title": "Dettagli del processo di fallimento del pagamento", "payment": {"title": "Dettagli del tentativo di pagamento", "amount": "Importo", "customer": "Cliente", "view": "Visualizza fattura"}, "details": {"title": "Dettagli sulla creazione", "state": "Stato"}, "error": {"title": "Errore"}, "buttons": {"process": "Processo di ritentativo"}}}, "charge_back_creation": {"list": {"title": "Creazione di charge back", "email": "Cliente", "status": "Stato", "plan": "Piano", "no_results": "Al momento non ci sono risultati", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "view": "Vista", "filter": {"title": "<PERSON><PERSON><PERSON>", "email": "Email", "reference": "Riferimento", "external_reference": "Riferimento esterno", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "has_error": "Ha un errore"}, "error_message": "Si è verificato un errore", "edit_button": "Modifica", "bulk_button": "Ritrattamento alla rinfusa"}, "view": {"title": "Dettagli sulla creazione del charge back", "payment": {"title": "Dettagli sul pagamento", "name": "Nome del piano", "customer": "Cliente", "view": "Visualizza il pagamento"}, "details": {"title": "Dettagli sulla creazione", "state": "Stato"}, "error": {"title": "Errore"}, "buttons": {"process": "Processo di ritentativo"}}, "edit": {"title": "Modifica della creazione del chargeback", "add_place": "Aggiungi luogo", "add_place_modal": {"title": "Aggiungi luogo", "from_place": "Dal luogo", "to_place": "Da collocare", "name": "Nome", "event_handler": "Gestore dell'evento", "handler_options": "Opzioni del gestore", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": "<PERSON> richiesto"}}}}, "country": {"list": {"title": "<PERSON><PERSON>", "no_countries": "Attualmente non ci sono paesi", "create_new": "<PERSON><PERSON><PERSON>", "next": "Pagina successiva", "prev": "<PERSON><PERSON><PERSON>e", "list": {"name": "Nome", "iso_code": "Codice", "tax_threshold": "Soglia fiscale", "collecting": "Riscossione delle imposte"}, "view": "Vista", "filter": {"title": "<PERSON><PERSON><PERSON>", "name": "Nome", "code": "Codice", "button": "<PERSON><PERSON><PERSON>", "search": "Filtro", "collecting": "Riscossione delle imposte"}, "error_message": "Si è verificato un errore"}, "create": {"title": "<PERSON><PERSON>re un nuovo paese", "country": {"fields": {"name": "Nome", "iso_code": "<PERSON><PERSON>", "currency": "Valuta", "threshold": "Soglia", "in_eu": "<PERSON><PERSON><PERSON><PERSON>?", "tax_year": "Inizio dell'anno fiscale", "collecting": "Riscossione delle imposte", "tax_number": "Codice fiscale"}, "help_info": {"name": "Il nome del paese", "iso_code": "Il codice ISO del paese", "currency": "La valuta di riferimento per il paese", "threshold": "La soglia fiscale per il paese", "in_eu": "Il paese fa parte dell'UE", "tax_year": "La data di inizio dell'anno fiscale per il Paese", "collecting": "Se le tasse devono essere sempre raccolte per questo paese", "tax_number": "Il vostro codice fiscale per questo <PERSON>."}}, "create_button": "<PERSON><PERSON><PERSON>"}, "view": {"title": "<PERSON><PERSON><PERSON>ese", "fields": {"name": "Nome", "iso_code": "<PERSON><PERSON>", "threshold": "Soglia", "currency": "Valuta", "in_eu": "In Eu", "start_of_tax_year": "Inizio dell'anno fiscale", "enabled": "Abilitato", "collecting": "Riscossione delle imposte", "tax_number": "Codice fiscale", "transaction_threshold": "Soglia di transazione", "threshold_type": "Tipo di soglia"}, "edit_button": "Modifica", "tax_rule": {"title": "Regole fiscali", "rate": "Aliquota fiscale", "type": "Tipo di imposta", "default": "È predefinito", "start_date": "Data di inizio", "end_date": "Data di fine", "no_tax_rules": "Nessuna regola fiscale", "add": "Aggiungi regola fiscale", "edit": "Modifica"}, "add_tax_rule": {"tax_rate": "Aliquota fiscale", "tax_type": "Tipo di imposta", "valid_from": "<PERSON><PERSON>", "valid_until": "Valido fino a", "title": "Aggiungi regola fiscale", "default": "Regola fiscale predefinita", "save": "Risparmiare", "select_tax_type": "Selezionare il tipo di imposta"}, "edit_tax_rule": {"tax_rate": "Aliquota fiscale", "tax_type": "Tipo di imposta", "valid_from": "<PERSON><PERSON>", "valid_until": "Valido fino a", "title": "Modifica della regola fiscale", "default": "Regola fiscale predefinita", "save": "Aggiornamento", "select_tax_type": "Selezionare il tipo di imposta"}, "states": {"title": "Stati", "add": "Aggiungi nuovo Stato", "name": "Nome", "code": "Codice", "collecting": "<PERSON><PERSON><PERSON><PERSON><PERSON> le tasse?", "threshold": "Soglia", "view": "Vista", "no_states": "Non ci sono stati"}}, "edit": {"title": "Modifica Paese", "country": {"fields": {"name": "Nome", "iso_code": "<PERSON><PERSON>", "currency": "Valuta", "threshold": "Soglia", "in_eu": "<PERSON><PERSON><PERSON><PERSON>?", "tax_year": "Inizio dell'anno fiscale", "enabled": "Abilitato", "collecting": "Riscossione delle imposte", "tax_number": "Codice fiscale", "transaction_threshold": "Soglia di transazione", "threshold_type": "Tipo di soglia", "threshold_types": {"rolling": "Annuale", "calendar": "<PERSON><PERSON> solare", "rolling_quarterly": "Rotolamento per quarti", "rolling_accounting": "Ripartizione per anno contabile"}}, "help_info": {"name": "Il nome del paese", "iso_code": "Il codice ISO del paese", "currency": "La valuta di riferimento per il paese", "threshold": "La soglia fiscale per il paese", "in_eu": "Il paese fa parte dell'UE", "tax_year": "La data di inizio dell'anno fiscale per il Paese", "enabled": "Se il paese è abilitato per l'iscrizione dei clienti", "collecting": "Se le tasse devono essere sempre raccolte per questo paese", "tax_number": "Il vostro codice fiscale per questo <PERSON>.", "transaction_threshold": "Qual è la soglia di transazione per lo Stato", "threshold_type": "Come viene determinato il periodo di tempo per il calcolo della soglia"}}, "update_button": "Aggiornamento"}}, "tax_type": {"list": {"title": "Tipi di imposta", "create_new": "Crea nuovo", "error_message": "Si è verificato un errore", "list": {"name": "Nome", "make_default": "Rendere predefinito", "is_default": "È predefinito", "default": "Predefinito", "update": "Aggiornamento"}, "no_tax_types": "Attualmente non ci sono tipi di imposte"}, "create": {"title": "Creare il tipo di imposta", "tax_type": {"fields": {"name": "Nome", "vat_sense_type": "IVA Tipo di senso"}, "help_info": {"name": "Il nome della tassa", "vat_sense_type": "Il tipo di imposta nel sistema IVA Sense"}}, "create_button": "<PERSON><PERSON><PERSON>"}, "update": {"title": "Aggiornare il tipo di imposta", "tax_type": {"fields": {"name": "Nome", "vat_sense_type": "IVA Tipo di senso"}, "help_info": {"name": "Il nome della tassa", "vat_sense_type": "Il tipo di imposta nel sistema IVA Sense"}}, "update_button": "Aggiornamento"}}, "finance": {"integration": {"title": "Integrazioni", "fields": {"integration": "Integrazione", "api_key": "Chiave API", "enabled": "Abilitato"}, "buttons": {"connect": "Connettersi tramite OAuth", "disconnect": "Disconnessione", "save": "Risparmiare"}, "settings": {"title": "Impostazioni"}, "xero": {"account_id": "Codice conto per i pagamenti"}, "errors": {"required": "Questo campo è obbligatorio", "invalid": "Questo campo non è valido", "complete_error": "Si è verificato un errore durante il tentativo di salvare queste impostazioni. Riprovare."}}, "menu": {"integration": "Integrazione"}}, "tax": [], "state": {"view": {"title": "Visualizza lo Stato", "edit": "Modifica", "fields": {"name": "Nome", "code": "Codice", "threshold": "Soglia", "collecting": "Raccolta", "transaction_threshold": "Soglia di transazione", "threshold_type": "Tipo di soglia"}, "tax_rule": {"title": "Regole fiscali", "rate": "Aliquota fiscale", "type": "Tipo di imposta", "default": "È predefinito", "start_date": "Data di inizio", "end_date": "Data di fine", "no_tax_rules": "Nessuna regola fiscale", "add": "Aggiungi regola fiscale", "edit": "Modifica"}, "add_tax_rule": {"tax_rate": "Aliquota fiscale", "tax_type": "Tipo di imposta", "valid_from": "<PERSON><PERSON>", "valid_until": "Valido fino a", "title": "Aggiungi regola fiscale", "default": "Regola fiscale predefinita", "save": "Risparmiare", "select_tax_type": "Selezionare il tipo di imposta"}, "edit_tax_rule": {"tax_rate": "Aliquota fiscale", "tax_type": "Tipo di imposta", "valid_from": "<PERSON><PERSON>", "valid_until": "Valido fino a", "title": "Modifica della regola fiscale", "default": "Regola fiscale predefinita", "save": "Aggiornamento", "select_tax_type": "Selezionare il tipo di imposta"}}, "create": {"title": "Creare un nuovo Stato", "state": {"fields": {"name": "Nome", "code": "Codice", "collecting": "Raccolta", "threshold": "Soglia"}, "help_info": {"name": "Il nome dello Stato", "code": "Il codice che viene spesso utilizzato come abbreviazione dello stato", "collecting": "Se raccogliamo sempre le tasse per lo Stato", "threshold": "Quale soglia economica per lo Stato"}}, "create_button": "<PERSON><PERSON><PERSON>"}, "edit": {"title": "Modifica Stato", "state": {"fields": {"name": "Nome", "code": "Codice", "collecting": "Raccolta", "threshold": "Soglia", "transaction_threshold": "Soglia di transazione", "threshold_type": "Tipo di soglia", "threshold_types": {"rolling": "Annuale", "calendar": "<PERSON><PERSON> solare", "rolling_quarterly": "Rotolamento per quarti", "rolling_accounting": "Ripartizione per anno contabile"}}, "help_info": {"name": "Il nome dello Stato", "code": "Il codice che viene spesso utilizzato come abbreviazione dello stato", "collecting": "Se raccogliamo sempre le tasse per lo Stato", "threshold": "Quale soglia economica per lo Stato", "transaction_threshold": "Qual è la soglia di transazione per lo Stato", "threshold_type": "Come viene determinato il periodo di tempo per il calcolo della soglia"}}, "update_button": "Aggiornamento"}}, "onboarding": {"main": {"bar": {"message": "Stripe deve essere configurato prima di poter utilizzare BillaBear"}, "dialog": {"title": "Inserimento", "has_stripe_key": {"text": "Inserire chiavi API Stripe valide", "button": "Entrare qui"}, "has_stripe_imports": {"text": "Importare i dati da Stripe", "button": "Importazione", "dismiss": "<PERSON><PERSON><PERSON>"}, "has_product": {"text": "<PERSON><PERSON><PERSON> il primo prodotto", "button": "<PERSON><PERSON> prodotto"}, "has_subscription_plan": {"text": "<PERSON><PERSON>re il primo piano di abbonamento", "button": "<PERSON><PERSON><PERSON>"}, "has_customer": {"text": "Creare il primo cliente", "button": "<PERSON><PERSON><PERSON>"}, "has_subscription": {"text": "Creare la prima sottoscrizione", "button": "<PERSON><PERSON><PERSON>"}}, "error": "Qualcosa è andato storto!"}}, "default_error_message": "Qualcosa è andato storto!", "metric": {"list": {"title": "Metric<PERSON>", "create": "<PERSON><PERSON><PERSON>", "name": "Nome", "no_metrics": "Non ci sono ancora metriche!", "filter": {"name": "Nome"}, "view_btn": "Vista"}, "create": {"title": "Crea metrica", "fields": {"name": "Nome", "code": "Codice", "type": "Tipo", "aggregation_method": "Metodo di aggregazione", "aggregation_property": "Proprietà di aggregazione", "ingestion": "Ingestione", "filters": "<PERSON><PERSON><PERSON>"}, "help_info": {"name": "Il nome della metrica", "code": "Il codice da utilizzare nelle chiamate api. Solo lettere minuscole, numeri e trattini bassi.", "type": "Se il contatore del cliente deve essere azzerato alla fine di un periodo di abbonamento", "aggregation_method": "Come devono essere aggregati gli eventi inviati a BillaBear.", "aggregation_property": "Quale proprietà dei dati dell'evento deve essere utilizzata per l'aggregazione.", "ingestion": "Con quale frequenza devono essere elaborati gli eventi", "filters": "I filtri che devono essere applicati al payload dell'evento per essere esclusi nell'aggregazione"}, "aggregation_methods": {"count": "Conteggio", "sum": "Somma", "latest": "Ultime novità", "unique_count": "Conteggio unico", "max": "<PERSON><PERSON>"}, "ingestion": {"real_time": "Tempo reale", "hourly": "Orario", "daily": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "filter": {"name": "Nome", "value": "Valore", "type": "Tipo", "no_filters": "<PERSON><PERSON><PERSON> filtro"}, "filter_type": {"inclusive": "Inclusivo", "exclusive": "Esclusivo"}, "create_button": "<PERSON><PERSON><PERSON>"}, "view": {"title": "Vista metrica", "main": {"name": "Nome", "code": "Codice", "type": "Tipo", "aggregation_method": "Metodo di aggregazione", "aggregation_property": "Proprietà di aggregazione", "event_ingestion": "Ingestione"}, "filters": {"title": "<PERSON><PERSON><PERSON>", "name": "Nome", "value": "Valore", "type": "Tipo", "inclusive": "Inclusivo", "exclusive": "Esclusivo"}, "update": "Aggiornamento"}, "update": {"title": "Aggiornamento della metrica", "update_button": "Risparmiare"}}, "usage_limit": {"create": {"title": "Creare un limite di utilizzo", "fields": {"amount": "Importo", "action": "Azione"}, "help_info": {"amount": "L'importo che si desidera limitare al cliente prima che venga intrapresa un'azione.", "action": "L'azione che deve essere eseguita quando il limite viene superato."}, "actions": {"warn": "Avvertire", "disable": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "submit": "<PERSON><PERSON><PERSON>"}}, "customer_support": {"integration": {"title": "Integrazioni dell'assistenza clienti", "fields": {"integration": "Integrazione", "api_key": "Chiave API", "enabled": "Abilitato"}, "buttons": {"connect": "Connettersi tramite OAuth", "disconnect": "Disconnessione", "save": "Risparmiare"}, "settings": {"title": "Impostazioni"}, "errors": {"required": "Questo campo è obbligatorio", "invalid": "Questo campo non è valido", "complete_error": "Si è verificato un errore durante il tentativo di salvare queste impostazioni. Riprovare."}, "zendesk": {"token": "Gettone", "subdomain": "Sottodominio", "username": "Nome utente"}, "freshdesk": {"subdomain": "Sottodominio", "api_key": "Chiave API"}}}, "integrations": {"newsletter": {"title": "Integrazione della newsletter", "fields": {"marketing_list": "Elenco di marketing", "announcement_list": "<PERSON><PERSON><PERSON> annunci"}, "no_lists": "Non ci sono elenchi disponibili. Inserire prima i dettagli della connessione.", "errors": {"list_required": "L'abilitazione è possibile solo dopo aver selezionato un elenco. Immettere i dettagli della connessione e salvare, quindi scegliere un elenco."}, "mailchimp": {"fields": {"server_prefix": "Prefisso del server"}}}, "menu": {"main": "Integrazioni", "accounting": "Contabilità", "customer_support": "Assistenza clienti", "newsletter": "Newsletter", "notifications": "Notifiche", "crm": "CRM"}, "general": {"fields": {"integration": "Integrazione", "api_key": "Chiave API", "enabled": "Abilitato"}, "buttons": {"connect": "Connettersi tramite OAuth", "disconnect": "Disconnessione", "save": "Risparmiare"}, "settings": {"title": "Impostazioni"}, "errors": {"required": "Questo campo è obbligatorio", "invalid": "Questo campo non è valido", "complete_error": "Si è verificato un errore durante il tentativo di salvare queste impostazioni. Riprovare."}}, "crm": {"title": "Integrazioni CRM", "fields": {"integration": "Integrazione"}, "buttons": {"connect": "Connettersi tramite Oauth", "disconnect": "Disconnessione", "save": "Risparmiare"}}}, "compliance": {"audit": {"all": {"title": "Registro di controllo", "log": "Log", "date": "Data", "billing_admin": "Amministratore della fatturazione connesso", "no_billing_admin": "Questo non è stato fatto da un amministratore della fatturazione", "display_name": "Nome visualiz<PERSON>", "context": "Contesto del registro", "no_logs": "<PERSON><PERSON>un registro trovato"}, "customer": {"title": "Registro di controllo clienti - {nome}"}, "billing_admin": {"title": "Registro di controllo dell'amministratore della fatturazione - {nome}"}}}}, "install": {"title": "Installare", "submit_button": "Installare", "user": {"title": "Primo utente amministratore", "email": "Email", "password": "Password"}, "settings": {"title": "Impostazioni di sistema", "default_brand": "Nome del marchio predefinito", "from_email": "Indirizzo e-mail predefinito di provenienza", "timezone": "<PERSON><PERSON> orario", "webhook_url": "URL di base", "currency": "Valuta", "country": "<PERSON><PERSON>"}, "complete_text": "BillaBear è stato installato! Ora è possibile effettuare il login utilizzando i dati forniti.", "login_link": "Fare clic qui per accedere", "unknown_error": "<PERSON><PERSON><PERSON> sconos<PERSON>.", "stripe": {"no_api_key": "È necessario fornire una chiave API di Stripe nella variabile ENV STRIPE_PRIVATE_API_KEY.", "doc_link": "Ulteriori informazioni su come configurare BillaBear.", "invalid_api_key": "La chiave API di Stripe non è valida", "support_link": "Potete chiedere aiuto qui."}}}