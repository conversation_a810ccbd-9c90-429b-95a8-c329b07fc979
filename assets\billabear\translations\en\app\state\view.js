export const STATE_VIEW_TRANSLATIONS = {
    title: "View State",
    edit: "Edit",
    fields: {
        name: "Name",
        code: "Code",
        threshold: "Threshold",
        collecting: "Collecting",
        transaction_threshold: "Transaction Threshold",
        threshold_type: "Threshold Type",
    },
    tax_rule: {
        title: "Tax Rules",
        rate: "Tax Rate",
        type: "Tax Type",
        default: "Is Default",
        start_date: "Start Date",
        end_date: "End Date",
        no_tax_rules: "No Tax Rules",
        add: "Add Tax Rule",
        edit: "Edit",
    },
    add_tax_rule: {
        tax_rate: "Tax Rate",
        tax_type: "Tax Type",
        valid_from: "Valid From",
        valid_until: "Valid Until",
        title: "Add Tax Rule",
        default: "Default tax rule",
        save: "Save",
        select_tax_type: "Select Tax Type"
    },
    edit_tax_rule: {
        tax_rate: "Tax Rate",
        tax_type: "Tax Type",
        valid_from: "Valid From",
        valid_until: "Valid Until",
        title: "Edit Tax Rule",
        default: "Default tax rule",
        save: "Update",
        select_tax_type: "Select Tax Type"
    },
}
